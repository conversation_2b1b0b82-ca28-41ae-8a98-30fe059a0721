<!DOCTYPE html>
<html>

<head>
  <base href="$FLUTTER_BASE_HREF">
  <meta charset="UTF-8">
  <meta content="IE=Edge" http-equiv="X-UA-Compatible">
  <meta name="description" content="A new Flutter project.">
  <meta name="flutter-web-renderer" content="html">
  <!-- iOS meta tags & icons -->
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">
  <meta name="apple-mobile-web-app-title" content="excel_app">
  <link rel="apple-touch-icon" href="icons/Icon-192.png">

  <!-- Google Sign-in -->
  <meta name="google-signin-client_id"
    content="189107451398-iamjnn70o6srhrhuq9kabo306fenmpqn.apps.googleusercontent.com">
  <!-- Add authorized domains for redirect -->
  <meta name="google-signin-hosted-domain" content="flexical-app.kodecreators.com">

  <!-- Favicon -->
  <link rel="icon" type="image/png" href="favicon.ico" />

  <title>ikPlanner</title>
  <link rel="manifest" href="manifest.json">
  <script src="https://www.gstatic.com/firebasejs/9.6.10/firebase-app.js"></script>
  <!-- Firebase Auth -->
  <script src="https://www.gstatic.com/firebasejs/9.6.10/firebase-auth.js"></script>
  <!-- Firebase Messaging -->
  <script src="https://www.gstatic.com/firebasejs/9.6.10/firebase-messaging.js"></script>

  <!-- Safari Notifications Helper -->
  <script src="safari_notifications.js"></script>
  <!-- Firebase Auth Helper -->
  <script src="firebase-auth.js"></script>

  <script>
    // Your Firebase configuration
    const firebaseConfig = {
      apiKey: "AIzaSyBBCakXKahb6kdAaoeBl4rN0F1-GYBLc1I",
      authDomain: "flexical-4075e.firebaseapp.com",
      projectId: "flexical-4075e",
      storageBucket: "flexical-4075e.firebasestorage.app",
      messagingSenderId: "189107451398",
      appId: "1:189107451398:web:2c926948d9af914e767587"
    };

    // Initialize Firebase
    firebase.initializeApp(firebaseConfig);
  </script>
</head>

<body>
  <script src="flutter_bootstrap.js" async></script>
  <script>
    if ('serviceWorker' in navigator) {
      window.addEventListener('load', function () {
        // Check if this is Safari on Mac
        const isSafari = navigator.userAgent.toLowerCase().includes('safari') &&
          !navigator.userAgent.toLowerCase().includes('chrome');
        const isMacOS = navigator.userAgent.toLowerCase().includes('macintosh') ||
          navigator.userAgent.toLowerCase().includes('mac os');

        // For Safari on Mac, we'll handle notifications differently
        if (isSafari && isMacOS) {
          console.log('Safari on Mac detected, using custom notification handling');
          // Initialize Safari notifications
          if (window.initSafariNotifications) {
            window.initSafariNotifications();
          }
        } else {
          // For other browsers, register the service worker
          navigator.serviceWorker.register('firebase-messaging-sw.js')
            .then(function (registration) {
              console.log('Service Worker registered with scope:', registration.scope);
            })
            .catch(function (error) {
              console.error('Service Worker registration failed:', error);
            });
        }
      });
    }
  </script>
  <!-- <script type="module">
    import { initializeApp } from "https://www.gstatic.com/firebasejs/11.0.1/firebase-app.js";
    import { getAnalytics } from "https://www.gstatic.com/firebasejs/11.0.1/firebase-analytics.js";
    const firebaseConfig = {
      apiKey: 'AIzaSyBBCakXKahb6kdAaoeBl4rN0F1-GYBLc1I',
      authDomain: 'flexical-4075e.firebaseapp.com',
      projectId: 'flexical-4075e',
      storageBucket: 'flexical-4075e.firebasestorage.app',
      messagingSenderId: '189107451398',
      appId: '1:189107451398:web:2c926948d9af914e767587',
      measurementId: 'G-YJ9QFK7KB2',
    };

    // Initialize Firebase
    const app = initializeApp(firebaseConfig);
    const analytics = getAnalytics(app);
  </script> -->
</body>

</html>