// web/firebase-messaging-sw.js
// This service worker handles notifications for all browsers except Safari on Mac

// Check if importScripts is available (not available in Safari on Mac)
try {
    importScripts('https://www.gstatic.com/firebasejs/9.22.0/firebase-app-compat.js');
    importScripts('https://www.gstatic.com/firebasejs/9.22.0/firebase-messaging-compat.js');
} catch (e) {
    console.error('Error importing Firebase scripts:', e);
}

// Initialize Firebase only if the scripts were loaded successfully
if (typeof firebase !== 'undefined') {
    // Firebase configuration
    firebase.initializeApp({
        apiKey: 'AIzaSyBBCakXKahb6kdAaoeBl4rN0F1-GYBLc1I',
        authDomain: 'flexical-4075e.firebaseapp.com',
        projectId: 'flexical-4075e',
        storageBucket: 'flexical-4075e.firebasestorage.app',
        messagingSenderId: '189107451398',
        appId: '1:189107451398:web:2c926948d9af914e767587',
        measurementId: 'G-YJ9QFK7KB2',
    });

    const messaging = firebase.messaging();

    // Log successful initialization
    console.log('[firebase-messaging-sw.js] Firebase Messaging initialized successfully');

    // Handle background messages
    try {
        messaging.onBackgroundMessage(function (payload) {
            console.log('[firebase-messaging-sw.js] Received background message ', payload);

            try {
                // Check if notification payload exists
                if (payload.notification) {
                    const notificationTitle = payload.notification.title || 'Notification';
                    const notificationOptions = {
                        body: payload.notification.body || '',
                        icon: '/icons/Icon-192.png',
                        data: payload.data || {}
                    };

                    // Show notification
                    return self.registration.showNotification(notificationTitle, notificationOptions);
                }
            } catch (error) {
                console.error('Error showing notification:', error);
            }
        });
    } catch (error) {
        console.error('[firebase-messaging-sw.js] Error setting up background message handler:', error);
    }
}

// Notification click event listener
self.addEventListener('notificationclick', function (event) {
    console.log('[firebase-messaging-sw.js] Notification click event', event);

    event.notification.close();

    // Handle click action
    const clickAction = event.notification.data && event.notification.data.click_action ?
        event.notification.data.click_action :
        '/';

    // This looks to see if the current window is already open and focuses if it is
    event.waitUntil(
        clients.matchAll({ type: 'window', includeUncontrolled: true })
            .then(function (clientList) {
                // Try to find an existing window
                for (let i = 0; i < clientList.length; i++) {
                    const client = clientList[i];
                    if (client.url === clickAction && 'focus' in client) {
                        return client.focus();
                    }
                }

                // If no existing window, open a new one
                if (clients.openWindow) {
                    return clients.openWindow(clickAction);
                }
            })
            .catch(function (error) {
                console.error('Error handling notification click:', error);
            })
    );
});