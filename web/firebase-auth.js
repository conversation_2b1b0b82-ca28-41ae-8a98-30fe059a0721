// Firebase Auth helper for web
window.configureFirebaseAuth = function() {
  // Your Firebase configuration
  const firebaseConfig = {
    apiKey: "AIzaSyBBCakXKahb6kdAaoeBl4rN0F1-GYBLc1I",
    authDomain: "flexical-4075e.firebaseapp.com",
    projectId: "flexical-4075e",
    storageBucket: "flexical-4075e.firebasestorage.app",
    messagingSenderId: "189107451398",
    appId: "1:189107451398:web:2c926948d9af914e767587"
  };

  // Initialize Firebase if not already initialized
  if (!firebase.apps || !firebase.apps.length) {
    firebase.initializeApp(firebaseConfig);
  }

  // Configure auth persistence
  firebase.auth().setPersistence(firebase.auth.Auth.Persistence.LOCAL)
    .catch((error) => {
      console.error("Auth persistence error:", error);
    });
    
  // Return the auth instance
  return firebase.auth();
};

// Helper for Google Sign-In
window.signInWithGoogle = function() {
  const auth = window.configureFirebaseAuth();
  const provider = new firebase.auth.GoogleAuthProvider();
  
  // Add scopes
  provider.addScope('email');
  provider.addScope('https://www.googleapis.com/auth/userinfo.profile');
  
  // Set custom parameters
  provider.setCustomParameters({
    'login_hint': '<EMAIL>',
    'prompt': 'select_account'
  });
  
  // Sign in with redirect (better for mobile)
  return auth.signInWithRedirect(provider);
};
