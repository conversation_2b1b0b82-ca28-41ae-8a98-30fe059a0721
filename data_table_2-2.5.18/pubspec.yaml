name: data_table_2
description: In-place substitute for <PERSON><PERSON><PERSON>'s DataTable and PaginatedDataTable with fixed/sticky headers and few extra features
version: 2.5.18
homepage: https://github.com/maxim-saplin/data_table_2
repository: https://github.com/maxim-saplin/data_table_2

environment:
  sdk: ">=3.0.0 <4.0.0"
  flutter: ">=3.10.0"

dependencies:
  flutter: 
    sdk: flutter
  async: ^2.10.0


dev_dependencies:
  flutter_test:
    sdk: flutter
  vector_math: ^2.1.0
  intl: ^0.20.0
  flutter_lints: ^5.0.0
  golden_toolkit: # Solving problem with not rendered fonts

screenshots:
  - description: 'DataTable2 with fixed top row and left column and customized styling'
    path: screenshots/fixed_row_col.png
