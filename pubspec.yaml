name: excel_app
description: "A new Flutter project."
publish_to: "none"

version: 1.0.0+1

environment:
  sdk: ">=3.4.3 <4.0.0"

dependencies:
  animate_do: ^3.3.4
  bloc: ^8.1.4
  bot_toast: ^4.1.3
  cached_network_image: ^3.4.1
  collection: ^1.18.0
  cupertino_icons: ^1.0.6
  data_table_2:
    path: ./data_table_2-2.5.18
  easy_debounce: ^2.0.3
  encrypt: ^5.0.3
  equatable: ^2.0.7
  file_picker: ^8.1.5
  file_saver: ^0.2.14
  firebase_core: ^3.13.0
  cloud_firestore: ^5.4.4
  firebase_ui_firestore: ^1.7.1
  flutter:
    sdk: flutter
  flutter_bloc: ^8.1.6
  flutter_colorpicker: ^1.1.0
  flutter_image_compress: ^2.3.0
  flutter_local_notifications: ^18.0.1
  flutter_localizations:
    sdk: flutter
  flutter_staggered_grid_view: ^0.7.0
  flutter_svg: ^2.0.10+1
  fluttertoast: ^8.2.8
  font_awesome_flutter: ^10.7.0
  fpdart: ^1.1.1
  gap: ^3.0.1
  get_it: ^8.0.3
  go_router: ^14.3.0
  html: ^0.15.5
  http: ^1.2.2
  http_parser: ^4.1.1
  image_picker: ^1.1.2
  injectable: ^2.5.0
  intl: ^0.19.0
  mime: ^2.0.0
  month_picker_dialog: ^5.1.3
  path: ^1.9.0
  pdf: ^3.11.2
  responsive_builder: ^0.7.1
  rxdart: ^0.28.0
  shared_preferences: ^2.3.4
  skeletonizer: ^1.4.2
  syncfusion_flutter_calendar: ^27.2.4
  talker_logger: ^4.5.3
  toastification: ^3.0.1
  url_launcher: ^6.3.1
  url_launcher_web: ^2.4.0
  visibility_detector: ^0.4.0+2
  firebase_messaging: ^15.2.5
  google_sign_in: ^6.3.0
  firebase_auth: ^5.5.3

dev_dependencies:
  build_runner: ^2.4.13
  flutter_lints: ^3.0.0
  injectable_generator: ^2.6.2
  very_good_analysis: ^7.0.0

flutter:
  uses-material-design: true
  generate: true
  assets:
    - assets/images/
    - assets/svg/
    - assets/gif/

  fonts:
    - family: SFProDisplay
      fonts:
        - asset: fonts/SFProDisplay-Regular.ttf
          weight: 400
        - asset: fonts/SFProDisplay-Medium.ttf
          weight: 500
        - asset: fonts/SFProDisplay-Semibold.ttf
          weight: 600
        - asset: fonts/SFProDisplay-Bold.ttf
          weight: 700
        - asset: fonts/SFProDisplay-Heavy.ttf
          weight: 900
