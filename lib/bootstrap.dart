import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:excel_app/app/observer/app_bloc_observer.dart';
import 'package:excel_app/injector/injector.dart';
import 'package:excel_app/utility/firebase_messaging_services.dart';
import 'package:excel_app/utility/helpers/logger_config.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';

Future<void> bootstrap(Widget builder) async {
  Zone.current.fork().runGuarded(() async {
    WidgetsFlutterBinding.ensureInitialized();

    Bloc.observer = AppBlocObserver();

    await FirebaseMessagingService().initializeMain();

    await configureDependencies();

    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.dark,
        statusBarBrightness: Brightness.dark,
      ),
    );
    GoRouter.optionURLReflectsImperativeAPIs = true;

    runApp(builder);

    FlutterError.onError = (details) {
      debugError('${details.exceptionAsString()}\n${details.stack}');
    };
  });
}
