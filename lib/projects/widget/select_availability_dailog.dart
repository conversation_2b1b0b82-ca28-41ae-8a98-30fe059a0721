import 'package:excel_app/constants/app_assets.dart';
import 'package:excel_app/holiday_category/model/holiday_catergory_model.dart';
import 'package:excel_app/widget/app_asset_image.dart';
import 'package:excel_app/widget/custome_button_gradiant_widget.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';

class SelectAvailabilityCategoryDialog extends StatefulWidget {
  const SelectAvailabilityCategoryDialog({
    super.key,
    this.listData = const <HolidayCategoryModel>[],
    this.title,
    this.selectedlistData = const <HolidayCategoryModel>[],
  });
  final List<HolidayCategoryModel>? listData;
  final String? title;
  final List<HolidayCategoryModel>? selectedlistData;
  @override
  State<SelectAvailabilityCategoryDialog> createState() => _SelectAvailabilityCategoryDialogState();
}

class _SelectAvailabilityCategoryDialogState extends State<SelectAvailabilityCategoryDialog> {
  final selectedValues = ValueNotifier<List<HolidayCategoryModel>>([]);

  @override
  void initState() {
    selectedValues.value = widget.selectedlistData ?? [];
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return StatefulBuilder(
      builder: (context, setUpdate) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          child: SizedBox(
            width: 390,
            child: Padding(
              padding: const EdgeInsets.fromLTRB(20, 20, 20, 30),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    widget.title ?? '',
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                    textAlign: TextAlign.center,
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                        ),
                  ),
                  const Gap(20),
                  ValueListenableBuilder<List<HolidayCategoryModel>>(
                    valueListenable: selectedValues,
                    builder: (context, selected, _) {
                      return Flexible(
                        child: ConstrainedBox(
                          constraints: const BoxConstraints(
                            maxHeight: 290,
                          ),
                          child: ListView.builder(
                            shrinkWrap: true,
                            itemBuilder: (context, index) {
                              return InkWell(
                                onTap: () {
                                  setUpdate(
                                    () {
                                      if (selected.any((element) => element.id == widget.listData?[index].id)) {
                                        selectedValues.value = [...selected]
                                          ..removeWhere((element) => element.id == widget.listData?[index].id);
                                      } else {
                                        selectedValues.value = [
                                          ...selected,
                                          widget.listData?[index].copyWith(
                                                controller: TextEditingController(),
                                              ) ??
                                              HolidayCategoryModel(
                                                controller: TextEditingController(),
                                              ),
                                        ];
                                      }
                                    },
                                  );
                                },
                                child: Padding(
                                  padding: const EdgeInsets.symmetric(vertical: 12),
                                  child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      AppAssetImage(
                                        selected.any((element) => element.id == widget.listData?[index].id)
                                            ? AppAssets.selectedCheckboxIcon
                                            : AppAssets.unselectedCheckboxIcon,
                                      ),
                                      const Gap(14),
                                      Expanded(
                                        child: Text(
                                          widget.listData?[index].name ?? '',
                                          overflow: TextOverflow.ellipsis,
                                          style: Theme.of(context).textTheme.bodyMedium,
                                        ),
                                      ),
                                      const Gap(6),
                                    ],
                                  ),
                                ),
                              );
                            },
                            itemCount: widget.listData?.length,
                          ),
                        ),
                      );
                    },
                  ),
                  const Gap(18),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      CustomeButtonGradiantWidget(
                        buttonText: 'Cancel',
                        isUseContainerBorder: true,
                        width: 100,
                        height: 38,
                        onTap: () {
                          selectedValues.value = widget.selectedlistData ?? [];
                          Navigator.pop(context, selectedValues.value);
                        },
                      ),
                      const Gap(15),
                      CustomeButtonGradiantWidget(
                        buttonText: 'Add',
                        isGradient: true,
                        width: 100,
                        height: 38,
                        onTap: () {
                          Navigator.pop(context, selectedValues.value);
                        },
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}

class SampleUserModel {
  SampleUserModel({this.userType, this.id, this.name});
  final int? id;
  final String? name;
  final String? userType;
}
