import 'package:excel_app/constants/app_assets.dart';
import 'package:excel_app/holiday_category/model/holiday_catergory_model.dart';
import 'package:excel_app/projects/model/shift_configuration_model.dart';
import 'package:excel_app/utility/extentions/string_extentions.dart';
import 'package:excel_app/widget/app_asset_image.dart';
import 'package:excel_app/widget/app_text_form_field.dart';
import 'package:excel_app/widget/container_widget.dart';
import 'package:excel_app/widget/radio_checkbox_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:gap/gap.dart';

class ShiftConfigurationWidget extends StatefulWidget {
  const ShiftConfigurationWidget({
    super.key,
    this.selectAvailabilityCategoryOnTap,
    this.availabilityCategories,
    this.canYouProhibitedDaysOnTap,
    this.shiftConfigurationList,
  });

  final List<HolidayCategoryModel>? availabilityCategories;
  final void Function()? selectAvailabilityCategoryOnTap;
  final void Function(int?)? canYouProhibitedDaysOnTap;
  final ShiftConfigurationModel? shiftConfigurationList;
  @override
  State<ShiftConfigurationWidget> createState() => _ShiftConfigurationWidgetState();
}

class _ShiftConfigurationWidgetState extends State<ShiftConfigurationWidget> {
  @override
  Widget build(BuildContext context) {
    return ContainerWidget(
      margin: const EdgeInsets.only(bottom: 25, left: 25, right: 25),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '${widget.shiftConfigurationList?.userType} Shift Configuration'.capitalizeFirstofEach,
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  fontSize: 16,
                ),
          ),
          const Gap(20),
          Wrap(
            spacing: 20,
            runSpacing: 20,
            children: [
              AppTextFormField(
                controller: widget.shiftConfigurationList?.maximumPerShiftController ?? TextEditingController(),
                title: 'Maximum ${widget.shiftConfigurationList?.userType} per shift*'.inCaps,
                maxWidth: 300,
                keyboardType: TextInputType.number,
                inputFormatters: [
                  FilteringTextInputFormatter.digitsOnly,
                  LengthLimitingTextInputFormatter(4),
                  FilteringTextInputFormatter.allow(RegExp('^[1-9][0-9]*')),
                ],
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Please enter maximum ${widget.shiftConfigurationList?.userType} per shift'.inCaps;
                  }
                  if (int.parse(value) <
                      int.parse(widget.shiftConfigurationList?.minimumPerShiftController.text ?? '0')) {
                    return 'Maximum  per shift should be greater than minimum  per shift'.inCaps;
                  }
                  return null;
                },
              ),
              AppTextFormField(
                controller: widget.shiftConfigurationList?.minimumPerShiftController ?? TextEditingController(),
                title: 'Minimum ${widget.shiftConfigurationList?.userType} per shift*'.inCaps,
                keyboardType: TextInputType.number,
                inputFormatters: [
                  FilteringTextInputFormatter.digitsOnly,
                  LengthLimitingTextInputFormatter(4),
                ],
                maxWidth: 300,
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Please enter minimum ${widget.shiftConfigurationList?.userType} per shift'.inCaps;
                  }
                  return null;
                },
              ),
              AppTextFormField(
                controller: widget.shiftConfigurationList?.preferredUserPerShiftController ?? TextEditingController(),
                title: 'Preferred ${widget.shiftConfigurationList?.userType} per shift*'.inCaps,
                keyboardType: TextInputType.number,
                inputFormatters: [
                  FilteringTextInputFormatter.digitsOnly,
                  LengthLimitingTextInputFormatter(4),
                ],
                maxWidth: 300,
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Please enter preferred ${widget.shiftConfigurationList?.userType} per shift'.inCaps;
                  }
                  return null;
                },
              ),
              AppTextFormField(
                controller: TextEditingController(text: widget.availabilityCategories?.map((e) => e.name).join(',')),
                title: 'Select availability category for ${widget.shiftConfigurationList?.userType}*'.inCaps,
                maxWidth: 300,
                readOnly: true,
                suffixIcon: const Padding(
                  padding: EdgeInsets.all(10),
                  child: AppAssetImage(
                    AppAssets.arrowDownIcon,
                  ),
                ),
                onTap: widget.selectAvailabilityCategoryOnTap,
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Please select availability category for ${widget.shiftConfigurationList?.userType}'.inCaps;
                  }
                  return null;
                },
              ),
            ],
          ),
          if (widget.availabilityCategories != null && widget.availabilityCategories!.isNotEmpty) const Gap(20),
          Wrap(
            spacing: 20,
            runSpacing: 20,
            children: List.generate(
              widget.availabilityCategories!.length,
              (index) {
                return AppTextFormField(
                  controller: widget.availabilityCategories?[index].controller ?? TextEditingController(),
                  title: '${widget.availabilityCategories?[index].name?.inCaps} per month can take?*',
                  maxWidth: 300,
                  keyboardType: TextInputType.number,
                  inputFormatters: [
                    LengthLimitingTextInputFormatter(4),
                    FilteringTextInputFormatter.digitsOnly,
                  ],
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'Please enter ${widget.availabilityCategories?[index].name?.inCaps} per month can take?';
                    }
                    return null;
                  },
                );
              },
            ),
          ),
          const Gap(20),
          Wrap(
            spacing: 20,
            runSpacing: 20,
            crossAxisAlignment: WrapCrossAlignment.center,
            children: [
              AppTextFormField(
                controller:
                    widget.shiftConfigurationList?.avoidShiftDayaBeforeOrAfterController ?? TextEditingController(),
                title: 'Avoid shifts (B) days before or after another shift*',
                maxWidth: 300,
                keyboardType: TextInputType.number,
                inputFormatters: [
                  FilteringTextInputFormatter.digitsOnly,
                ],
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Please enter avoid shifts (N) days before or after another shift';
                  }
                  return null;
                },
              ),
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text('Can use Prohibited days', style: Theme.of(context).textTheme.titleSmall),
                  const Gap(18),
                  RadioCheckboxWidget(
                    title: 'Yes',
                    image: widget.shiftConfigurationList?.canYouProhibitedDays == 1
                        ? AppAssets.selectedCheckboxIcon
                        : AppAssets.unselectedCheckboxIcon,
                    onTap: () {
                      widget.canYouProhibitedDaysOnTap?.call(1);
                    },
                  ),
                  const Gap(16),
                  RadioCheckboxWidget(
                    title: 'No',
                    image: widget.shiftConfigurationList?.canYouProhibitedDays == 0
                        ? AppAssets.selectedCheckboxIcon
                        : AppAssets.unselectedCheckboxIcon,
                    onTap: () {
                      widget.canYouProhibitedDaysOnTap?.call(0);
                    },
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }
}
