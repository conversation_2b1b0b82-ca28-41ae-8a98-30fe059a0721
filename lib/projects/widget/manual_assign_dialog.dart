import 'package:collection/collection.dart';
import 'package:excel_app/calender/model/project_staff_model.dart';
import 'package:excel_app/constants/app_colors.dart';
import 'package:excel_app/projects/controller/manual_assign_controller.dart';
import 'package:excel_app/projects/model/project_calendardate_model.dart';
import 'package:excel_app/projects/model/project_model.dart';
import 'package:excel_app/projects/model/user_project_calendar_date_model.dart';
import 'package:excel_app/users/model/user_model.dart';
import 'package:excel_app/widget/custom_network_image.dart';
import 'package:excel_app/widget/custome_button_gradiant_widget.dart';
import 'package:flutter/material.dart' hide MetaData;
import 'package:gap/gap.dart';

class ManualAssignDialog extends StatelessWidget {
  ManualAssignDialog({
    required this.currentMonth,
    required this.staffMembers,
    required this.userProjectConflictDates,
    required this.onAssignComplete,
    super.key,
    this.projectCalendarDates,
    this.projectDetailData,
  }) : controller = ManualAssignController();

  final DateTime currentMonth;
  final List<ProjectStaffMember> staffMembers;
  final List<ProjectCalendarDateModel>? projectCalendarDates;
  final List<UserProjectCalendarDateModel> userProjectConflictDates;
  final ProjectModel? projectDetailData;
  final VoidCallback onAssignComplete;

  // Controller for managing state
  final ManualAssignController controller;

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.white,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.8,
        height: MediaQuery.of(context).size.height * 0.8,
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // Dialog header
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Manual Assignment',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: () => Navigator.of(context).pop(),
                ),
              ],
            ),
            const Divider(),

            // Instructions and counters
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                // Instructions
                Flexible(
                  child: Center(
                    child: Container(
                      padding: const EdgeInsets.all(8),
                      margin: const EdgeInsets.symmetric(vertical: 8),
                      decoration: BoxDecoration(
                        color: AppColors.lightMint,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: const Text(
                        'Click on empty cells to mark staff as present (P) for that day. '
                        'Click on cells with "P" to remove assignments.',
                        style: TextStyle(fontSize: 14),
                      ),
                    ),
                  ),
                ),

                // Counters
                ValueListenableBuilder<int>(
                  valueListenable: controller.addedCount,
                  builder: (context, addedCount, _) {
                    return ValueListenableBuilder<int>(
                      valueListenable: controller.removedCount,
                      builder: (context, removedCount, _) {
                        return Container(
                          padding: const EdgeInsets.all(8),
                          margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 8),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(color: AppColors.strokeColor),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  Container(
                                    width: 12,
                                    height: 12,
                                    decoration: BoxDecoration(
                                      color: AppColors.primary.withAlpha(76),
                                      borderRadius: BorderRadius.circular(2),
                                    ),
                                  ),
                                  const SizedBox(width: 4),
                                  Text(
                                    'Added: $addedCount',
                                    style: const TextStyle(fontSize: 12),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 4),
                              Row(
                                children: [
                                  Container(
                                    width: 12,
                                    height: 12,
                                    decoration: BoxDecoration(
                                      color: Colors.red.withAlpha(76),
                                      borderRadius: BorderRadius.circular(2),
                                    ),
                                  ),
                                  const SizedBox(width: 4),
                                  Text(
                                    'Removed: $removedCount',
                                    style: const TextStyle(fontSize: 12),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        );
                      },
                    );
                  },
                ),
              ],
            ),

            // Calendar view
            Expanded(
              child: SingleChildScrollView(
                child: _buildCalendarView(context),
              ),
            ),

            // Undo/Redo and action buttons
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                // Undo/Redo buttons
                ValueListenableBuilder<bool>(
                  valueListenable: controller.canUndo,
                  builder: (context, canUndo, _) {
                    return ValueListenableBuilder<bool>(
                      valueListenable: controller.canRedo,
                      builder: (context, canRedo, _) {
                        return ValueListenableBuilder<List<UserProjectCalendarDateModel>>(
                          valueListenable: controller.manualAssignments,
                          builder: (context, assignments, _) {
                            return Row(
                              children: [
                                IconButton(
                                  icon: const Icon(Icons.undo),
                                  tooltip: 'Undo',
                                  onPressed: canUndo ? () => controller.handleUndo(context) : null,
                                  color: canUndo ? AppColors.primary : Colors.grey,
                                ),
                                IconButton(
                                  icon: const Icon(Icons.redo),
                                  tooltip: 'Redo',
                                  onPressed: canRedo ? () => controller.handleRedo(context) : null,
                                  color: canRedo ? AppColors.primary : Colors.grey,
                                ),
                                if (canUndo || canRedo)
                                  Text(
                                    '${assignments.length} assignments',
                                    style: TextStyle(
                                      color: Colors.grey[600],
                                      fontSize: 12,
                                    ),
                                  ),
                              ],
                            );
                          },
                        );
                      },
                    );
                  },
                ),

                // Action buttons
                Row(
                  children: [
                    CustomeButtonGradiantWidget(
                      onTap: () => Navigator.of(context).pop(),
                      buttonText: 'Cancel',
                      height: 40,
                      width: 100,
                    ),
                    const Gap(16),
                    ValueListenableBuilder<bool>(
                      valueListenable: controller.isLoading,
                      builder: (context, isLoading, _) {
                        return CustomeButtonGradiantWidget(
                          onTap: () => controller.handleDonePressed(
                            context,
                            projectDetailData?.id.toString() ?? '',
                            onAssignComplete,
                          ),
                          buttonText: 'Done',
                          isGradient: true,
                          height: 40,
                          width: 100,
                          isLoading: isLoading,
                        );
                      },
                    ),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCalendarView(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Calendar header with days
        _buildCalendarHeader(context),

        // Staff rows
        ...staffMembers.map((staff) => _buildStaffRows(context, staff)),
      ],
    );
  }

  Widget _buildCalendarHeader(BuildContext context) {
    final daysInMonth = controller.getDaysInMonth(currentMonth);

    return Container(
      height: 60,
      decoration: const BoxDecoration(
        border: Border(
          bottom: BorderSide(color: AppColors.strokeColor),
        ),
      ),
      child: Row(
        children: [
          // Staff name column
          Container(
            width: 200,
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: const Text(
              'Staff',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 14,
              ),
            ),
          ),

          // Day columns
          ...List.generate(daysInMonth, (index) {
            final day = index + 1;
            final date = DateTime(currentMonth.year, currentMonth.month, day);
            final isWeekend = date.weekday == DateTime.saturday || date.weekday == DateTime.sunday;

            // Check if this date is a holiday
            final isHoliday = projectCalendarDates?.firstWhereOrNull(
                  (ProjectCalendarDateModel element) =>
                      element.date?.year == date.year &&
                      element.date?.month == date.month &&
                      element.date?.day == date.day,
                ) !=
                null;

            // Check if this date has a conflict
            final isCheckProjectConflictDate = userProjectConflictDates.firstWhereOrNull(
              (UserProjectCalendarDateModel element) =>
                  element.date?.year == date.year && element.date?.month == date.month && element.date?.day == date.day,
            );

            // Determine background color - match exactly with ProjectMonthlyListCalenderView
            final bgColor = isCheckProjectConflictDate != null
                ? AppColors.conflictBackgroundColor
                : (!isWeekend && isHoliday)
                    ? AppColors.lightGray
                    : isWeekend
                        ? AppColors.geyser
                        : Colors.transparent;

            return Expanded(
              child: Container(
                decoration: BoxDecoration(
                  color: bgColor,
                  border: const Border(
                    left: BorderSide(color: AppColors.strokeColor),
                  ),
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      controller.getDayLetter(date.weekday),
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[700],
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      day.toString(),
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        color: isHoliday ? Colors.grey[600] : null,
                      ),
                    ),
                  ],
                ),
              ),
            );
          }),
        ],
      ),
    );
  }

  Widget _buildStaffRows(BuildContext context, ProjectStaffMember staff) {
    if (staff.userModel?.users == null || staff.userModel!.users.isEmpty) {
      return const SizedBox();
    }

    return Column(
      children: staff.userModel!.users.map((user) => _buildStaffRow(context, user)).toList(),
    );
  }

  Widget _buildStaffRow(BuildContext context, UserModel user) {
    final daysInMonth = controller.getDaysInMonth(currentMonth);

    return Container(
      height: 40,
      decoration: const BoxDecoration(
        border: Border(
          bottom: BorderSide(color: AppColors.strokeColor),
        ),
      ),
      child: Row(
        children: [
          // Staff name with role
          Container(
            width: 200,
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Row(
              children: [
                // User image
                ClipRRect(
                  borderRadius: BorderRadius.circular(100),
                  child: CustomNetworkImage(
                    imageUrl: user.imagePath ?? '',
                    height: 24,
                    width: 24,
                    isSmallImage: true,
                  ),
                ),
                const SizedBox(width: 12),
                // User name and role
                Flexible(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        user.name ?? '',
                        style: const TextStyle(
                          fontWeight: FontWeight.w500,
                          fontSize: 12,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                      if (user.role != null)
                        Text(
                          user.userRoleViewString,
                          style: TextStyle(
                            fontSize: 10,
                            color: Colors.grey[600],
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          // Day cells
          ...List.generate(daysInMonth, (index) {
            final day = index + 1;
            final date = DateTime(currentMonth.year, currentMonth.month, day);
            return _buildDayCell(context, date, user);
          }),
        ],
      ),
    );
  }

  Widget _buildDayCell(BuildContext context, DateTime date, UserModel user) {
    return ValueListenableBuilder<List<UserProjectCalendarDateModel>>(
      valueListenable: controller.manualAssignments,
      builder: (context, manualAssignments, _) {
        // Check if this date is a holiday from project calendar dates
        final holidayDate = projectCalendarDates?.firstWhereOrNull(
          (ProjectCalendarDateModel element) =>
              element.date?.year == date.year && element.date?.month == date.month && element.date?.day == date.day,
        );

        final isHoliday = holidayDate != null;

        // Check if this date is a weekend
        final isWeekend = date.weekday == DateTime.saturday || date.weekday == DateTime.sunday;

        // Check if this date already has an assignment
        final existingAssignment = user.userProjectAvailableDates?.firstWhereOrNull(
          (UserProjectCalendarDateModel element) =>
              element.date?.year == date.year && element.date?.month == date.month && element.date?.day == date.day,
        );

        // Check if this date has a conflict
        final conflictDate = userProjectConflictDates.firstWhereOrNull(
          (UserProjectCalendarDateModel element) =>
              element.date?.year == date.year &&
              element.date?.month == date.month &&
              element.date?.day == date.day &&
              element.userId == user.id,
        );

        final hasConflict = conflictDate != null;

        // Check if this date is manually assigned
        final manualAssignment = manualAssignments.firstWhereOrNull(
          (UserProjectCalendarDateModel element) =>
              element.date?.year == date.year &&
              element.date?.month == date.month &&
              element.date?.day == date.day &&
              element.userId == user.id,
        );

        // Check if this is a manual assignment with positive leaveCategoryId (added)
        // or negative leaveCategoryId (removed)
        final isManuallyAssigned = manualAssignment != null && (manualAssignment.leaveCategoryId ?? 0) > 0;

        // Check if this is a manual removal of an existing assignment
        final isManuallyRemoved = manualAssignment != null && (manualAssignment.leaveCategoryId ?? 0) < 0;

        UserProjectCalendarDateModel? addAvailabilityDate;
        final scheduledDate = user.userProjectCalendarDates?.firstWhereOrNull(
          (scheduledDate) =>
              scheduledDate.date?.year == date.year &&
              scheduledDate.date?.month == date.month &&
              scheduledDate.date?.day == date.day,
        );
        if (scheduledDate != null && scheduledDate.leaveCategory != null) {
          addAvailabilityDate = scheduledDate;
        }

        // Determine cell color - match exactly with ProjectMonthlyListCalenderView
        final cellColor = (hasConflict && addAvailabilityDate == null)
            ? AppColors.conflictBackgroundColor
            : isManuallyRemoved
                ? (isWeekend ? AppColors.geyser : Colors.transparent) // Keep weekend color when manually removed
                : addAvailabilityDate != null
                    ? AppColors.hexToColor(addAvailabilityDate.leaveCategory?.colorHex ?? '')
                    : (isHoliday && !isWeekend)
                        ? AppColors.lightGray
                        : (isWeekend && addAvailabilityDate != null)
                            ? AppColors.hexToColor(addAvailabilityDate.leaveCategory?.colorHex ?? '')
                            : isWeekend
                                ? AppColors.geyser
                                : isManuallyAssigned
                                    ? AppColors.primary.withAlpha(76) // 0.3 opacity
                                    : Colors.transparent;

        // Determine if cell is clickable - allow clicking on all non-holiday, non-conflict dates
        // Also allow clicking on dates with existing assignments to remove them
        // Now includes weekends (Saturday and Sunday) for assignment
        final isClickable = !isHoliday && !hasConflict && addAvailabilityDate == null;

        return Expanded(
          child: InkWell(
            onTap: isClickable || isManuallyAssigned || existingAssignment != null
                ? () => _handleCellTap(context, date, user)
                : null,
            child: Container(
              decoration: BoxDecoration(
                color: cellColor,
                border: const Border(
                  left: BorderSide(color: AppColors.strokeColor),
                ),
              ),
              child: Center(
                child: Text(
                  // Show 'P' if:
                  // 1. Manually assigned (positive leaveCategoryId)
                  // 2. Has existing assignment that hasn't been manually removed
                  // 3. Has addAvailabilityDate (scheduled assignment)
                  isManuallyAssigned ||
                          (existingAssignment != null && !isManuallyRemoved) ||
                          (addAvailabilityDate != null && !isManuallyRemoved)
                      ? 'P'
                      : '',
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  void _handleCellTap(BuildContext context, DateTime date, UserModel user) {
    // Get the current state of manualAssignments
    final manualAssignments = controller.manualAssignments.value;

    // Check if this date is already assigned
    final existingManualAssignment = manualAssignments.firstWhereOrNull(
      (UserProjectCalendarDateModel element) =>
          element.date?.year == date.year &&
          element.date?.month == date.month &&
          element.date?.day == date.day &&
          element.userId == user.id,
    );

    final existingAssignment = user.userProjectAvailableDates?.firstWhereOrNull(
      (UserProjectCalendarDateModel element) =>
          element.date?.year == date.year && element.date?.month == date.month && element.date?.day == date.day,
    );

    // Check if this is a manual assignment with positive leaveCategoryId (added)
    final isManuallyAssigned = existingManualAssignment != null && (existingManualAssignment.leaveCategoryId ?? 0) > 0;

    // Check if this is a manual removal of an existing assignment
    final isManuallyRemoved = existingManualAssignment != null && (existingManualAssignment.leaveCategoryId ?? 0) < 0;

    // Determine if already assigned:
    // - Has manual assignment with positive ID (manually added)
    // - Has existing assignment that hasn't been manually removed
    final isAlreadyAssigned = isManuallyAssigned || (existingAssignment != null && !isManuallyRemoved);

    // Show different confirmation dialog based on whether the cell already has an assignment
    showDialog<void>(
      context: context,
      builder: (dialogContext) => AlertDialog(
        title: Text(isAlreadyAssigned ? 'Remove Assignment' : 'Confirm Assignment'),
        content: Text(
          isAlreadyAssigned
              ? 'Remove ${user.name} from ${date.day}/${date.month}/${date.year}?'
              : 'Mark ${user.name} as present on ${date.day}/${date.month}/${date.year}?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(dialogContext).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(dialogContext).pop();
              controller.addManualAssignment(date, user, context);
            },
            child: Text(isAlreadyAssigned ? 'Remove' : 'Yes'),
          ),
        ],
      ),
    );
  }
}
