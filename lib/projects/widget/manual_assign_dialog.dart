import 'package:collection/collection.dart';
import 'package:excel_app/calender/model/project_staff_model.dart';
import 'package:excel_app/constants/app_assets.dart';
import 'package:excel_app/constants/app_colors.dart';
import 'package:excel_app/projects/controller/manual_assign_controller.dart';
import 'package:excel_app/projects/model/project_calendardate_model.dart';
import 'package:excel_app/projects/model/project_model.dart';
import 'package:excel_app/projects/model/user_project_calendar_date_model.dart';
import 'package:excel_app/users/model/user_model.dart';
import 'package:excel_app/widget/app_asset_image.dart';
import 'package:excel_app/widget/custom_network_image.dart';
import 'package:excel_app/widget/custome_button_gradiant_widget.dart';
import 'package:flutter/material.dart' hide MetaData;
import 'package:gap/gap.dart';

class ManualAssignDialog extends StatelessWidget {
  ManualAssignDialog({
    required this.currentMonth,
    required this.staffMembers,
    required this.userProjectConflictDates,
    required this.onAssignComplete,
    super.key,
    this.projectCalendarDates,
    this.projectDetailData,
  }) : controller = ManualAssignController();

  final DateTime currentMonth;
  final List<ProjectStaffMember> staffMembers;
  final List<ProjectCalendarDateModel>? projectCalendarDates;
  final List<UserProjectCalendarDateModel> userProjectConflictDates;
  final ProjectModel? projectDetailData;
  final VoidCallback onAssignComplete;

  // Controller for managing state
  final ManualAssignController controller;

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.white,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.8,
        height: MediaQuery.of(context).size.height * 0.8,
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // Dialog header
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Manual Assignment',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: () => Navigator.of(context).pop(),
                ),
              ],
            ),
            const Divider(),

            // Instructions and counters
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                // Instructions
                Flexible(
                  child: Center(
                    child: Container(
                      padding: const EdgeInsets.all(8),
                      margin: const EdgeInsets.symmetric(vertical: 8),
                      decoration: BoxDecoration(
                        color: AppColors.lightMint,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: const Text(
                        'Click on empty cells to mark staff as present (✔) for that day. '
                        'Click on cells with "✔" to remove assignments.',
                        style: TextStyle(fontSize: 14),
                      ),
                    ),
                  ),
                ),

                // Counters
                ValueListenableBuilder<int>(
                  valueListenable: controller.addedCount,
                  builder: (context, addedCount, _) {
                    return ValueListenableBuilder<int>(
                      valueListenable: controller.removedCount,
                      builder: (context, removedCount, _) {
                        return Container(
                          padding: const EdgeInsets.all(8),
                          margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 8),
                          decoration: BoxDecoration(
                            color: AppColors.white,
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(color: AppColors.strokeColor),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  Container(
                                    width: 12,
                                    height: 12,
                                    decoration: BoxDecoration(
                                      color: AppColors.primary.withAlpha(76),
                                      borderRadius: BorderRadius.circular(2),
                                    ),
                                  ),
                                  const SizedBox(width: 4),
                                  Text(
                                    'Added: $addedCount',
                                    style: const TextStyle(fontSize: 12),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 4),
                              Row(
                                children: [
                                  Container(
                                    width: 12,
                                    height: 12,
                                    decoration: BoxDecoration(
                                      color: Colors.red.withAlpha(76),
                                      borderRadius: BorderRadius.circular(2),
                                    ),
                                  ),
                                  const SizedBox(width: 4),
                                  Text(
                                    'Removed: $removedCount',
                                    style: const TextStyle(fontSize: 12),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        );
                      },
                    );
                  },
                ),
              ],
            ),

            // Calendar view with fixed header and scrollable content
            Expanded(
              child: _buildCalendarView(context),
            ),
            const Gap(10),
            // Undo/Redo and action buttons
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                // Undo/Redo buttons
                ValueListenableBuilder<bool>(
                  valueListenable: controller.canUndo,
                  builder: (context, canUndo, _) {
                    return ValueListenableBuilder<bool>(
                      valueListenable: controller.canRedo,
                      builder: (context, canRedo, _) {
                        return ValueListenableBuilder<List<UserProjectCalendarDateModel>>(
                          valueListenable: controller.manualAssignments,
                          builder: (context, assignments, _) {
                            return Row(
                              children: [
                                IconButton(
                                  icon: const Icon(Icons.undo),
                                  tooltip: 'Undo',
                                  onPressed: canUndo ? () => controller.handleUndo(context) : null,
                                  color: canUndo ? AppColors.primary : Colors.grey,
                                ),
                                IconButton(
                                  icon: const Icon(Icons.redo),
                                  tooltip: 'Redo',
                                  onPressed: canRedo ? () => controller.handleRedo(context) : null,
                                  color: canRedo ? AppColors.primary : Colors.grey,
                                ),
                                if (canUndo || canRedo)
                                  Text(
                                    '${assignments.length} assignments',
                                    style: TextStyle(
                                      color: Colors.grey[600],
                                      fontSize: 12,
                                    ),
                                  ),
                              ],
                            );
                          },
                        );
                      },
                    );
                  },
                ),

                // Action buttons
                Row(
                  children: [
                    CustomeButtonGradiantWidget(
                      onTap: () => Navigator.of(context).pop(),
                      buttonText: 'Cancel',
                      height: 40,
                      width: 100,
                      isUseContainerBorder: true,
                    ),
                    const Gap(16),
                    ValueListenableBuilder<bool>(
                      valueListenable: controller.isLoading,
                      builder: (context, isLoading, _) {
                        return CustomeButtonGradiantWidget(
                          onTap: () => controller.handleDonePressed(
                            context,
                            projectDetailData?.id.toString() ?? '',
                            onAssignComplete,
                          ),
                          buttonText: 'Done',
                          isGradient: true,
                          height: 40,
                          width: 100,
                          isLoading: isLoading,
                        );
                      },
                    ),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCalendarView(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        border: Border(
          top: BorderSide(color: AppColors.strokeColor),
          left: BorderSide(color: AppColors.strokeColor),
          right: BorderSide(color: AppColors.strokeColor),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Calendar header with days (fixed)
          _buildCalendarHeader(context),
          // Staff rows (scrollable)
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  ...staffMembers.map((staff) => _buildStaffRows(context, staff)),
                  // Add user category rows section (project planning section)
                  ...staffMembers.map(
                    (staff) => ValueListenableBuilder<List<UserProjectCalendarDateModel>>(
                      valueListenable: controller.manualAssignments,
                      builder: (context, assignments, _) {
                        return _buildUserCategoryRow(staff);
                      },
                    ),
                  ),
                  // Add total count row that updates with manual assignments
                  ValueListenableBuilder<List<UserProjectCalendarDateModel>>(
                    valueListenable: controller.manualAssignments,
                    builder: (context, assignments, _) {
                      return _buildCategoryTotal(staffMembers);
                    },
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCalendarHeader(BuildContext context) {
    return Container(
      height: 60,
      decoration: const BoxDecoration(
        border: Border(
          bottom: BorderSide(color: AppColors.strokeColor),
        ),
      ),
      child: Row(
        children: [
          // Staff name column
          Container(
            width: 200,
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: const Text(
              'Staff',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 14,
              ),
            ),
          ),
          // Day headers
          Expanded(
            child: Row(
              children: _buildDayHeaders(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStaffRows(BuildContext context, ProjectStaffMember staff) {
    if (staff.userModel?.users == null || staff.userModel!.users.isEmpty) {
      return const SizedBox();
    }

    return Column(
      children: staff.userModel!.users
          .map(
            (user) => ValueListenableBuilder<List<UserProjectCalendarDateModel>>(
              valueListenable: controller.manualAssignments,
              builder: (context, assignments, _) {
                return _buildStaffRow(context, user);
              },
            ),
          )
          .toList(),
    );
  }

  Widget _buildStaffRow(BuildContext context, UserModel user) {
    return Container(
      height: 40,
      decoration: const BoxDecoration(
        border: Border(
          bottom: BorderSide(color: AppColors.strokeColor),
        ),
      ),
      child: Row(
        children: [
          // Staff name with role
          Container(
            width: 200,
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Row(
              children: [
                // User image
                ClipRRect(
                  borderRadius: BorderRadius.circular(100),
                  child: CustomNetworkImage(
                    imageUrl: user.imagePath ?? '',
                    height: 24,
                    width: 24,
                    isSmallImage: true,
                  ),
                ),
                const SizedBox(width: 12),
                // User name and role
                Flexible(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        user.name ?? '',
                        style: const TextStyle(
                          fontWeight: FontWeight.w500,
                          fontSize: 12,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                      if (user.role != null)
                        Text(
                          user.userRoleViewString,
                          style: TextStyle(
                            fontSize: 10,
                            color: Colors.grey[600],
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          // Day cells with Total column
          Expanded(
            child: Row(
              children: _buildUserDayCells(context, user),
            ),
          ),
        ],
      ),
    );
  }

  List<Widget> _buildUserDayCells(BuildContext context, UserModel user) {
    final cells = <Widget>[];
    final daysInMonth = _getDaysInMonthWithTotal();

    // Calculate user's total assignments
    var existingAssignments = 0;
    var manualAdditions = 0;
    var manualRemovals = 0;

    // Count existing assignments
    for (final date in user.userProjectAvailableDates ?? <UserProjectCalendarDateModel>[]) {
      if (date.date != null && date.date!.year == currentMonth.year && date.date!.month == currentMonth.month) {
        existingAssignments++;
      }
    }

    // Count manual assignments for this user (separate additions and removals)
    for (final assignment in controller.manualAssignments.value) {
      if (assignment.userId == user.id &&
          assignment.date != null &&
          assignment.date!.year == currentMonth.year &&
          assignment.date!.month == currentMonth.month) {
        if ((assignment.leaveCategoryId ?? 0) > 0) {
          // Positive leaveCategoryId means manual addition
          manualAdditions++;
        } else if ((assignment.leaveCategoryId ?? 0) < 0) {
          // Negative leaveCategoryId means manual removal
          manualRemovals++;
        }
      }
    }

    // Calculate final total: existing + additions - removals
    final totalAssignments = existingAssignments + manualAdditions - manualRemovals;

    for (var day = 1; day <= daysInMonth.length; day++) {
      if (day == daysInMonth.length) {
        // Add the "Total" cell at the end with user's total count
        cells.add(
          Expanded(
            child: Container(
              decoration: const BoxDecoration(
                color: AppColors.transparent,
              ),
              child: Center(
                child: Text(
                  totalAssignments != 0 ? totalAssignments.toString() : '',
                  style: const TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 12,
                  ),
                ),
              ),
            ),
          ),
        );
      } else {
        final date = DateTime(currentMonth.year, currentMonth.month, day);
        cells.add(
          _buildDayCell(
            context,
            date,
            user,
            day == daysInMonth.length - 1,
          ),
        );
      }
    }
    return cells;
  }

  Widget _buildDayCell(BuildContext context, DateTime date, UserModel user, bool isLastCell) {
    return ValueListenableBuilder<List<UserProjectCalendarDateModel>>(
      valueListenable: controller.manualAssignments,
      builder: (context, manualAssignments, _) {
        // Check if this date is a holiday from project calendar dates
        final holidayDate = projectCalendarDates?.firstWhereOrNull(
          (ProjectCalendarDateModel element) =>
              element.date?.year == date.year && element.date?.month == date.month && element.date?.day == date.day,
        );

        final isHoliday = holidayDate != null;

        // Check if this date is a weekend
        final isWeekend = date.weekday == DateTime.saturday || date.weekday == DateTime.sunday;

        // Check if this date already has an assignment
        final existingAssignment = user.userProjectAvailableDates?.firstWhereOrNull(
          (UserProjectCalendarDateModel element) =>
              element.date?.year == date.year && element.date?.month == date.month && element.date?.day == date.day,
        );

        // Check if this date has a conflict
        final conflictDate = userProjectConflictDates.firstWhereOrNull(
          (UserProjectCalendarDateModel element) =>
              element.date?.year == date.year &&
              element.date?.month == date.month &&
              element.date?.day == date.day &&
              element.userId == user.id,
        );

        final hasConflict = conflictDate != null;

        // Check if this date is manually assigned
        final manualAssignment = manualAssignments.firstWhereOrNull(
          (UserProjectCalendarDateModel element) =>
              element.date?.year == date.year &&
              element.date?.month == date.month &&
              element.date?.day == date.day &&
              element.userId == user.id,
        );

        // Check if this is a manual assignment with positive leaveCategoryId (added)
        // or negative leaveCategoryId (removed)
        final isManuallyAssigned = manualAssignment != null && (manualAssignment.leaveCategoryId ?? 0) > 0;

        // Check if this is a manual removal of an existing assignment
        final isManuallyRemoved = manualAssignment != null && (manualAssignment.leaveCategoryId ?? 0) < 0;

        UserProjectCalendarDateModel? addAvailabilityDate;
        final scheduledDate = user.userProjectCalendarDates?.firstWhereOrNull(
          (scheduledDate) =>
              scheduledDate.date?.year == date.year &&
              scheduledDate.date?.month == date.month &&
              scheduledDate.date?.day == date.day,
        );
        if (scheduledDate != null && scheduledDate.leaveCategory != null) {
          addAvailabilityDate = scheduledDate;
        }

        // Determine cell color - match exactly with ProjectMonthlyListCalenderView
        final cellColor = (hasConflict && addAvailabilityDate == null)
            ? AppColors.conflictBackgroundColor
            : isManuallyRemoved
                ? (isWeekend ? AppColors.geyser : Colors.transparent) // Keep weekend color when manually removed
                : addAvailabilityDate != null
                    ? AppColors.hexToColor(addAvailabilityDate.leaveCategory?.colorHex ?? '')
                    : (isHoliday && !isWeekend)
                        ? AppColors.lightGray
                        : (isWeekend && addAvailabilityDate != null)
                            ? AppColors.hexToColor(addAvailabilityDate.leaveCategory?.colorHex ?? '')
                            : isWeekend
                                ? AppColors.geyser
                                : isManuallyAssigned
                                    ? AppColors.primary.withAlpha(76) // 0.3 opacity
                                    : Colors.transparent;

        // Determine if cell is clickable - allow clicking on all non-holiday, non-conflict dates
        // Also allow clicking on dates with existing assignments to remove them
        // Now includes weekends (Saturday and Sunday) for assignment
        final isClickable = !hasConflict;

        return Expanded(
          child: InkWell(
            onTap: isClickable || isManuallyAssigned || existingAssignment != null
                ? () => _handleCellTap(context, date, user)
                : null,
            child: Container(
              decoration: BoxDecoration(
                color: cellColor,
                border: Border(
                  left: const BorderSide(color: AppColors.strokeColor),
                  right: BorderSide(color: isLastCell ? AppColors.black : AppColors.transparent),
                ),
              ),
              child: Center(
                child: isManuallyAssigned || (existingAssignment != null && !isManuallyRemoved)
                    ? const AppAssetImage(AppAssets.checkIcon)
                    : const SizedBox.shrink(),
              ),
            ),
          ),
        );
      },
    );
  }

  void _handleCellTap(BuildContext context, DateTime date, UserModel user) {
    // Get the current state of manualAssignments
    final manualAssignments = controller.manualAssignments.value;

    // Check if this date is already assigned
    final existingManualAssignment = manualAssignments.firstWhereOrNull(
      (UserProjectCalendarDateModel element) =>
          element.date?.year == date.year &&
          element.date?.month == date.month &&
          element.date?.day == date.day &&
          element.userId == user.id,
    );

    final existingAssignment = user.userProjectAvailableDates?.firstWhereOrNull(
      (UserProjectCalendarDateModel element) =>
          element.date?.year == date.year && element.date?.month == date.month && element.date?.day == date.day,
    );

    // Check if this is a manual assignment with positive leaveCategoryId (added)
    final isManuallyAssigned = existingManualAssignment != null && (existingManualAssignment.leaveCategoryId ?? 0) > 0;

    // Check if this is a manual removal of an existing assignment
    final isManuallyRemoved = existingManualAssignment != null && (existingManualAssignment.leaveCategoryId ?? 0) < 0;

    // Determine if already assigned:
    // - Has manual assignment with positive ID (manually added)
    // - Has existing assignment that hasn't been manually removed
    final isAlreadyAssigned = isManuallyAssigned || (existingAssignment != null && !isManuallyRemoved);

    // Show different confirmation dialog based on whether the cell already has an assignment
    showDialog<void>(
      context: context,
      builder: (dialogContext) => AlertDialog(
        title: Text(isAlreadyAssigned ? 'Remove Assignment' : 'Confirm Assignment'),
        content: Text(
          isAlreadyAssigned
              ? 'Remove ${user.name} from ${date.day}/${date.month}/${date.year}?'
              : 'Mark ${user.name} as present on ${date.day}/${date.month}/${date.year}?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(dialogContext).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(dialogContext).pop();
              controller.addManualAssignment(date, user, context);
            },
            child: Text(isAlreadyAssigned ? 'Remove' : 'Yes'),
          ),
        ],
      ),
    );
  }

  // Total count methods copied from ProjectMonthlyListCalenderView
  Widget _buildCategoryTotal(List<ProjectStaffMember> staffList) {
    final totalSchedules = _calculateCategoryTotalSchedules(staffList);

    return SizedBox(
      height: 40,
      child: Row(
        children: [
          Container(
            width: 200,
            alignment: Alignment.centerLeft,
            padding: const EdgeInsets.symmetric(horizontal: 16),
            decoration: const BoxDecoration(
              border: Border(
                bottom: BorderSide(color: AppColors.strokeColor),
              ),
            ),
            child: const Text(
              'Total',
              style: TextStyle(
                fontWeight: FontWeight.w600,
                fontSize: 12,
              ),
            ),
          ),
          Expanded(
            child: Row(
              children: _buildCategoryTotalCounts(totalSchedules),
            ),
          ),
        ],
      ),
    );
  }

  List<Widget> _buildCategoryTotalCounts(Map<int, int> totalsByDay) {
    final cells = <Widget>[];
    final daysInMonth = _getDaysInMonthWithTotal();

    for (var day = 1; day <= daysInMonth.length; day++) {
      final total = totalsByDay[day] ?? 0;
      if (day == daysInMonth.length) {
        // Calculate total assignments across all days
        final grandTotal = totalsByDay.values.fold(0, (sum, value) => sum + value);
        cells.add(
          Expanded(
            child: Container(
              height: 40,
              decoration: const BoxDecoration(
                border: Border(
                  bottom: BorderSide(color: AppColors.strokeColor),
                ),
              ),
              child: Center(
                child: Text(
                  grandTotal > 0 ? grandTotal.toString() : '',
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
              ),
            ),
          ),
        );
      } else {
        cells.add(
          Expanded(
            child: Container(
              height: 40,
              decoration: BoxDecoration(
                border: Border(
                  left: const BorderSide(color: AppColors.strokeColor),
                  right: BorderSide(color: day == daysInMonth.length - 1 ? AppColors.black : AppColors.transparent),
                  bottom: const BorderSide(color: AppColors.strokeColor),
                ),
              ),
              child: Center(
                child: Text(
                  total > 0 ? total.toString() : '',
                  style: const TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 12,
                  ),
                ),
              ),
            ),
          ),
        );
      }
    }
    return cells;
  }

  Map<int, int> _calculateCategoryTotalSchedules(List<ProjectStaffMember> staffList) {
    final totalsByDay = <int, int>{};

    // First, count existing assignments from staff members
    for (final staff in staffList) {
      for (final user in staff.userModel?.users ?? <UserModel>[]) {
        for (final date in user.userProjectAvailableDates ?? <UserProjectCalendarDateModel>[]) {
          if (date.date != null) {
            final day = date.date!.day;
            totalsByDay[day] = (totalsByDay[day] ?? 0) + 1;
          }
        }
      }
    }

    // Then, apply manual assignments (additions and removals)
    for (final assignment in controller.manualAssignments.value) {
      if (assignment.date != null) {
        final day = assignment.date!.day;
        if ((assignment.leaveCategoryId ?? 0) > 0) {
          // Positive leaveCategoryId means manual addition
          totalsByDay[day] = (totalsByDay[day] ?? 0) + 1;
        } else if ((assignment.leaveCategoryId ?? 0) < 0) {
          // Negative leaveCategoryId means manual removal
          totalsByDay[day] = (totalsByDay[day] ?? 0) - 1;
          // Ensure we don't go below 0
          if (totalsByDay[day]! < 0) {
            totalsByDay[day] = 0;
          }
        }
      }
    }

    return totalsByDay;
  }

  Widget _buildUserCategoryRow(ProjectStaffMember staff) {
    return Container(
      height: 40,
      decoration: const BoxDecoration(
        border: Border(
          bottom: BorderSide(color: AppColors.strokeColor),
        ),
      ),
      child: Row(
        children: [
          Container(
            width: 200,
            padding: const EdgeInsets.symmetric(horizontal: 16),
            decoration: const BoxDecoration(),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    staff.userModel?.userRoleViewString ?? '',
                    style: const TextStyle(
                      fontWeight: FontWeight.w500,
                      fontSize: 12,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ),
          Expanded(
            child: Row(
              children: _buildUserCategoryCount(staff),
            ),
          ),
        ],
      ),
    );
  }

  List<Widget> _buildUserCategoryCount(ProjectStaffMember staff) {
    final cells = <Widget>[];
    final daysInMonth = _getDaysInMonthWithTotal();

    // Get all assigned dates for this staff category
    final assignUserDate = staff.userModel?.users
            .expand<UserProjectCalendarDateModel>((e) => e.userProjectAvailableDates ?? [])
            .toList() ??
        [];

    // Calculate total assignments for this category including manual assignments
    var totalCategoryAssignments = 0;

    for (var day = 1; day <= daysInMonth.length; day++) {
      if (day == daysInMonth.length) {
        // Add the "Total" cell at the end with the cumulative count
        cells.add(
          Expanded(
            child: Container(
              decoration: const BoxDecoration(
                color: AppColors.transparent,
              ),
              child: SizedBox(
                height: 40,
                child: Center(
                  child: Text(
                    totalCategoryAssignments > 0 ? totalCategoryAssignments.toString() : '',
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                ),
              ),
            ),
          ),
        );
      } else {
        final date = DateTime(currentMonth.year, currentMonth.month, day);

        // Count existing assignments for this date
        final existingCount = assignUserDate
            .where(
              (scheduledDate) =>
                  scheduledDate.date?.year == date.year &&
                  scheduledDate.date?.month == date.month &&
                  scheduledDate.date?.day == date.day,
            )
            .length;

        // Count manual assignments for this date for users in this category
        // Separate manual additions and removals
        var manualAdditions = 0;
        var manualRemovals = 0;

        for (final assignment in controller.manualAssignments.value) {
          if (assignment.date?.year == date.year &&
              assignment.date?.month == date.month &&
              assignment.date?.day == date.day) {
            // Check if this assignment belongs to a user in this staff category
            final belongsToCategory = staff.userModel?.users.any((user) => user.id == assignment.userId) ?? false;
            if (belongsToCategory) {
              if ((assignment.leaveCategoryId ?? 0) > 0) {
                // Positive leaveCategoryId means manual addition
                manualAdditions++;
              } else if ((assignment.leaveCategoryId ?? 0) < 0) {
                // Negative leaveCategoryId means manual removal
                manualRemovals++;
              }
            }
          }
        }

        // Calculate final count: existing + additions - removals
        final totalCount = existingCount + manualAdditions - manualRemovals;
        totalCategoryAssignments += totalCount;

        cells.add(
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                border: Border(
                  left: const BorderSide(color: AppColors.strokeColor),
                  right: BorderSide(color: day == daysInMonth.length - 1 ? AppColors.black : AppColors.transparent),
                ),
              ),
              child: SizedBox(
                height: 40,
                child: Center(
                  child: Text(
                    totalCount > 0 ? '$totalCount' : '',
                    style: const TextStyle(
                      fontWeight: FontWeight.w600,
                      fontSize: 12,
                    ),
                  ),
                ),
              ),
            ),
          ),
        );
      }
    }
    return cells;
  }

  // Helper methods copied from ProjectMonthlyListCalenderView
  List<String> _getDaysInMonthWithTotal() {
    return List<String>.generate(
      _getDaysInMonth() + 1,
      (index) => index < _getDaysInMonth() ? '${index + 1}' : 'Total',
    );
  }

  int _getDaysInMonth() {
    return DateTime(currentMonth.year, currentMonth.month + 1, 0).day;
  }

  List<Widget> _buildDayHeaders() {
    final headers = <Widget>[];
    final daysInMonth = _getDaysInMonthWithTotal();

    for (var day = 1; day <= daysInMonth.length; day++) {
      if (day == daysInMonth.length) {
        // Add the "Total" header at the end
        headers.add(
          const Expanded(
            child: SizedBox(
              child: Center(
                child: Text(
                  'Total',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ),
          ),
        );
      } else {
        final date = DateTime(currentMonth.year, currentMonth.month, day);
        final isWeekend = date.weekday == DateTime.saturday || date.weekday == DateTime.sunday;

        // Check if this date is a holiday
        final isHoliday = projectCalendarDates?.firstWhereOrNull(
              (ProjectCalendarDateModel element) =>
                  element.date?.year == date.year && element.date?.month == date.month && element.date?.day == date.day,
            ) !=
            null;

        // Check if this date has a conflict
        final isCheckProjectConflictDate = userProjectConflictDates.firstWhereOrNull(
          (UserProjectCalendarDateModel element) =>
              element.date?.year == date.year && element.date?.month == date.month && element.date?.day == date.day,
        );

        // Determine background color - match exactly with ProjectMonthlyListCalenderView
        final bgColor = isCheckProjectConflictDate != null
            ? AppColors.conflictBackgroundColor
            : (!isWeekend && isHoliday)
                ? AppColors.lightGray
                : isWeekend
                    ? AppColors.geyser
                    : Colors.transparent;

        headers.add(
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                color: bgColor,
                border: Border(
                  left: const BorderSide(color: AppColors.strokeColor),
                  right: BorderSide(color: day == daysInMonth.length - 1 ? AppColors.black : AppColors.transparent),
                ),
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    controller.getDayLetter(date.weekday),
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[700],
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    day.toString(),
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: isHoliday ? Colors.grey[600] : null,
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      }
    }
    return headers;
  }
}
