import 'package:excel_app/constants/app_assets.dart';
import 'package:excel_app/widget/app_asset_image.dart';
import 'package:excel_app/widget/custome_button_gradiant_widget.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';

class SelectUserTypeDialog extends StatefulWidget {
  const SelectUserTypeDialog({
    super.key,
    this.listData = const <String>[],
    this.title,
    this.selectedlistData = const <String>[],
    this.leaderRoll,
  });
  final List<String>? listData;
  final String? title;
  final List<String>? selectedlistData;
  final String? leaderRoll;
  @override
  State<SelectUserTypeDialog> createState() => _SelectUserTypeDialogState();
}

class _SelectUserTypeDialogState extends State<SelectUserTypeDialog> {
  final selectedValues = ValueNotifier<List<String>>([]);

  @override
  void initState() {
    selectedValues.value = widget.selectedlistData ?? [];
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return StatefulBuilder(
      builder: (context, setUpdate) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          child: SizedBox(
            width: 390,
            child: Padding(
              padding: const EdgeInsets.fromLTRB(20, 20, 20, 30),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    widget.title ?? '',
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                        ),
                  ),
                  const Gap(20),
                  ValueListenableBuilder<List<String>>(
                    valueListenable: selectedValues,
                    builder: (context, selected, _) {
                      return Flexible(
                        child: ConstrainedBox(
                          constraints: const BoxConstraints(
                            maxHeight: 290,
                          ),
                          child: ListView.builder(
                            shrinkWrap: true,
                            itemBuilder: (context, index) {
                              return InkWell(
                                onTap: () {
                                  if (widget.leaderRoll == widget.listData?[index]) return;
                                  setUpdate(
                                    () {
                                      if (selected.any((element) => element == widget.listData?[index])) {
                                        selectedValues.value = [...selected]..remove(widget.listData?[index]);
                                      } else {
                                        selectedValues.value = [...selected, widget.listData?[index] ?? ''];
                                      }
                                    },
                                  );
                                },
                                child: Padding(
                                  padding: const EdgeInsets.symmetric(vertical: 12),
                                  child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      AppAssetImage(
                                        selected.any((element) => element == widget.listData?[index])
                                            ? AppAssets.selectedCheckboxIcon
                                            : AppAssets.unselectedCheckboxIcon,
                                      ),
                                      const Gap(14),
                                      Text(
                                        widget.listData?[index] ?? '',
                                        style: Theme.of(context).textTheme.bodyMedium,
                                      ),
                                      const Gap(6),
                                    ],
                                  ),
                                ),
                              );
                            },
                            itemCount: widget.listData?.length,
                          ),
                        ),
                      );
                    },
                  ),
                  const Gap(18),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      CustomeButtonGradiantWidget(
                        buttonText: 'Cancel',
                        isUseContainerBorder: true,
                        width: 100,
                        height: 38,
                        onTap: () {
                          selectedValues.value = widget.selectedlistData ?? [];
                          Navigator.pop(context, selectedValues.value);
                        },
                      ),
                      const Gap(15),
                      CustomeButtonGradiantWidget(
                        buttonText: 'Add',
                        isGradient: true,
                        width: 100,
                        height: 38,
                        onTap: () {
                          Navigator.pop(context, selectedValues.value);
                        },
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}

class SampleUserModel {
  SampleUserModel({this.userType, this.id, this.name});
  final int? id;
  final String? name;
  final String? userType;
}
