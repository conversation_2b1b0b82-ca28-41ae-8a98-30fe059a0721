import 'package:excel_app/constants/app_assets.dart';
import 'package:excel_app/constants/app_colors.dart';
import 'package:excel_app/widget/action_button_widget.dart';
import 'package:excel_app/widget/app_asset_image.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:intl/intl.dart';

class DateStatusModel {
  DateStatusModel({required this.date, this.status});
  final String date;
  final String? status;
}

class HolidayListViewWidget extends StatelessWidget {
  const HolidayListViewWidget({
    required this.bgColor,
    required this.title,
    super.key,
    this.dates = const [],
    this.onDeleteTap,
  });
  final Color bgColor;
  final String title;
  final List<DateTime> dates;
  final void Function(DateTime)? onDeleteTap;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 16),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10),
            color: bgColor,
          ),
          child: Text(
            title,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600),
          ),
        ),
        ListView.builder(
          itemCount: dates.length,
          physics: const NeverScrollableScrollPhysics(),
          shrinkWrap: true,
          itemBuilder: (context, index) {
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  child: Row(
                    children: [
                      Expanded(
                        child: Text(
                          DateFormat('dd-MM-yyyy').format(dates[index]),
                          style: Theme.of(context).textTheme.titleSmall,
                        ),
                      ),
                      ActionButtonWidget(
                        toolTipMessage: 'Delete',
                        icon: const AppAssetImage(
                          AppAssets.deleteIcon,
                        ),
                        onTap: () {
                          onDeleteTap?.call(dates[index]);
                        },
                      ),
                    ],
                  ),
                ),
                Container(
                  height: 1,
                  color: AppColors.strokeColor,
                ),
              ],
            );
          },
        ),
        const Gap(20),
      ],
    );
  }
}
