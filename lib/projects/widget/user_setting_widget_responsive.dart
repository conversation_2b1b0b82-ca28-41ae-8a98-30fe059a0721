// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:developer';

import 'package:excel_app/constants/app_assets.dart';
import 'package:excel_app/constants/app_colors.dart';
import 'package:excel_app/projects/model/user_setting_model.dart';
import 'package:excel_app/utility/extentions/string_extentions.dart';
import 'package:excel_app/utility/helpers/utility.dart';
import 'package:excel_app/widget/app_asset_image.dart';
import 'package:excel_app/widget/app_text_form_field.dart';
import 'package:excel_app/widget/custom_network_image.dart';
import 'package:excel_app/widget/custome_button_gradiant_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:gap/gap.dart';

class UserSettingWidget extends StatefulWidget {
  const UserSettingWidget({
    super.key,
    this.index,
    this.onTap,
    this.isSelected = false,
    this.userSettingModel,
    this.isDeleteonTap,
    this.oneEyesTap,
    this.startDate,
    this.endDate,
  });
  final int? index;
  final bool isSelected;
  final void Function()? onTap;
  final void Function()? oneEyesTap;
  final void Function()? isDeleteonTap;
  final UserSettingModel? userSettingModel;
  final DateTime? startDate;
  final DateTime? endDate;

  @override
  State<UserSettingWidget> createState() => _UserSettingWidgetState();
}

class _UserSettingWidgetState extends State<UserSettingWidget> {
  final totalAssignDate = ValueNotifier<int?>(null);

  @override
  void initState() {
    super.initState();

    // Ensure at least 3 date fields by default
    if ((widget.userSettingModel?.assignDateAndControllerModel?.length ?? 0) < 3) {
      final currentList = widget.userSettingModel?.assignDateAndControllerModel ?? [];
      while (currentList.length < 3) {
        currentList.add(
          AssignDateAndControllerModel(
            assignDateController: TextEditingController(),
          ),
        );
      }
      widget.userSettingModel?.assignDateAndControllerModel = currentList;
    }

    totalAssignDate.value = widget.userSettingModel?.assignDateAndControllerModel?.length;
  }

  /// Check if user has any assigned dates (dates that are actually selected)
  bool _hasAssignedDates() {
    return widget.userSettingModel?.assignDateAndControllerModel?.any(
          (dateModel) => dateModel.assignDate != null && dateModel.assignDateController.text.isNotEmpty,
        ) ??
        false;
  }

  /// Check if the first 3 date fields have assigned dates
  bool _areFirstThreeDatesAssigned() {
    final dateList = widget.userSettingModel?.assignDateAndControllerModel ?? [];
    if (dateList.length < 3) return false;

    // Check if first 3 dates are assigned
    for (var i = 0; i < 3; i++) {
      if (dateList[i].assignDate == null || dateList[i].assignDateController.text.isEmpty) {
        return false;
      }
    }
    return true;
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 1200;
    final isVerySmallScreen = screenWidth < 800;

    return Container(
      decoration: BoxDecoration(
        color: (widget.index! % 2) == 0 ? AppColors.white : AppColors.tableGray,
        border: Border.all(color: AppColors.strokeColor.withOpacity2(0.5)),
      ),
      child: Padding(
        padding: EdgeInsets.all(isVerySmallScreen ? 8 : 15),
        child: isVerySmallScreen ? _buildVerticalLayout(context) : _buildHorizontalLayout(context, isSmallScreen),
      ),
    );
  }

  Widget _buildVerticalLayout(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // User info section
        Row(
          children: [
            SizedBox(
              height: 24,
              width: 24,
              child: ClipRRect(
                borderRadius: BorderRadius.circular(100),
                child: CustomNetworkImage(
                  imageUrl: widget.userSettingModel?.userModel?.imagePath ?? '',
                  height: 24,
                  width: 24,
                ),
              ),
            ),
            const Gap(8),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '${widget.userSettingModel?.userModel?.name?.capitalizeFirstofEach}',
                    overflow: TextOverflow.ellipsis,
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                  Text(
                    widget.userSettingModel?.userModel?.userRoleViewString.capitalizeFirstofEach ??
                        ''.capitalizeFirstofEach,
                    overflow: TextOverflow.ellipsis,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(color: AppColors.subText),
                  ),
                ],
              ),
            ),
            // Action buttons
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                InkWell(
                  onTap: widget.oneEyesTap,
                  child: const AppAssetImage(
                    AppAssets.eyeOpenIcon,
                    height: 24,
                    width: 24,
                  ),
                ),
                const Gap(8),
                InkWell(
                  onTap: widget.onTap,
                  child: AppAssetImage(
                    (widget.isSelected || _hasAssignedDates())
                        ? AppAssets.expentionUpperIcon
                        : AppAssets.expentionLowerIcon,
                    height: 20,
                    width: 20,
                  ),
                ),
              ],
            ),
          ],
        ),
        const Gap(8),
        // Leader badge and delete button
        Row(
          children: [
            if (widget.userSettingModel?.projectLeaderId == widget.userSettingModel?.userModel?.id) ...[
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: AppColors.skinColor,
                  borderRadius: BorderRadius.circular(5),
                ),
                child: Text(
                  'Leader + User',
                  overflow: TextOverflow.ellipsis,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: AppColors.dark,
                      ),
                ),
              ),
            ],
            // const Spacer(),
            if (widget.userSettingModel?.projectLeaderId != widget.userSettingModel?.userModel?.id)
              InkWell(
                onTap: widget.isDeleteonTap,
                child: const AppAssetImage(
                  AppAssets.deleteIcon,
                  height: 18,
                  width: 18,
                ),
              ),
          ],
        ),
        const Gap(12),
        // Form fields in vertical layout
        _buildFormFields(context, true),
      ],
    );
  }

  Widget _buildHorizontalLayout(BuildContext context, bool isSmallScreen) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        // Left side - User info with fixed width for better alignment
        SizedBox(
          width: isSmallScreen ? 180 : 220,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                children: [
                  SizedBox(
                    height: 24,
                    width: 24,
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(100),
                      child: CustomNetworkImage(
                        imageUrl: widget.userSettingModel?.userModel?.imagePath ?? '',
                        height: 24,
                        width: 24,
                      ),
                    ),
                  ),
                  const Gap(8),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '${widget.userSettingModel?.userModel?.name?.capitalizeFirstofEach}',
                          overflow: TextOverflow.ellipsis,
                          style: Theme.of(context).textTheme.bodyMedium,
                        ),
                        Text(
                          widget.userSettingModel?.userModel?.userRoleViewString.capitalizeFirstofEach ??
                              ''.capitalizeFirstofEach,
                          overflow: TextOverflow.ellipsis,
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(color: AppColors.subText),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const Gap(8),
              Row(
                children: [
                  if (widget.userSettingModel?.projectLeaderId == widget.userSettingModel?.userModel?.id) ...[
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                      decoration: BoxDecoration(
                        color: AppColors.skinColor,
                        borderRadius: BorderRadius.circular(5),
                      ),
                      child: Text(
                        'Leader + User',
                        overflow: TextOverflow.ellipsis,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: AppColors.dark,
                            ),
                      ),
                    ),
                    const Gap(8),
                  ],
                  if (widget.userSettingModel?.projectLeaderId != widget.userSettingModel?.userModel?.id)
                    InkWell(
                      onTap: widget.isDeleteonTap,
                      child: const AppAssetImage(
                        AppAssets.deleteIcon,
                        height: 18,
                        width: 18,
                      ),
                    ),
                ],
              ),
            ],
          ),
        ),
        const Gap(12),
        Expanded(
          flex: isSmallScreen ? 5 : 4,
          child: _buildFormFields(context, false),
        ),
        const Gap(8),
        InkWell(
          onTap: widget.oneEyesTap,
          child: const AppAssetImage(
            AppAssets.eyeOpenIcon,
            height: 28,
            width: 28,
          ),
        ),
        const Gap(16),
        InkWell(
          onTap: widget.onTap,
          child: AppAssetImage(
            (widget.isSelected || _hasAssignedDates()) ? AppAssets.expentionUpperIcon : AppAssets.expentionLowerIcon,
            height: 24,
            width: 24,
          ),
        ),
      ],
    );
  }

  Widget _buildFormFields(BuildContext context, bool isVerticalLayout) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 1200;
    final isVerySmallScreen = screenWidth < 800;

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Shift fields
        if (isVerticalLayout || isVerySmallScreen)
          _buildVerticalShiftFields(context)
        else
          _buildHorizontalShiftFields(context, isSmallScreen),

        // Date assignment section
        if (widget.isSelected || _hasAssignedDates()) ...[
          const Gap(20),
          _buildDateAssignmentSection(context, isVerticalLayout),
        ],
      ],
    );
  }

  Widget _buildVerticalShiftFields(BuildContext context) {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: AppTextFormField(
                title: 'Max Shift',
                keyboardType: TextInputType.number,
                errorMaxLines: 2,
                inputFormatters: [
                  FilteringTextInputFormatter.digitsOnly,
                  LengthLimitingTextInputFormatter(3),
                ],
                hintStyle: Theme.of(context).textTheme.bodyMedium?.copyWith(color: AppColors.subText),
                contentPadding: const EdgeInsets.all(10),
                controller: widget.userSettingModel?.maxShift,
                validator: (p0) {
                  if (widget.userSettingModel?.maxShift.text.isNotEmpty ?? false) {
                    if ((int.tryParse(widget.userSettingModel?.maxShift.text ?? '') ?? 0) <
                        (int.tryParse(widget.userSettingModel?.minShift.text ?? '') ?? 0)) {
                      return 'Lesser than Min Shift';
                    }
                  }
                  return null;
                },
              ),
            ),
            const Gap(12),
            Expanded(
              child: AppTextFormField(
                title: 'Min Shift',
                keyboardType: TextInputType.number,
                inputFormatters: [
                  FilteringTextInputFormatter.digitsOnly,
                  LengthLimitingTextInputFormatter(3),
                ],
                hintStyle: Theme.of(context).textTheme.bodyMedium?.copyWith(color: AppColors.subText),
                contentPadding: const EdgeInsets.all(10),
                controller: widget.userSettingModel?.minShift,
              ),
            ),
          ],
        ),
        const Gap(12),
        Row(
          children: [
            Expanded(
              child: AppTextFormField(
                title: 'Max Weekend Shift',
                keyboardType: TextInputType.number,
                errorMaxLines: 2,
                inputFormatters: [
                  FilteringTextInputFormatter.digitsOnly,
                  LengthLimitingTextInputFormatter(3),
                ],
                validator: (p0) {
                  if (widget.userSettingModel?.maxWeekEndShift.text.isNotEmpty ?? false) {
                    if ((int.tryParse(widget.userSettingModel?.maxWeekEndShift.text ?? '') ?? 0) <
                        (int.tryParse(widget.userSettingModel?.minWeekEndShift.text ?? '') ?? 0)) {
                      return 'Lesser than Min Weekend Shift';
                    }
                    return null;
                  }
                  return null;
                },
                hintStyle: Theme.of(context).textTheme.bodyMedium?.copyWith(color: AppColors.subText),
                contentPadding: const EdgeInsets.all(10),
                controller: widget.userSettingModel?.maxWeekEndShift,
              ),
            ),
            const Gap(12),
            Expanded(
              child: AppTextFormField(
                title: 'Min Weekend Shift',
                keyboardType: TextInputType.number,
                inputFormatters: [
                  FilteringTextInputFormatter.digitsOnly,
                  LengthLimitingTextInputFormatter(3),
                ],
                hintStyle: Theme.of(context).textTheme.bodyMedium?.copyWith(color: AppColors.subText),
                contentPadding: const EdgeInsets.all(10),
                controller: widget.userSettingModel?.minWeekEndShift,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildHorizontalShiftFields(BuildContext context, bool isSmallScreen) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isVerySmallScreen = screenWidth < 900;

    // For very small screens, use vertical layout instead of horizontal scroll
    if (isVerySmallScreen) {
      return Column(
        children: [
          Row(
            children: [
              Expanded(
                child: AppTextFormField(
                  title: 'Max Shift',
                  keyboardType: TextInputType.number,
                  errorMaxLines: 2,
                  inputFormatters: [
                    FilteringTextInputFormatter.digitsOnly,
                    LengthLimitingTextInputFormatter(3),
                  ],
                  hintStyle: Theme.of(context).textTheme.bodyMedium?.copyWith(color: AppColors.subText),
                  contentPadding: const EdgeInsets.all(10),
                  controller: widget.userSettingModel?.maxShift,
                  validator: (p0) {
                    if (widget.userSettingModel?.maxShift.text.isNotEmpty ?? false) {
                      if ((int.tryParse(widget.userSettingModel?.maxShift.text ?? '') ?? 0) <
                          (int.tryParse(widget.userSettingModel?.minShift.text ?? '') ?? 0)) {
                        return 'Lesser than Min Shift';
                      }
                    }
                    return null;
                  },
                ),
              ),
              const Gap(12),
              Expanded(
                child: AppTextFormField(
                  title: 'Min Shift',
                  keyboardType: TextInputType.number,
                  inputFormatters: [
                    FilteringTextInputFormatter.digitsOnly,
                    LengthLimitingTextInputFormatter(3),
                  ],
                  hintStyle: Theme.of(context).textTheme.bodyMedium?.copyWith(color: AppColors.subText),
                  contentPadding: const EdgeInsets.all(10),
                  controller: widget.userSettingModel?.minShift,
                ),
              ),
            ],
          ),
          const Gap(12),
          Row(
            children: [
              Expanded(
                child: AppTextFormField(
                  title: 'Max Weekend Shift',
                  keyboardType: TextInputType.number,
                  errorMaxLines: 2,
                  inputFormatters: [
                    FilteringTextInputFormatter.digitsOnly,
                    LengthLimitingTextInputFormatter(3),
                  ],
                  validator: (p0) {
                    if (widget.userSettingModel?.maxWeekEndShift.text.isNotEmpty ?? false) {
                      if ((int.tryParse(widget.userSettingModel?.maxWeekEndShift.text ?? '') ?? 0) <
                          (int.tryParse(widget.userSettingModel?.minWeekEndShift.text ?? '') ?? 0)) {
                        return 'Lesser than Min Weekend Shift';
                      }
                      return null;
                    }
                    return null;
                  },
                  hintStyle: Theme.of(context).textTheme.bodyMedium?.copyWith(color: AppColors.subText),
                  contentPadding: const EdgeInsets.all(10),
                  controller: widget.userSettingModel?.maxWeekEndShift,
                ),
              ),
              const Gap(12),
              Expanded(
                child: AppTextFormField(
                  title: 'Min Weekend Shift',
                  keyboardType: TextInputType.number,
                  inputFormatters: [
                    FilteringTextInputFormatter.digitsOnly,
                    LengthLimitingTextInputFormatter(3),
                  ],
                  hintStyle: Theme.of(context).textTheme.bodyMedium?.copyWith(color: AppColors.subText),
                  contentPadding: const EdgeInsets.all(10),
                  controller: widget.userSettingModel?.minWeekEndShift,
                ),
              ),
            ],
          ),
        ],
      );
    }

    // For larger screens, use horizontal layout with proper spacing
    final fieldWidth = isSmallScreen ? 100.0 : 114.0;
    final weekendFieldWidth = isSmallScreen ? 130.0 : 156.0;
    final gap = isSmallScreen ? 12.0 : 20.0;

    return Wrap(
      spacing: gap,
      runSpacing: 12,
      children: [
        AppTextFormField(
          maxWidth: fieldWidth,
          title: 'Max Shift',
          keyboardType: TextInputType.number,
          errorMaxLines: 2,
          inputFormatters: [
            FilteringTextInputFormatter.digitsOnly,
            LengthLimitingTextInputFormatter(3),
          ],
          hintStyle: Theme.of(context).textTheme.bodyMedium?.copyWith(color: AppColors.subText),
          contentPadding: const EdgeInsets.all(10),
          controller: widget.userSettingModel?.maxShift,
          validator: (p0) {
            if (widget.userSettingModel?.maxShift.text.isNotEmpty ?? false) {
              if ((int.tryParse(widget.userSettingModel?.maxShift.text ?? '') ?? 0) <
                  (int.tryParse(widget.userSettingModel?.minShift.text ?? '') ?? 0)) {
                return 'Lesser than Min Shift';
              }
            }
            return null;
          },
        ),
        AppTextFormField(
          maxWidth: fieldWidth,
          title: 'Min Shift',
          keyboardType: TextInputType.number,
          inputFormatters: [
            FilteringTextInputFormatter.digitsOnly,
            LengthLimitingTextInputFormatter(3),
          ],
          hintStyle: Theme.of(context).textTheme.bodyMedium?.copyWith(color: AppColors.subText),
          contentPadding: const EdgeInsets.all(10),
          controller: widget.userSettingModel?.minShift,
        ),
        AppTextFormField(
          maxWidth: weekendFieldWidth,
          title: 'Max Weekend Shift',
          keyboardType: TextInputType.number,
          errorMaxLines: 2,
          inputFormatters: [
            FilteringTextInputFormatter.digitsOnly,
            LengthLimitingTextInputFormatter(3),
          ],
          validator: (p0) {
            if (widget.userSettingModel?.maxWeekEndShift.text.isNotEmpty ?? false) {
              if ((int.tryParse(widget.userSettingModel?.maxWeekEndShift.text ?? '') ?? 0) <
                  (int.tryParse(widget.userSettingModel?.minWeekEndShift.text ?? '') ?? 0)) {
                return 'Lesser than Min Weekend Shift';
              }
              return null;
            }
            return null;
          },
          hintStyle: Theme.of(context).textTheme.bodyMedium?.copyWith(color: AppColors.subText),
          contentPadding: const EdgeInsets.all(10),
          controller: widget.userSettingModel?.maxWeekEndShift,
        ),
        AppTextFormField(
          maxWidth: weekendFieldWidth,
          title: 'Min Weekend Shift',
          keyboardType: TextInputType.number,
          inputFormatters: [
            FilteringTextInputFormatter.digitsOnly,
            LengthLimitingTextInputFormatter(3),
          ],
          hintStyle: Theme.of(context).textTheme.bodyMedium?.copyWith(color: AppColors.subText),
          contentPadding: const EdgeInsets.all(10),
          controller: widget.userSettingModel?.minWeekEndShift,
        ),
      ],
    );
  }

  Widget _buildDateAssignmentSection(BuildContext context, bool isVerticalLayout) {
    return Column(
      children: [
        // Text labels centered
        Column(
          children: [
            Text(
              'Automatic assignation',
              overflow: TextOverflow.ellipsis,
              style: Theme.of(context).textTheme.bodySmall,
            ),
            const Gap(2),
            Text(
              'Specific night to work',
              overflow: TextOverflow.ellipsis,
              style: Theme.of(context).textTheme.labelSmall?.copyWith(
                    fontWeight: FontWeight.w500,
                    color: AppColors.subText,
                  ),
            ),
          ],
        ),
        const Gap(20),
        // Dynamic date selection with improved overflow handling
        Column(
          children: [
              // Date selection fields with scrollable container for overflow protection
              if (widget.userSettingModel?.assignDateAndControllerModel?.isNotEmpty ?? false)
                Container(
                  constraints: const BoxConstraints(
                    maxWidth: 500,
                    maxHeight: 160, // Limit height to prevent vertical overflow
                  ),
                  child: SingleChildScrollView(
                    child: Wrap(
                      spacing: 8,
                      runSpacing: 8,
                      children: List.generate(
                        widget.userSettingModel!.assignDateAndControllerModel!.length,
                        (index) {
                          final assignDateInfo = widget.userSettingModel!.assignDateAndControllerModel![index];

                          return Stack(
                            alignment: Alignment.topRight,
                            clipBehavior: Clip.none,
                            children: [
                              AppTextFormField(
                                maxWidth: 114,
                                maxHeight: 36,
                                readOnly: true,
                                onTap: () async {
                                  final date = await Utility.datePicker(
                                    context: context,
                                    firstDate: widget.startDate != null && widget.startDate!.isAfter(DateTime.now())
                                        ? widget.startDate
                                        : DateTime.now(),
                                    initialDate: widget.startDate == null || widget.startDate!.isBefore(DateTime.now())
                                        ? DateTime.now()
                                        : widget.startDate,
                                    lastdate: widget.endDate,
                                  );
                                  if (date != null) {
                                    // Check if the selected date already exists
                                    final isDateAlreadyExit =
                                        widget.userSettingModel!.assignDateAndControllerModel!.any(
                                      (e) => e.assignDate != null && e.assignDate!.isAtSameMomentAs(date),
                                    );

                                    if (isDateAlreadyExit) {
                                      Utility.toast(
                                        message: 'Date already selected. Choose a different date.',
                                      );
                                      return;
                                    }

                                    assignDateInfo.assignDate = date;
                                    assignDateInfo.assignDateController.text = Utility().dateYearFormat(date: date);
                                    setState(() {});
                                  }
                                },
                                contentPadding: const EdgeInsets.all(10),
                                controller: assignDateInfo.assignDateController,
                              ),
                              // Remove button logic:
                              // - Show close icon for selected dates in first 3 fields (to clear date)
                              // - Show remove icon for extra fields beyond 3 (to remove field)
                              if (assignDateInfo.assignDateController.text.isNotEmpty ||
                                  widget.userSettingModel!.assignDateAndControllerModel!.length > 3)
                                Positioned(
                                  top: -8,
                                  right: -8,
                                  child: InkWell(
                                    onTap: () {
                                      setState(() {
                                        // If this is one of the first 3 fields, just clear the date
                                        if (index < 3) {
                                          assignDateInfo.assignDate = null;
                                          assignDateInfo.assignDateController.clear();
                                        } else {
                                          // If this is an extra field (beyond 3), remove the entire field
                                          widget.userSettingModel!.assignDateAndControllerModel!.removeAt(index);
                                          totalAssignDate.value =
                                              widget.userSettingModel!.assignDateAndControllerModel!.length;
                                        }
                                      });
                                    },
                                    child: Container(
                                      width: 20,
                                      height: 20,
                                      decoration: const BoxDecoration(
                                        color: Colors.red,
                                        shape: BoxShape.circle,
                                      ),
                                      child: const Icon(
                                        Icons.close,
                                        size: 12,
                                        color: Colors.white,
                                      ),
                                    ),
                                  ),
                                ),
                            ],
                          );
                        },
                      ),
                    ),
                  ),
                ),
              const Gap(12),
              // Add button
              ValueListenableBuilder(
                valueListenable: totalAssignDate,
                builder: (context, totalDateLength, _) {
                  log('totalDateLength: $totalDateLength');
                  return CustomeButtonGradiantWidget(
                    buttonText: 'Add Date',
                    height: 40,
                    width: 120,
                    isGradient: true,
                    isDisabled: !_areFirstThreeDatesAssigned(),
                    onTap: _areFirstThreeDatesAssigned()
                        ? () {
                            setState(() {
                              widget.userSettingModel?.assignDateAndControllerModel?.add(
                                AssignDateAndControllerModel(
                                  assignDateController: TextEditingController(),
                                ),
                              );
                              totalAssignDate.value = widget.userSettingModel?.assignDateAndControllerModel?.length;
                            });
                          }
                        : null,
                  );
                },
              ),
            ],
          ),
        ,,,,),
      ],
    );
  }
}
