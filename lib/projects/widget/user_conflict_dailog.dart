import 'package:excel_app/constants/app_colors.dart';
import 'package:excel_app/projects/model/user_project_calendar_date_model.dart';
import 'package:excel_app/widget/custome_button_gradiant_widget.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';

class UserConflictAlertDailog extends StatefulWidget {
  const UserConflictAlertDailog({super.key, this.listConflict});
  final List<UserProjectCalendarDateModel>? listConflict;

  @override
  State<UserConflictAlertDailog> createState() => _UserConflictAlertDailogState();
}

class _UserConflictAlertDailogState extends State<UserConflictAlertDailog> {
  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20),
      ),
      child: Container(
        width: 500,
        constraints: const BoxConstraints(
          maxHeight: 500,
          minHeight: 100,
        ),
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 30, horizontal: 30),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text(
                'Conflict Details',
                style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 20),
              Flexible(
                child: ListView.separated(
                  shrinkWrap: true,
                  itemCount: widget.listConflict?.length ?? 0,
                  separatorBuilder: (context, index) => const Gap(10),
                  itemBuilder: (context, index) {
                    final conflict = widget.listConflict?[index];
                    if (conflict == null) return const SizedBox.shrink();
                    return InkWell(
                      onTap: () {
                        context.pop(conflict);
                      },
                      child: Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Colors.transparent,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: AppColors.strokeColor,
                          ),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Text(
                                  'Date: ',
                                  style: Theme.of(context).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.w600),
                                ),
                                Text(
                                  conflict.date != null ? DateFormat('MMM-dd-yyyy').format(conflict.date!) : 'N/A',
                                  style: Theme.of(context).textTheme.bodyMedium,
                                ),
                              ],
                            ),
                            if (conflict.metaData != null && conflict.metaData!.reason != null) ...[
                              const Gap(8),
                              Row(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    'Reason: ',
                                    style:
                                        Theme.of(context).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.w600),
                                  ),
                                  Expanded(
                                    child: Text(
                                      conflict.metaData!.reason!,
                                      style: Theme.of(context).textTheme.bodyMedium,
                                    ),
                                  ),
                                ],
                              ),
                            ],
                            if (conflict.metaData != null && conflict.metaData!.solution != null) ...[
                              const Gap(8),
                              Row(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    'Solution: ',
                                    style:
                                        Theme.of(context).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.w600),
                                  ),
                                  Expanded(
                                    child: Text(
                                      conflict.metaData!.solution!,
                                      style: Theme.of(context).textTheme.bodyMedium,
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ],
                        ),
                      ),
                    );
                  },
                ),
              ),
              const SizedBox(height: 20),
              CustomeButtonGradiantWidget(
                buttonText: 'Cancel',
                isUseContainerBorder: true,
                width: 100,
                height: 38,
                onTap: () => Navigator.pop(context),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
