import 'package:excel_app/widget/custome_button_gradiant_widget.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';

class UserAutoAssignConflictAlertDailog extends StatelessWidget {
  const UserAutoAssignConflictAlertDailog({
    super.key,
    this.message,
    this.autoAssignInProgress = false,
  });

  final String? message;
  final bool autoAssignInProgress;

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(10),
      ),
      child: MouseRegion(
        child: Container(
          width: 500,
          constraints: BoxConstraints(
            maxHeight: 300,
            minHeight: autoAssignInProgress ? 100 : 200,
          ),
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 30, horizontal: 30),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                if (!autoAssignInProgress) ...[
                  Text(
                    'Auto Assign Conflict',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  const Gap(20),
                ],
                Text(
                  message ?? 'No reason provided',
                  style: Theme.of(context).textTheme.titleSmall,
                  textAlign: TextAlign.center,
                ),
                const Gap(20),
                CustomeButtonGradiantWidget(
                  isUseContainerBorder: true,
                  width: 100,
                  height: 38,
                  onTap: () {
                    // if (autoAssignInProgress) {
                    //   context.goNamed(AppRoutes.projects.name);
                    // }
                    Navigator.pop(context);
                  },
                  child: const Text('OK'),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
