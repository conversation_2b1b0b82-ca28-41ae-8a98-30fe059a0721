import 'package:collection/collection.dart';
import 'package:excel_app/injector/injector.dart';
import 'package:excel_app/projects/model/user_project_calendar_date_model.dart';
import 'package:excel_app/projects/repository/i_project_repository.dart';
import 'package:excel_app/users/model/user_model.dart';
import 'package:excel_app/utility/helpers/utility.dart';
import 'package:flutter/material.dart' hide MetaData;

/// Controller for the manual assignment dialog
class ManualAssignController {
  ManualAssignController() {
    // Initialize history with empty list
    _saveToHistory();
  }

  // Track manually assigned cells
  final manualAssignments = ValueNotifier<List<UserProjectCalendarDateModel>>([]);

  // Track added and removed assignments separately
  final addedAssignments = ValueNotifier<List<UserProjectCalendarDateModel>>([]);
  final removedAssignments = ValueNotifier<List<UserProjectCalendarDateModel>>([]);

  // Counters for added and removed assignments
  final addedCount = ValueNotifier<int>(0);
  final removedCount = ValueNotifier<int>(0);

  // Track history for undo/redo
  final _history = <List<UserProjectCalendarDateModel>>[];
  int _currentHistoryIndex = -1;

  final isLoading = ValueNotifier<bool>(false);
  final canUndo = ValueNotifier<bool>(false);
  final canRedo = ValueNotifier<bool>(false);

  /// Add or remove a manual assignment
  void addManualAssignment(DateTime date, UserModel user, BuildContext context) {
    // Check if this date is already assigned manually
    final existingManualIndex = manualAssignments.value.indexWhere(
      (element) =>
          element.date?.year == date.year &&
          element.date?.month == date.month &&
          element.date?.day == date.day &&
          element.userId == user.id,
    );

    // Check if this date has an existing assignment from userProjectAvailableDates
    final existingAssignment = user.userProjectAvailableDates?.firstWhereOrNull(
      (element) =>
          element.date?.year == date.year && element.date?.month == date.month && element.date?.day == date.day,
    );

    // Create a copy of the current lists to modify
    final newManualAssignments = List<UserProjectCalendarDateModel>.from(manualAssignments.value);
    final newAddedAssignments = List<UserProjectCalendarDateModel>.from(addedAssignments.value);
    final newRemovedAssignments = List<UserProjectCalendarDateModel>.from(removedAssignments.value);
    var newAddedCount = addedCount.value;
    var newRemovedCount = removedCount.value;

    if (existingManualIndex >= 0) {
      // Check if this was an added assignment or a removal marker
      final assignment = manualAssignments.value[existingManualIndex];
      if ((assignment.leaveCategoryId ?? 0) > 0) {
        // This was an added assignment, so decrement the counter
        newAddedCount--;
        // Remove from added assignments list
        newAddedAssignments.removeWhere(
          (element) =>
              element.date?.year == date.year &&
              element.date?.month == date.month &&
              element.date?.day == date.day &&
              element.userId == user.id,
        );
      } else {
        // This was a removal marker, so decrement the counter
        newRemovedCount--;
        // Remove from removed assignments list
        newRemovedAssignments.removeWhere(
          (element) =>
              element.date?.year == date.year &&
              element.date?.month == date.month &&
              element.date?.day == date.day &&
              element.userId == user.id,
        );
      }

      // Remove the manual assignment from the main list
      newManualAssignments.removeAt(existingManualIndex);
    } else if (existingAssignment != null) {
      // For existing assignments from userProjectAvailableDates, we need to track that we've removed it
      // Create a removal marker
      final removalMarker = UserProjectCalendarDateModel(
        userId: user.id,
        date: date,
        // Use a negative leaveCategoryId to indicate removal
        leaveCategoryId: -1, // Special value to indicate removal
        // Store the original leaveCategoryId for reference
        metaData: MetaData(
          reason: 'Removed assignment',
          solution: 'Original ID: ${existingAssignment.leaveCategoryId?.toString() ?? '0'}',
        ),
      );

      // Add to main list
      newManualAssignments.add(removalMarker);

      // Add to removed assignments list and increment counter
      newRemovedAssignments.add(removalMarker);
      newRemovedCount++;
    } else {
      // Create a new assignment
      final newAssignment = UserProjectCalendarDateModel(
        userId: user.id,
        date: date,
        // Use a default leave category ID for "Present"
        leaveCategoryId: 1, // Assuming 1 is for "Present"
      );

      // Add to main list
      newManualAssignments.add(newAssignment);

      // Add to added assignments list and increment counter
      newAddedAssignments.add(newAssignment);
      newAddedCount++;
    }

    // Update all ValueNotifiers
    manualAssignments.value = newManualAssignments;
    addedAssignments.value = newAddedAssignments;
    removedAssignments.value = newRemovedAssignments;
    addedCount.value = newAddedCount;
    removedCount.value = newRemovedCount;

    // Save to history after each change
    _saveToHistory();
  }

  /// Handle the done button press
  Future<void> handleDonePressed(
    BuildContext context,
    String projectId,
    VoidCallback onSuccess,
  ) async {
    if (manualAssignments.value.isEmpty) {
      // No assignments were made, just return
      return;
    }

    // Set loading state
    isLoading.value = true;

    try {
      // Get the project repository
      final projectRepository = getIt<IProjectRepository>();

      // Call the API
      final failOrSuccess = await projectRepository.manualAssignShift(
        projectId: projectId,
        assignments: manualAssignments.value,
      );

      failOrSuccess.fold(
        (failure) {
          // Handle error
          isLoading.value = false;
          Utility.toast(message: failure.message);
        },
        (response) {
          // Handle success
          isLoading.value = false;

          // Show success message
          Utility.toast(message: response.message ?? 'Manual assignment completed successfully');

          // Close the dialog
          Navigator.of(context).pop();

          // Call success callback to reload the planning details page
          onSuccess();
        },
      );
    } on Exception catch (e) {
      // Handle unexpected errors
      isLoading.value = false;
      Utility.toast(message: 'An error occurred: $e');
    }
  }

  /// Handle undo button press
  void handleUndo(BuildContext context) {
    if (_currentHistoryIndex > 0) {
      _currentHistoryIndex--;
      _restoreFromHistory(context);
    }
  }

  /// Handle redo button press
  void handleRedo(BuildContext context) {
    if (_currentHistoryIndex < _history.length - 1) {
      _currentHistoryIndex++;
      _restoreFromHistory(context);
    }
  }

  /// Save current state to history
  void _saveToHistory() {
    // If we're not at the end of the history, remove all future states
    if (_currentHistoryIndex < _history.length - 1) {
      _history.removeRange(_currentHistoryIndex + 1, _history.length);
    }

    // Create a deep copy of the current assignments
    final copy = manualAssignments.value
        .map(
          (assignment) => UserProjectCalendarDateModel(
            userId: assignment.userId,
            date: assignment.date,
            leaveCategoryId: assignment.leaveCategoryId,
            metaData: assignment.metaData,
          ),
        )
        .toList();

    // Add to history
    _history.add(copy);
    _currentHistoryIndex = _history.length - 1;

    // Update undo/redo state
    canUndo.value = _currentHistoryIndex > 0;
    canRedo.value = false; // We just added a new state, so can't redo
  }

  /// Restore state from history
  void _restoreFromHistory(BuildContext context) {
    // Clear current assignments and add from history
    final newManualAssignments = List<UserProjectCalendarDateModel>.from(_history[_currentHistoryIndex]);
    manualAssignments.value = newManualAssignments;

    // Clear added and removed assignments lists
    final newAddedAssignments = <UserProjectCalendarDateModel>[];
    final newRemovedAssignments = <UserProjectCalendarDateModel>[];

    // Reset counters
    var newAddedCount = 0;
    var newRemovedCount = 0;

    // Recalculate added and removed assignments
    for (final assignment in newManualAssignments) {
      if ((assignment.leaveCategoryId ?? 0) > 0) {
        // This is an added assignment
        newAddedAssignments.add(assignment);
        newAddedCount++;
      } else if ((assignment.leaveCategoryId ?? 0) < 0) {
        // This is a removal marker
        newRemovedAssignments.add(assignment);
        newRemovedCount++;
      }
    }

    // Update all ValueNotifiers
    addedAssignments.value = newAddedAssignments;
    removedAssignments.value = newRemovedAssignments;
    addedCount.value = newAddedCount;
    removedCount.value = newRemovedCount;

    // Update undo/redo state
    canUndo.value = _currentHistoryIndex > 0;
    canRedo.value = _currentHistoryIndex < _history.length - 1;
  }

  /// Get the number of days in the month
  int getDaysInMonth(DateTime currentMonth) {
    return DateTime(
      currentMonth.year,
      currentMonth.month + 1,
      0,
    ).day;
  }

  /// Get the day letter for a weekday
  String getDayLetter(int weekday) {
    const days = ['', 'M', 'T', 'W', 'T', 'F', 'S', 'S'];
    return days[weekday];
  }

  /// Dispose all ValueNotifiers
  void dispose() {
    manualAssignments.dispose();
    addedAssignments.dispose();
    removedAssignments.dispose();
    addedCount.dispose();
    removedCount.dispose();
    isLoading.dispose();
    canUndo.dispose();
    canRedo.dispose();
  }
}
