import 'package:equatable/equatable.dart';
import 'package:excel_app/projects/model/project_rules_category_model.dart';

class ProjectListAvailabiltyResponse extends Equatable {
  const ProjectListAvailabiltyResponse({
    this.message,
    this.status,
    this.data = const <ProjectRuleCategoryModel>[],
  });

  factory ProjectListAvailabiltyResponse.fromJson(Map<String, dynamic> json) {
    return ProjectListAvailabiltyResponse(
      message: json['message'] as String?,
      status: json['status'] as String?,
      data: json['data'] == null
          ? []
          : List<ProjectRuleCategoryModel>.from(
              (json['data'] as List<dynamic>).map(
                (e) => ProjectRuleCategoryModel.fromJson(e as Map<String, dynamic>),
              ),
            ),
    );
  }

  final String? message;
  final String? status;

  final List<ProjectRuleCategoryModel> data;

  Map<String, dynamic> toJson() => {
        'message': message,
        'status': status,
        'data': data.map((x) => x.toJson()).toList(),
      };

  @override
  List<Object?> get props => [
        message,
        status,
        data,
      ];
}
