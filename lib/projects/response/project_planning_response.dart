import 'package:equatable/equatable.dart';
import 'package:excel_app/projects/model/leader_model.dart';
import 'package:excel_app/projects/model/project_planning_model.dart';

class ProjectPlanningResponse extends Equatable {
  const ProjectPlanningResponse({
    this.data = const [],
    this.message,
    this.status,
    this.assignmentStatus,
    this.isApproved,
    this.leader,
  });

  factory ProjectPlanningResponse.fromJson(Map<String, dynamic> json) {
    return ProjectPlanningResponse(
      data: json['data'] == null
          ? []
          : List<ProjectPlanningModel>.from(
              (json['data'] as List<dynamic>).map(
                (e) => ProjectPlanningModel.fromJson(e as Map<String, dynamic>),
              ),
            ),
      leader: json['leader'] != null ? LeaderModel.fromJson(json['leader'] as Map<String, dynamic>) : null,
      message: json['message'] as String?,
      status: json['status'] as String?,
      assignmentStatus: json['assignment_status'] as String?,
      isApproved: json['is_approved'] as int?,
    );
  }

  final List<ProjectPlanningModel> data;
  final String? message;
  final String? status;
  final String? assignmentStatus;
  final int? isApproved;
  final LeaderModel? leader;

  Map<String, dynamic> toJson() => {
        'data': data.map((x) => x.toJson()).toList(),
        'message': message,
        'status': status,
        'assignment_status': assignmentStatus,
        'is_approved': isApproved,
        'leader': leader,
      };

  @override
  List<Object?> get props => [
        data,
        message,
        status,
        assignmentStatus,
        isApproved,
      ];
}
