import 'package:equatable/equatable.dart';
import 'package:excel_app/projects/model/project_model.dart';

class ProjectDetailResponse extends Equatable {
  const ProjectDetailResponse({
    this.message,
    this.status,
    this.currentPage,
    this.data,
  });

  factory ProjectDetailResponse.fromJson(Map<String, dynamic> json) {
    return ProjectDetailResponse(
      message: json['message'] as String?,
      status: json['status'] as String?,
      currentPage: json['current_page'] as int?,
      data: json['data'] == null ? null : ProjectModel.fromJson(json['data'] as Map<String, dynamic>),
    );
  }

  final String? message;
  final String? status;
  final int? currentPage;
  final ProjectModel? data;

  Map<String, dynamic> toJson() => {
        'message': message,
        'status': status,
        'current_page': currentPage,
        'data': data?.toJson(),
      };

  @override
  List<Object?> get props => [
        message,
        status,
        currentPage,
        data,
      ];
}
