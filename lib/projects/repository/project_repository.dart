part of 'i_project_repository.dart';

@Injectable(as: IProjectRepository)
class ProjectRepository extends IProjectRepository {
  ProjectRepository(super.client);

  @override
  ApiResult<ProjectListResponse> getProject({
    required int page,
    required int perPage,
    String? orderBy,
    String? orderDirection,
    String? search,
  }) async {
    final response = await client.get(
      url: AppStrings.projects,
      params: {
        'page': page.toString(),
        'per_page': perPage.toString(),
        if (orderBy != null) 'orderBy': orderBy,
        if (orderDirection != null) 'orderDirection': orderDirection,
        if (search != null && search.trim().isNotEmpty) 'search': search,
      },
    );

    return response.parseResponse(ProjectListResponse.fromJson);
  }

  @override
  ApiResult<ProjectDetailResponse> createProject({
    required String name,
    required DateTime startDate,
    required DateTime endDate,
    required String startDay,
    required String leaderType,
    required int leaderId,
  }) async {
    final response = await client.post(
      url: AppStrings.createProject,
      requests: {
        'name': name.trim(),
        'start_date': DateFormat('yyyy-MM-dd').format(startDate),
        'end_date': DateFormat('yyyy-MM-dd').format(endDate),
        'start_day': startDay,
        'leader_type': leaderType,
        'leader_user_id': leaderId.toString(),
      },
    );

    return response.parseResponse(ProjectDetailResponse.fromJson);
  }

  @override
  ApiResult<ProjectDetailResponse> detailProject({required String projectId}) async {
    final response = await client.get(
      url: AppStrings.detailProject(projectId),
    );
    return response.parseResponse(ProjectDetailResponse.fromJson);
  }

  @override
  ApiResult<ProjectDetailResponse> updateProject({
    required String projectId,
    String? name,
    DateTime? startDate,
    DateTime? endDate,
    String? startDay,
    String? leaderType,
    int? leaderId,
    String? userPerShift,
    DateTime? availabilityInputDeadline,
    List<ShiftConfigurationModel>? shiftConfigurations,
    List<HolidaysModel>? holidayAvailabilityList,
    List<UserSettingModel>? userSettingsList,
  }) async {
    final response = await client.multipart(
      url: AppStrings.updateProject(projectId),
      requests: {
        // ---------------------STEP 1------------------------ //

        if (name != null) 'name': name.trim(),
        if (startDate != null) 'start_date': DateFormat('yyyy-MM-dd').format(startDate),
        if (endDate != null) 'end_date': DateFormat('yyyy-MM-dd').format(endDate),
        if (startDay != null) 'start_day': startDay,
        if (leaderType != null) 'leader_type': leaderType,
        if (leaderId != null) 'leader_user_id': leaderId.toString(),

        // ---------------------STEP 2------------------------ //

        if (userPerShift != null) 'user_per_shift': userPerShift.trim(),
        if (availabilityInputDeadline != null)
          'availability_input_deadline': DateFormat('yyyy-MM-dd').format(availabilityInputDeadline),
        if (shiftConfigurations != null) ...{
          for (int i = 0; i < shiftConfigurations.length; i++) ...{
            'rules[$i][user_type]': Utility.userRoleViewString(shiftConfigurations[i].userType ?? ''),
            'rules[$i][maximum_user_per_shift]': shiftConfigurations[i].maximumPerShiftController.text,
            'rules[$i][minimum_user_per_shift]': shiftConfigurations[i].minimumPerShiftController.text,
            if (shiftConfigurations[i].availabilityCategories != null) ...{
              for (int j = 0; j < shiftConfigurations[i].availabilityCategories!.length; j++) ...{
                'rules[$i][category_ids][$j][id]': shiftConfigurations[i].availabilityCategories![j].id.toString(),
                'rules[$i][category_ids][$j][allowed_available_days]':
                    shiftConfigurations[i].availabilityCategories![j].controller?.text ?? '',
              },
            },
            'rules[$i][buffer_days]': shiftConfigurations[i].avoidShiftDayaBeforeOrAfterController.text,
            'rules[$i][can_use_prohibited_days]': shiftConfigurations[i].canYouProhibitedDays.toString(),
          },
        },
        // ---------------------STEP 3------------------------ //
        if (holidayAvailabilityList != null && holidayAvailabilityList.isNotEmpty) ...{
          for (int i = 0; i < holidayAvailabilityList.length; i++) ...{
            'calendar_dates[$i][day_type]': holidayAvailabilityList[i].holidayKey ?? '',
            if (holidayAvailabilityList[i].dates.isNotEmpty) ...{
              for (int j = 0; j < holidayAvailabilityList[i].dates.length; j++) ...{
                'calendar_dates[$i][dates][$j]': DateFormat('yyyy-MM-dd').format(holidayAvailabilityList[i].dates[j]),
              },
            },
          },
        },
        // ---------------------STEP 4------------------------ //
        if (userSettingsList != null && userSettingsList.isNotEmpty) ...{
          for (int i = 0; i < userSettingsList.length; i++) ...{
            'users[$i][id]': userSettingsList[i].userModel?.id.toString() ?? '',
            if (userSettingsList[i].maxShift.text.isNotEmpty) 'users[$i][max_shift]': userSettingsList[i].maxShift.text,
            if (userSettingsList[i].minShift.text.isNotEmpty) 'users[$i][min_shift]': userSettingsList[i].minShift.text,
            if (userSettingsList[i].maxWeekEndShift.text.isNotEmpty)
              'users[$i][max_weekend_shift]': userSettingsList[i].maxWeekEndShift.text,
            if (userSettingsList[i].minWeekEndShift.text.isNotEmpty)
              'users[$i][min_weekend_shift]': userSettingsList[i].minWeekEndShift.text,
            for (int j = 0; j < userSettingsList[i].assignDateAndControllerModel!.length; j++) ...{
              if (userSettingsList[i].assignDateAndControllerModel![j].assignDate != null)
                'users[$i][auto_assign_days][$j]': DateFormat('yyyy-MM-dd')
                    .format(userSettingsList[i].assignDateAndControllerModel?[j].assignDate ?? DateTime.now()),
            },
          },
        },
      },
    );

    return response.parseResponse(ProjectDetailResponse.fromJson);
  }

  @override
  ApiResult<ProjectDetailResponse> deleteProject({required String projectId}) async {
    final response = await client.delete(
      url: AppStrings.deleteProject(projectId),
    );

    return response.parseResponse(ProjectDetailResponse.fromJson);
  }

  @override
  ApiResult<ProjectDetailResponse> deleteUserForProject({required String userId, required String projectId}) async {
    final response = await client.delete(
      url: AppStrings.deleteUserForThisProject(projectId, userId),
    );

    return response.parseResponse(ProjectDetailResponse.fromJson);
  }

  @override
  ApiResult<ProjectPlanningResponse> projectPlanning({
    required String projectId,
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    final response = await client.get(
      url: AppStrings.projectPlanning(
        projectId,
      ),
      params: {
        'start_date': DateFormat('yyyy-MM-dd').format(startDate),
        'end_date': DateFormat('yyyy-MM-dd').format(endDate),
      },
    );
    return response.parseResponse(ProjectPlanningResponse.fromJson);
  }

  @override
  ApiResult<CommonResponse> updateProjectAvailability({
    required int projectId,
    List<HolidaysModel> holidays = const <HolidaysModel>[],
  }) async {
    final response = await client.multipart(
      url: AppStrings.updateProjectAvailability(projectId.toString()),
      requests: {
        for (int i = 0; i < holidays.length; i++) ...{
          'data[$i][leave_category_id]': holidays[i].holidayKey ?? '',
          for (int j = 0; j < holidays[i].dates.length; j++) ...{
            'data[$i][dates][$j]': DateFormat('yyyy-MM-dd').format(holidays[i].dates[j]),
          },
        },
      },
    );
    return response.parseResponse(CommonResponse.fromJson);
  }

  @override
  ApiResult<ProjectListAvailabiltyResponse> getProjectAvailability({
    required int projectId,
    required int userId,
  }) async {
    final response = await client.get(
      url: AppStrings.projectAvailability(projectId.toString()),
      params: <String, String>{
        'user_id': userId.toString(),
      },
    );
    return response.parseResponse(ProjectListAvailabiltyResponse.fromJson);
  }

  @override
  ApiResult<CommonResponse> rejectLeave({
    required int projectId,
    int? userProjectCalendarDateId,
    String? rejectReason,
  }) async {
    final response = await client.post(
      url: AppStrings.rejectLeave(projectId.toString()),
      requests: {
        if (userProjectCalendarDateId != null) 'user_project_calendar_date_id': userProjectCalendarDateId,
        if (rejectReason != null) 'reject_reason': rejectReason,
      },
    );
    return response.parseResponse(CommonResponse.fromJson);
  }

  @override
  ApiResult<ProjectListAvailabiltyResponse> getUserAvailability({
    required String projectId,
    required String userId,
  }) async {
    final response = await client.get(
      url: AppStrings.getUserAvailability(projectId),
      params: <String, String>{
        'user_id': userId,
      },
    );
    return response.parseResponse(ProjectListAvailabiltyResponse.fromJson);
  }

  @override
  ApiResult<CommonResponse> updateUserProjectAvailability({
    required String projectId,
    List<UserProjectModelNew> holidays = const <UserProjectModelNew>[],
  }) async {
    final response = await client.multipart(
      url: AppStrings.updateUserProjectAvailability(projectId),
      requests: {
        if (holidays.isNotEmpty)
          for (int i = 0; i < holidays.length; i++) ...{
            'data[$i][leave_category_id]': holidays[i].leaveCategoryId.toString(),
            if (holidays[i].ruleCategoryList != null && holidays[i].ruleCategoryList!.isNotEmpty)
              for (int j = 0; j < holidays[i].ruleCategoryList!.length; j++) ...{
                'data[$i][dates][$j]':
                    DateFormat('yyyy-MM-dd').format(holidays[i].ruleCategoryList?[j].date ?? DateTime.now()),
              },
          },
      },
    );
    return response.parseResponse(CommonResponse.fromJson);
  }

  @override
  ApiResult<CalendarDateListResponse> getCalendarDateList({
    required String projectId,
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    final response = await client.get(
      url: AppStrings.calendarDateList(projectId),
      params: {
        'start_date': DateFormat('yyyy-MM-dd').format(startDate),
        'end_date': DateFormat('yyyy-MM-dd').format(endDate),
      },
    );
    return response.parseResponse(CalendarDateListResponse.fromJson);
  }

  @override
  ApiResult<CommonResponse> autoAssignShift({required String projectId}) async {
    final response = await client.post(
      url: AppStrings.autoAssignShift(projectId),
    );
    return response.parseResponse(CommonResponse.fromJson);
  }

  @override
  ApiResult<CommonResponse> manualAssignShift({
    required String projectId,
    required List<UserProjectCalendarDateModel> assignments,
  }) async {
    final requestData = <String, String>{};

    // Group assignments by user ID
    final userAssignments = <int, Map<String, List<String>>>{};

    for (final assignment in assignments) {
      final userId = assignment.userId ?? 0;
      if (!userAssignments.containsKey(userId)) {
        userAssignments[userId] = {
          'add_dates': <String>[],
          'remove_dates': <String>[],
        };
      }

      final dateString = DateFormat('yyyy-MM-dd').format(assignment.date ?? DateTime.now());

      // Handle add_dates for assignments with positive leaveCategoryId
      if ((assignment.leaveCategoryId ?? 0) > 0) {
        userAssignments[userId]!['add_dates']!.add(dateString);
      }

      // Handle remove_dates for assignments with negative leaveCategoryId (removal markers)
      if ((assignment.leaveCategoryId ?? 0) < 0) {
        userAssignments[userId]!['remove_dates']!.add(dateString);
      }
    }

    // Format the data for the API
    var userIndex = 0;
    for (final entry in userAssignments.entries) {
      final userId = entry.key;
      final dates = entry.value;

      requestData['users[$userIndex][user_id]'] = userId.toString();

      // Add dates
      for (var i = 0; i < dates['add_dates']!.length; i++) {
        requestData['users[$userIndex][add_dates][$i]'] = dates['add_dates']![i];
      }

      // Remove dates
      for (var i = 0; i < dates['remove_dates']!.length; i++) {
        requestData['users[$userIndex][remove_dates][$i]'] = dates['remove_dates']![i];
      }

      userIndex++;
    }

    final response = await client.multipart(
      url: AppStrings.manualAssignShift(projectId),
      requests: requestData,
    );
    return response.parseResponse(CommonResponse.fromJson);
  }

  @override
  ApiResult<CommonResponse> swapAvailabilityDate({
    required String projectId,
    SwapUserAvailabilityDate? swapUserAvailabilityDate,
  }) async {
    final response = await client.post(
      url: AppStrings.swapAvailabilityDate(projectId),
      requests: {
        if (swapUserAvailabilityDate != null) ...{
          'first_user': swapUserAvailabilityDate.userOneId.toString(),
          'first_user_date': DateFormat('yyyy-MM-dd').format(swapUserAvailabilityDate.userOneDate ?? DateTime.now()),
          'second_user': swapUserAvailabilityDate.userTwoId.toString(),
          'second_user_date': DateFormat('yyyy-MM-dd').format(swapUserAvailabilityDate.userTwoDate ?? DateTime.now()),
        },
      },
    );
    return response.parseResponse(CommonResponse.fromJson);
  }

  @override
  ApiResult<ExportPdfResponse> exportPdf({
    required String projectId,
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    final response = await client.get(
      url: AppStrings.exportPdf(projectId),
      params: {
        'start_date': DateFormat('yyyy-MM-dd').format(startDate),
        'end_date': DateFormat('yyyy-MM-dd').format(endDate),
      },
    );
    return response.parseResponse(ExportPdfResponse.fromJson);
  }

  @override
  ApiResult<CommonResponse> importProjectUser({required String projectId, AppFile? excleUserFile}) async {
    final response = await client.multipart(
      url: AppStrings.importProjectUser(projectId),
      requests: {
        'is_from_web': '1',
      },
      webFiles: [
        if (excleUserFile != null)
          http.MultipartFile.fromBytes(
            'file',
            excleUserFile.bytes,
            filename: excleUserFile.name,
            contentType: excleUserFile.mimeType != null ? MediaType.parse(excleUserFile.mimeType!) : null,
          ),
      ],
    );
    return response.parseResponse(CommonResponse.fromJson);
  }

  @override
  ApiResult<CommonResponse> uploadForApproval({required String projectId}) async {
    final response = await client.post(
      url: AppStrings.approveAndPublish(projectId),
    );
    return response.parseResponse(CommonResponse.fromJson);
  }

  @override
  ApiResult<CommonResponse> publish({required String projectId}) async {
    final response = await client.post(
      url: AppStrings.publish(projectId),
    );
    return response.parseResponse(CommonResponse.fromJson);
  }

  @override
  ApiResult<CommonResponse> exportToGoogleCalendar({required String projectId}) async {
    final response = await client.post(
      url: AppStrings.exportToGoogleCalendar(projectId),
    );
    return response.parseResponse(CommonResponse.fromJson);
  }
}
