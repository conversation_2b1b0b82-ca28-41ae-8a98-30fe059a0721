import 'dart:typed_data';

import 'package:excel_app/constants/app_strings.dart';
import 'package:excel_app/home/<USER>/export_pdf_response.dart';
import 'package:excel_app/projects/model/holiday_model.dart';
import 'package:excel_app/projects/model/shift_configuration_model.dart';
import 'package:excel_app/projects/model/swap_availability_model.dart';
import 'package:excel_app/projects/model/user_project_calendar_date_model.dart';
import 'package:excel_app/projects/model/user_setting_model.dart';
import 'package:excel_app/projects/response/calendar_date_list_response.dart';
import 'package:excel_app/projects/response/project_detail_response.dart';
import 'package:excel_app/projects/response/project_list_availabilty_response.dart';
import 'package:excel_app/projects/response/project_list_response.dart';
import 'package:excel_app/projects/response/project_planning_response.dart';
import 'package:excel_app/projects/view/user_availability_dailog_view.dart';
import 'package:excel_app/users/response/common_response.dart';
import 'package:excel_app/utility/extentions/fpdart_extentions.dart';
import 'package:excel_app/utility/helpers/utility.dart';
import 'package:excel_app/utility/model/app_file.dart';
import 'package:excel_app/utility/network/client.dart';
import 'package:fpdart/fpdart.dart';
import 'package:http/http.dart' as http;
import 'package:http_parser/http_parser.dart';
import 'package:injectable/injectable.dart';
import 'package:intl/intl.dart';

part 'project_repository.dart';

abstract class IProjectRepository {
  IProjectRepository(this.client);
  final Client client;

  ApiResult<ProjectListResponse> getProject({
    int page,
    int perPage,
    String? orderBy,
    String? orderDirection,
    String? search,
  });

  ApiResult<ProjectDetailResponse> createProject({
    required String name,
    required DateTime startDate,
    required DateTime endDate,
    required String startDay,
    required String leaderType,
    required int leaderId,
  });

  ApiResult<ProjectDetailResponse> updateProject({
    required String projectId,
    // ---------------------STEP 1------------------------ //
    String? name,
    DateTime? startDate,
    DateTime? endDate,
    String? startDay,
    String? leaderType,
    int? leaderId,
    // ---------------------STEP 2------------------------ //
    String? userPerShift,
    DateTime? availabilityInputDeadline,
    List<ShiftConfigurationModel>? shiftConfigurations,
    // ---------------------STEP 3------------------------ //
    List<HolidaysModel>? holidayAvailabilityList,
    // ---------------------STEP 4------------------------ //
    List<UserSettingModel>? userSettingsList,
  });

  ApiResult<ProjectDetailResponse> detailProject({
    required String projectId,
  });
  ApiResult<ProjectDetailResponse> deleteProject({
    required String projectId,
  });
  ApiResult<ProjectDetailResponse> deleteUserForProject({
    required String userId,
    required String projectId,
  });

  ApiResult<ProjectPlanningResponse> projectPlanning({
    required String projectId,
    required DateTime startDate,
    required DateTime endDate,
  });

  ApiResult<CommonResponse> updateProjectAvailability({
    required int projectId,
    List<HolidaysModel> holidays = const <HolidaysModel>[],
  });

  ApiResult<ProjectListAvailabiltyResponse> getProjectAvailability({
    required int projectId,
    required int userId,
  });

  ApiResult<CommonResponse> rejectLeave({
    required int projectId,
    int? userProjectCalendarDateId,
    String? rejectReason,
  });

  ApiResult<CalendarDateListResponse> getCalendarDateList({
    required String projectId,
    required DateTime startDate,
    required DateTime endDate,
  });

  ApiResult<ProjectListAvailabiltyResponse> getUserAvailability({
    required String projectId,
    required String userId,
  });

  ApiResult<CommonResponse> updateUserProjectAvailability({
    required String projectId,
    List<UserProjectModelNew> holidays = const <UserProjectModelNew>[],
  });

  ApiResult<CommonResponse> autoAssignShift({
    required String projectId,
  });

  ApiResult<CommonResponse> manualAssignShift({
    required String projectId,
    required List<UserProjectCalendarDateModel> assignments,
  });

  ApiResult<CommonResponse> swapAvailabilityDate({
    required String projectId,
    SwapUserAvailabilityDate? swapUserAvailabilityDate,
  });

  ApiResult<ExportPdfResponse> exportPdf({
    required String projectId,
    required DateTime startDate,
    required DateTime endDate,
  });

  ApiResult<CommonResponse> importProjectUser({
    required String projectId,
    AppFile? excleUserFile,
  });
  ApiResult<CommonResponse> uploadForApproval({
    required String projectId,
  });
  ApiResult<CommonResponse> publish({
    required String projectId,
  });
  ApiResult<CommonResponse> exportToGoogleCalendar({
    required String projectId,
  });
  ApiResult<Uint8List> exportToIcal({
    required String projectId,
  });
}
