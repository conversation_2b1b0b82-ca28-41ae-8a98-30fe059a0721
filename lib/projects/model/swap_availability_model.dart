class SwapUserAvailabilityDate {
  SwapUserAvailabilityDate({
    this.userOneId,
    this.userTwoId,
    this.userOneDate,
    this.userTwoDate,
  });
  int? userOneId;
  int? userTwoId;
  DateTime? userOneDate;
  DateTime? userTwoDate;

  SwapUserAvailabilityDate copyWith({
    int? userOneId,
    int? userTwoId,
    DateTime? userOneDate,
    DateTime? userTwoDate,
  }) {
    return SwapUserAvailabilityDate(
      userOneId: userOneId ?? this.userOneId,
      userTwoId: userTwoId ?? this.userTwoId,
      userOneDate: userOneDate ?? this.userOneDate,
      userTwoDate: userTwoDate ?? this.userTwoDate,
    );
  }

  bool isCheckFirstUserNotNull() {
    return userOneId != null && userOneDate != null;
  }

  bool isCheckAllUserNotNull() {
    return userOneId != null && userTwoId != null && userOneDate != null && userTwoDate != null;
  }
}
