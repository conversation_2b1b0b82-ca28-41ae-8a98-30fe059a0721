// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:excel_app/holiday_category/model/holiday_catergory_model.dart';
import 'package:excel_app/users/model/user_model.dart';
import 'package:flutter/material.dart';

class UserSettingModel {
  final int? projectId;
  final int? projectLeaderId;
  final UserModel? userModel;
  final TextEditingController maxShift;
  final TextEditingController minShift;
  final TextEditingController maxWeekEndShift;
  final TextEditingController minWeekEndShift;
  final int? canYouProhibitedDays;
  List<AssignDateAndControllerModel>? assignDateAndControllerModel;
  final bool isActionOpen;

  UserSettingModel({
    required this.minShift,
    required this.maxShift,
    required this.minWeekEndShift,
    required this.maxWeekEndShift,
    this.projectLeaderId,
    this.projectId,
    this.userModel,
    this.canYouProhibitedDays,
    this.assignDateAndControllerModel = const [],
    this.isActionOpen = false,
  });

  UserSettingModel copyWith({
    int? projectId,
    int? projectLeaderId,
    UserModel? userModel,
    TextEditingController? maxShift,
    TextEditingController? minShift,
    TextEditingController? maxWeekEndShift,
    TextEditingController? minWeekEndShift,
    List<HolidayCategoryModel>? availabilityCategories,
    int? canYouProhibitedDays,
    List<AssignDateAndControllerModel>? assignDateAndControllerModel,
    bool? isActionOpen,
  }) {
    return UserSettingModel(
      projectId: projectId ?? this.projectId,
      projectLeaderId: projectLeaderId ?? this.projectLeaderId,
      userModel: userModel ?? this.userModel,
      maxShift: maxShift ?? this.maxShift,
      minShift: minShift ?? this.minShift,
      maxWeekEndShift: maxWeekEndShift ?? this.maxWeekEndShift,
      minWeekEndShift: minWeekEndShift ?? this.minWeekEndShift,
      canYouProhibitedDays: canYouProhibitedDays ?? this.canYouProhibitedDays,
      assignDateAndControllerModel: assignDateAndControllerModel ?? this.assignDateAndControllerModel,
      isActionOpen: isActionOpen ?? this.isActionOpen,
    );
  }
}

class AssignDateAndControllerModel {
  AssignDateAndControllerModel({
    required this.assignDateController,
    this.assignDate,
  });
  DateTime? assignDate;
  final TextEditingController assignDateController;

  AssignDateAndControllerModel copyWith({
    DateTime? assignDate,
    TextEditingController? assignDateController,
  }) {
    return AssignDateAndControllerModel(
      assignDate: assignDate ?? this.assignDate,
      assignDateController: assignDateController ?? this.assignDateController,
    );
  }
}
