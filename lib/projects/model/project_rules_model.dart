import 'package:equatable/equatable.dart';
import 'package:excel_app/constants/app_constants.dart';
import 'package:excel_app/projects/model/project_rules_category_model.dart';

class ProjectRuleModel extends Equatable {
  const ProjectRuleModel({
    this.id,
    this.projectId,
    this.userType,
    this.maximumUserPerShift,
    this.minimumUserPerShift,
    this.preferredUserPerShift,
    this.bufferDays,
    this.canUseProhibitedDays,
    this.createdAt,
    this.updatedAt,
    this.projectRuleCategories = const [],
  });

  factory ProjectRuleModel.fromJson(Map<String, dynamic> json) {
    return ProjectRuleModel(
      id: json['id'] as int?,
      projectId: json['project_id'] as int?,
      userType: json['user_type'] as String?,
      maximumUserPerShift: json['maximum_user_per_shift'] as int?,
      minimumUserPerShift: json['minimum_user_per_shift'] as int?,
      preferredUserPerShift: json['preferred_users_per_shift'] as int?,
      bufferDays: json['buffer_days'] as int?,
      canUseProhibitedDays: json['can_use_prohibited_days'] as int?,
      createdAt: DateTime.tryParse((json['created_at'] as String?) ?? ''),
      updatedAt: DateTime.tryParse((json['updated_at'] as String?) ?? ''),
      projectRuleCategories: json['project_rule_categories'] == null
          ? []
          : List<ProjectRuleCategoryModel>.from(
              (json['project_rule_categories'] as List<dynamic>).map(
                (e) => ProjectRuleCategoryModel.fromJson(e as Map<String, dynamic>),
              ),
            ),
    );
  }

  final int? id;
  final int? projectId;
  final String? userType;
  final int? maximumUserPerShift;
  final int? minimumUserPerShift;
  final int? preferredUserPerShift;
  final int? bufferDays;
  final int? canUseProhibitedDays;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final List<ProjectRuleCategoryModel> projectRuleCategories;

  Map<String, dynamic> toJson() => {
        'id': id,
        'project_id': projectId,
        'user_type': userType,
        'maximum_user_per_shift': maximumUserPerShift,
        'minimum_user_per_shift': minimumUserPerShift,
        'preferred_users_per_shift': preferredUserPerShift,
        'buffer_days': bufferDays,
        'can_use_prohibited_days': canUseProhibitedDays,
        'created_at': createdAt?.toIso8601String(),
        'updated_at': updatedAt?.toIso8601String(),
        'project_rule_categories': projectRuleCategories.map((x) => x.toJson()).toList(),
      };

  @override
  List<Object?> get props => [
        id,
        projectId,
        userType,
        maximumUserPerShift,
        minimumUserPerShift,
        preferredUserPerShift,
        bufferDays,
        canUseProhibitedDays,
        createdAt,
        updatedAt,
        projectRuleCategories,
      ];

  String get userTypeViewString {
    switch (userType) {
      case AppConstants.doctorOne:
        return AppConstants.doctor1;
      case AppConstants.doctorTwo:
        return AppConstants.doctor2;
      case AppConstants.firstYear:
        return AppConstants.firstYearResident;
      case AppConstants.secondYear:
        return AppConstants.secondYearResident;
      case AppConstants.thirdYear:
        return AppConstants.thirdYearResident;
      case AppConstants.lastYear:
        return AppConstants.lastYearResident;
      default:
        return AppConstants.students;
    }
  }
}
