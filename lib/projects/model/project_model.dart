// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:equatable/equatable.dart';
import 'package:excel_app/app/bloc/authentication_bloc.dart';
import 'package:excel_app/constants/app_colors.dart';
import 'package:excel_app/constants/app_constants.dart';
import 'package:excel_app/constants/app_strings.dart';
import 'package:excel_app/data_table/data_table_title_widget.dart';
import 'package:excel_app/data_table/widget/chip_text_widget.dart';
import 'package:excel_app/injector/injector.dart';
import 'package:excel_app/projects/model/holiday_model.dart';
import 'package:excel_app/projects/model/pivot_model.dart';
import 'package:excel_app/projects/model/project_calendardate_model.dart';
import 'package:excel_app/projects/model/project_rules_model.dart';
import 'package:excel_app/projects/model/project_user_model.dart';
import 'package:excel_app/users/model/user_model.dart';
import 'package:excel_app/utility/extentions/string_extentions.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

class ProjectModel extends Equatable {
  const ProjectModel({
    this.id,
    this.leaderUserId,
    this.name,
    this.startDate,
    this.endDate,
    this.startDay,
    this.status,
    this.leaderType,
    this.userPerShift,
    this.availabilityInputDeadline,
    this.userTypes = const [],
    this.isSubmittedForApproval = 0,
    this.isAssigned = 0,
    this.canUseProhibitedDays = 0,
    this.isApproved,
    this.createdAt,
    this.updatedAt,
    this.projectUsersCount,
    this.leader,
    this.pivot,
    this.projectRules = const [],
    this.projectCalendarDates = const [],
    this.projectUsers = const [],
  });

  // Helper method to safely parse user_types which might be a List or a Map
  static List<String> _parseUserTypes(dynamic userTypes) {
    if (userTypes == null) {
      return [];
    }

    // If it's already a List, process it
    if (userTypes is List) {
      return userTypes.map((e) => e.toString()).toList();
    }

    // If it's a Map, extract values
    if (userTypes is Map) {
      return userTypes.values.map((e) => e.toString()).toList();
    }

    // If it's a String, try to handle it (in case it's a single value)
    if (userTypes is String) {
      return [userTypes];
    }

    // Default fallback
    return [];
  }

  factory ProjectModel.fromJson(Map<String, dynamic> json) {
    return ProjectModel(
      id: json['id'] as int?,
      leaderUserId: json['leader_user_id'] as int?,
      name: json['name'] as String?,
      startDate: DateTime.tryParse((json['start_date'] as String?) ?? ''),
      endDate: DateTime.tryParse((json['end_date'] as String?) ?? ''),
      startDay: json['start_day'] as String?,
      status: json['status'] as String?,
      leaderType: json['leader_type'] as String?,
      userPerShift: json['user_per_shift'] as int?,
      availabilityInputDeadline: DateTime.tryParse((json['availability_input_deadline'] as String?) ?? ''),
      userTypes: _parseUserTypes(json['user_types']),
      isSubmittedForApproval: (json['is_submitted_for_approval'] as int?) ?? 0,
      canUseProhibitedDays: (json['can_use_prohibited_days'] as int?) ?? 0,
      isAssigned: (json['is_assigned'] as int?) ?? 0,
      isApproved: json['is_approved'] as int?,
      createdAt: DateTime.tryParse((json['created_at'] as String?) ?? ''),
      updatedAt: DateTime.tryParse((json['updated_at'] as String?) ?? ''),
      projectUsersCount: json['project_users_count'] as int?,
      leader: json['leader'] == null ? null : UserModel.fromJson(json['leader'] as Map<String, dynamic>),
      pivot: json['pivot'] == null ? null : PivotModel.fromJson(json['pivot'] as Map<String, dynamic>),
      projectRules: json['project_rules'] == null
          ? []
          : List<ProjectRuleModel>.from(
              (json['project_rules'] as List<dynamic>).map(
                (e) => ProjectRuleModel.fromJson(e as Map<String, dynamic>),
              ),
            ),
      projectCalendarDates: json['project_calendar_dates'] == null
          ? []
          : List<ProjectCalendarDateModel>.from(
              (json['project_calendar_dates'] as List<dynamic>).map(
                (e) => ProjectCalendarDateModel.fromJson(e as Map<String, dynamic>),
              ),
            ),
      projectUsers: json['project_users'] == null
          ? []
          : List<ProjectUserModel>.from(
              (json['project_users'] as List<dynamic>).map(
                (e) => ProjectUserModel.fromJson(e as Map<String, dynamic>),
              ),
            ),
    );
  }

  final int? id;
  final int? leaderUserId;
  final String? name;
  final DateTime? startDate;
  final DateTime? endDate;
  final String? startDay;
  final String? status;
  final String? leaderType;
  final int? userPerShift;
  final DateTime? availabilityInputDeadline;
  final List<String> userTypes;
  final int isSubmittedForApproval;
  final int isAssigned;
  final int? isApproved;
  final int? canUseProhibitedDays;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final int? projectUsersCount;
  final UserModel? leader;
  final PivotModel? pivot;
  final List<ProjectRuleModel> projectRules;
  final List<ProjectCalendarDateModel> projectCalendarDates;
  final List<ProjectUserModel> projectUsers;

  Map<String, dynamic> toJson() => {
        'id': id,
        'leader_user_id': leaderUserId,
        'name': name,
        'start_date':
            "${startDate?.year.toString().padLeft(4, '0')}-${startDate?.month.toString().padLeft(2, '0')}-${startDate?.day.toString().padLeft(2, '0')}",
        'end_date':
            "${endDate?.year.toString().padLeft(4, '0')}-${endDate?.month.toString().padLeft(2, '0')}-${endDate?.day.toString().padLeft(2, '0')}",
        'start_day': startDay,
        'status': status,
        'leader_type': leaderType,
        'user_per_shift': userPerShift,
        'availability_input_deadline':
            "${availabilityInputDeadline?.year.toString().padLeft(4, '0')}-${availabilityInputDeadline?.month.toString().padLeft(2, '0')}-${availabilityInputDeadline?.day.toString().padLeft(2, '0')}",
        'user_types': userTypes,
        'is_submitted_for_approval': isSubmittedForApproval,
        'can_use_prohibited_days': canUseProhibitedDays,
        'is_assigned': isAssigned,
        'is_approved': isApproved,
        'created_at': createdAt?.toIso8601String(),
        'updated_at': updatedAt?.toIso8601String(),
        'project_users_count': projectUsersCount,
        'leader': leader?.toJson(),
        'pivot': pivot?.toJson(),
        'project_rules': projectRules.map((e) => e.toJson()).toList(),
        'project_calendar_dates': projectCalendarDates.map((e) => e.toJson()).toList(),
        'project_users': projectUsers.map((e) => e.toJson()).toList(),
      };

  @override
  List<Object?> get props => [
        id,
        leaderUserId,
        name,
        startDate,
        endDate,
        startDay,
        status,
        leaderType,
        userPerShift,
        availabilityInputDeadline,
        userTypes,
        isSubmittedForApproval,
        canUseProhibitedDays,
        isAssigned,
        isApproved,
        createdAt,
        updatedAt,
        projectUsersCount,
        leader,
        pivot,
        projectRules,
        projectCalendarDates,
        projectUsers,
      ];

  String? get projectStatus {
    if (isSubmittedForApproval == 1 && (isApproved == 0 || isApproved == null)) {
      return 'Planning approval pending';
    } else if (isSubmittedForApproval == 0) {
      return 'Yet to send for approval';
    }
    return null;
  }

  String get leaderTypeViewString {
    switch (leaderType) {
      case AppConstants.onlyLeader:
        return 'Leader Only';
      case AppConstants.leaderUser:
        return 'Leader + User';
      default:
        return 'Leader';
    }
  }

  Color get leaderTypeViewBackgroundColor {
    switch (leaderType) {
      case AppConstants.onlyLeader:
        return AppColors.paleLavender;
      case AppConstants.leaderUser:
        return AppColors.lightMint;
      default:
        return AppColors.lightMint;
    }
  }

  Color get availabilityStatusViewBackgroundColor {
    final authState = getIt<AuthenticationBloc>().state;
    if (authState is Authenticated) {
      if (authState.user.role == AppStrings.admin && isSubmittedForApproval == 1) {
        return AppColors.primary;
      }
      if (authState.user.role == AppStrings.admin && isSubmittedForApproval == 0) {
        return AppColors.disableColor;
      }
      return AppColors.uploadOrangeColor;
    }
    return AppColors.transparent;
  }

  Widget get availabilityStatusView {
    if (isSubmittedForApproval == 0 && isApproved == null) {
      return const ChipTextWidget(
        backgroundColor: AppColors.transparent,
        text: 'Not available',
      );
    } else if (isSubmittedForApproval == 1 && isApproved == 0) {
      return const ChipTextWidget(
        backgroundColor: AppColors.oldLace,
        text: 'Pending',
        textColor: AppColors.sunShade,
      );
    }
    // else if (isSubmittedForApproval == 1 && isApproved == 0) {
    //   return const ChipTextWidget(
    //     backgroundColor: AppColors.lavenderBlush,
    //     text: 'Rejected',
    //     textColor: AppColors.redOrange,
    //   );
    // }
    else if (isSubmittedForApproval == 1 && isApproved == 1) {
      return const ChipTextWidget(
        backgroundColor: AppColors.bubbles,
        text: 'Accepted',
        textColor: AppColors.deepSkyBlue,
      );
    }
    return const DataTableTitleWidget(
      title: '-',
    );
  }

  String get deadline {
    return availabilityInputDeadline == null
        ? '-'
        : DateFormat('dd/MM/yyyy').format(availabilityInputDeadline ?? DateTime.now());
  }

  String get duration {
    return startDate == null || endDate == null
        ? '-'
        : '${DateFormat('MM/yyyy').format(startDate ?? DateTime.now())} to \n${DateFormat('MM/yyyy').format(endDate ?? DateTime.now())}';
  }

  ProjectModel copyWith({
    int? id,
    int? leaderUserId,
    String? name,
    DateTime? startDate,
    DateTime? endDate,
    String? startDay,
    String? status,
    String? leaderType,
    int? userPerShift,
    DateTime? availabilityInputDeadline,
    List<String>? userTypes,
    int? isSubmittedForApproval,
    int? isAssigned,
    int? isApproved,
    DateTime? createdAt,
    DateTime? updatedAt,
    int? projectUsersCount,
    UserModel? leader,
    PivotModel? pivot,
    List<ProjectRuleModel>? projectRules,
    List<ProjectCalendarDateModel>? projectCalendarDates,
    List<ProjectUserModel>? projectUsers,
  }) {
    return ProjectModel(
      id: id ?? this.id,
      leaderUserId: leaderUserId ?? this.leaderUserId,
      name: name ?? this.name,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      startDay: startDay ?? this.startDay,
      status: status ?? this.status,
      leaderType: leaderType ?? this.leaderType,
      userPerShift: userPerShift ?? this.userPerShift,
      availabilityInputDeadline: availabilityInputDeadline ?? this.availabilityInputDeadline,
      userTypes: userTypes ?? this.userTypes,
      isSubmittedForApproval: isSubmittedForApproval ?? this.isSubmittedForApproval,
      isAssigned: isAssigned ?? this.isAssigned,
      isApproved: isApproved ?? this.isApproved,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      projectUsersCount: projectUsersCount ?? this.projectUsersCount,
      leader: leader ?? this.leader,
      pivot: pivot ?? this.pivot,
      projectRules: projectRules ?? this.projectRules,
      projectCalendarDates: projectCalendarDates ?? this.projectCalendarDates,
      projectUsers: projectUsers ?? this.projectUsers,
    );
  }

  List<HolidaysModel> getHolidays() {
    var holidays = <HolidaysModel>[];
    for (final element in projectCalendarDates) {
      final isHolidayExists = holidays.any((h) => h.holidayKey == element.dayType);
      if (isHolidayExists) {
        holidays = [...holidays].map((e) {
          if (e.holidayKey == element.dayType) {
            return e.copyWith(
              dates: [...e.dates, element.date].whereType<DateTime>().toList(),
            );
          }
          return e;
        }).toList();
        continue;
      }
      holidays.add(
        HolidaysModel(
          holidayKey: element.dayType,
          dates: [element.date].whereType<DateTime>().toList(),
          bgColor: AppColors.getHolidayColor(element.dayType ?? ''),
        ),
      );
    }
    return holidays;
  }

  DateTime get getStartDate => startDate ?? DateTime.now();
  DateTime get getEndDate => endDate ?? DateTime.now();

  int get weekdayOfFirstOfWeek {
    final kStartDay = (startDay ?? 'Monday').capitalizeFirstofEach;
    switch (kStartDay) {
      case 'Sunday':
        return 0;
      case 'Monday':
        return 1;
      case 'Tuesday':
        return 2;
      case 'Wednesday':
        return 3;
      case 'Thursday':
        return 4;
      case 'Friday':
        return 5;
      case 'Saturday':
        return 6;
      default:
        return 0;
    }
  }
}
