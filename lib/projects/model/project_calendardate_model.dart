import 'package:equatable/equatable.dart';
import 'package:excel_app/constants/app_constants.dart';

class ProjectCalendarDateModel extends Equatable {
  const ProjectCalendarDateModel({
    this.id,
    this.projectId,
    this.dayType,
    this.date,
    this.createdAt,
    this.updatedAt,
  });

  factory ProjectCalendarDateModel.fromJson(Map<String, dynamic> json) {
    return ProjectCalendarDateModel(
      id: json['id'] as int?,
      projectId: json['project_id'] as int?,
      dayType: json['day_type'] as String?,
      date: DateTime.tryParse((json['date'] as String?) ?? ''),
      createdAt: DateTime.tryParse((json['created_at'] as String?) ?? ''),
      updatedAt: DateTime.tryParse((json['updated_at'] as String?) ?? ''),
    );
  }

  final int? id;
  final int? projectId;
  final String? dayType;
  final DateTime? date;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  Map<String, dynamic> toJson() => {
        'id': id,
        'project_id': projectId,
        'day_type': dayType,
        'date': date?.toIso8601String(),
        'created_at': createdAt?.toIso8601String(),
        'updated_at': updatedAt?.toIso8601String(),
      };

  @override
  List<Object?> get props => [
        id,
        projectId,
        dayType,
        date,
        createdAt,
        updatedAt,
      ];

  String get dayTypeViewString {
    switch (dayType) {
      case AppConstants.holiday:
        return AppConstants.holidays;
      case AppConstants.otherHoliday:
        return AppConstants.otherHolidays;
      case AppConstants.prohibitedDay:
        return AppConstants.prohibitedDays;
      default:
        return AppConstants.holidays;
    }
  }
}
