// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:equatable/equatable.dart';
import 'package:excel_app/holiday_category/model/holiday_catergory_model.dart';
import 'package:excel_app/projects/model/user_project_log_model.dart';

class ProjectRuleCategoryModel extends Equatable {
  const ProjectRuleCategoryModel({
    this.id,
    this.projectId,
    this.projectRuleId,
    this.leaveCategoryId,
    this.allowedAvailableDays,
    this.createdAt,
    this.date,
    this.updatedAt,
    this.name,
    this.leaveCategory,
    this.userProjectLogs = const [],
  });

  factory ProjectRuleCategoryModel.fromJson(Map<String, dynamic> json) {
    return ProjectRuleCategoryModel(
      id: json['id'] as int?,
      projectId: json['project_id'] as int?,
      projectRuleId: json['project_rule_id'] as int?,
      leaveCategoryId: json['leave_category_id'] as int?,
      allowedAvailableDays: json['allowed_available_days'] as int?,
      createdAt: DateTime.tryParse((json['created_at'] as String?) ?? ''),
      updatedAt: DateTime.tryParse((json['updated_at'] as String?) ?? ''),
      date: DateTime.tryParse((json['date'] as String?) ?? ''),
      name: json['name'] as String?,
      leaveCategory: json['leave_category'] == null
          ? null
          : HolidayCategoryModel.fromJson(json['leave_category'] as Map<String, dynamic>),
      userProjectLogs: json['user_project_logs'] == null
          ? []
          : List<UserProjectLog>.from(
              (json['user_project_logs'] as List<dynamic>).map(
                (e) => UserProjectLog.fromJson(e as Map<String, dynamic>),
              ),
            ),
    );
  }

  final int? id;
  final int? projectId;
  final int? projectRuleId;
  final int? leaveCategoryId;
  final int? allowedAvailableDays;
  final DateTime? createdAt;
  final DateTime? date;
  final DateTime? updatedAt;
  final String? name;
  final HolidayCategoryModel? leaveCategory;
  final List<UserProjectLog> userProjectLogs;

  Map<String, dynamic> toJson() => {
        'id': id,
        'project_id': projectId,
        'project_rule_id': projectRuleId,
        'leave_category_id': leaveCategoryId,
        'allowed_available_days': allowedAvailableDays,
        'created_at': createdAt?.toIso8601String(),
        'updated_at': updatedAt?.toIso8601String(),
        'name': name,
        'date': date?.toIso8601String(),
        'leave_category': leaveCategory?.toJson(),
        'user_project_logs': userProjectLogs.map((e) => e.toJson()).toList(),
      };

  @override
  List<Object?> get props {
    return [
      id,
      projectId,
      projectRuleId,
      leaveCategoryId,
      allowedAvailableDays,
      createdAt,
      date,
      updatedAt,
      name,
      leaveCategory,
      userProjectLogs,
    ];
  }

  ProjectRuleCategoryModel copyWith({
    int? id,
    int? projectId,
    int? projectRuleId,
    int? leaveCategoryId,
    int? allowedAvailableDays,
    DateTime? createdAt,
    DateTime? date,
    DateTime? updatedAt,
    String? name,
    HolidayCategoryModel? leaveCategory,
    List<UserProjectLog>? userProjectLogs,
  }) {
    return ProjectRuleCategoryModel(
      id: id ?? this.id,
      projectId: projectId ?? this.projectId,
      projectRuleId: projectRuleId ?? this.projectRuleId,
      leaveCategoryId: leaveCategoryId ?? this.leaveCategoryId,
      allowedAvailableDays: allowedAvailableDays ?? this.allowedAvailableDays,
      createdAt: createdAt ?? this.createdAt,
      date: date ?? this.date,
      updatedAt: updatedAt ?? this.updatedAt,
      name: name ?? this.name,
      leaveCategory: leaveCategory ?? this.leaveCategory,
      userProjectLogs: userProjectLogs ?? this.userProjectLogs,
    );
  }

  @override
  bool get stringify => true;
}
