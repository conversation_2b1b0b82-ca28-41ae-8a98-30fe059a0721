import 'package:equatable/equatable.dart';

class UserProjectConflictDatesModel extends Equatable {
  const UserProjectConflictDatesModel({
    this.id,
    this.projectId,
    this.date,
    this.metaData,
    this.createdAt,
    this.updatedAt,
    this.userId,
  });

  factory UserProjectConflictDatesModel.fromJson(Map<String, dynamic> json) {
    return UserProjectConflictDatesModel(
      id: json['id'] as int?,
      projectId: json['project_id'] as int?,
      date: DateTime.tryParse((json['date'] as String?) ?? ''),
      metaData: json['meta_data'] as String?,
      createdAt: DateTime.tryParse((json['created_at'] as String?) ?? ''),
      updatedAt: DateTime.tryParse((json['updated_at'] as String?) ?? ''),
      userId: json['user_id'] as int?,
    );
  }

  final int? id;
  final int? projectId;
  final DateTime? date;
  final String? metaData;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final int? userId;

  Map<String, dynamic> toJson() => {
        'id': id,
        'project_id': projectId,
        'date': date?.toIso8601String(),
        'meta_data': metaData,
        'created_at': createdAt?.toIso8601String(),
        'updated_at': updatedAt?.toIso8601String(),
        'user_id': userId,
      };

  @override
  List<Object?> get props => [
        id,
        projectId,
        date,
        metaData,
        createdAt,
        updatedAt,
        userId,
      ];
}
