// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:flutter/material.dart';

class SelectAvailabilityCategoryModel {
  final String? title;
  final TextEditingController? controller;

  SelectAvailabilityCategoryModel({this.title, this.controller});

  SelectAvailabilityCategoryModel copyWith({
    String? title,
    TextEditingController? controller,
  }) {
    return SelectAvailabilityCategoryModel(
      title: title ?? this.title,
      controller: controller ?? this.controller,
    );
  }
}
