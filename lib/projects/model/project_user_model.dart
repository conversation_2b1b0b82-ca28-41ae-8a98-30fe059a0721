import 'package:equatable/equatable.dart';
import 'package:excel_app/users/model/user_model.dart';

class ProjectUserModel extends Equatable {
  const ProjectUserModel({
    this.id,
    this.projectId,
    this.userId,
    this.maxShift,
    this.minShift,
    this.maxWeekendShift,
    this.minWeekendShift,
    this.canUserProhibitedDays,
    this.status,
    this.createdAt,
    this.updatedAt,
    this.userModel,
    this.assignDate,
  });

  factory ProjectUserModel.fromJson(Map<String, dynamic> json) {
    return ProjectUserModel(
      id: json['id'] as int?,
      projectId: json['project_id'] as int?,
      userId: json['user_id'] as int?,
      maxShift: json['max_shift'] as int?,
      minShift: json['min_shift'] as int?,
      maxWeekendShift: json['max_weekend_shift'] as int?,
      minWeekendShift: json['min_weekend_shift'] as int?,
      canUserProhibitedDays: json['can_use_prohibited_days'] as int?,
      status: json['status'] as String?,
      createdAt: DateTime.tryParse((json['created_at'] as String?) ?? ''),
      updatedAt: DateTime.tryParse((json['updated_at'] as String?) ?? ''),
      userModel: json['user'] == null ? null : UserModel.fromJson(json['user'] as Map<String, dynamic>),
      assignDate: json['assign_date'] == null
          ? []
          : List<DateTime>.from(
              (json['assign_date'] as List<dynamic>).map(
                (e) => DateTime.tryParse(e as String) ?? DateTime.now(),
              ),
            ),
    );
  }

  final int? id;
  final int? projectId;
  final int? userId;
  final int? maxShift;
  final int? minShift;
  final int? maxWeekendShift;
  final int? minWeekendShift;
  final int? canUserProhibitedDays;
  final String? status;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final UserModel? userModel;
  final List<DateTime>? assignDate;

  Map<String, dynamic> toJson() => {
        'id': id,
        'project_id': projectId,
        'user_id': userId,
        'max_shift': maxShift,
        'min_shift': minShift,
        'max_weekend_shift': maxWeekendShift,
        'min_weekend_shift': minWeekendShift,
        'can_use_prohibited_days': canUserProhibitedDays,
        'status': status,
        'created_at': createdAt?.toIso8601String(),
        'updated_at': updatedAt?.toIso8601String(),
        'user': userModel?.toJson(),
        'assign_date': assignDate?.map((e) => e.toIso8601String()).toList(),
      };

  @override
  List<Object?> get props => [
        id,
        projectId,
        userId,
        maxShift,
        minShift,
        maxWeekendShift,
        minWeekendShift,
        canUserProhibitedDays,
        status,
        createdAt,
        updatedAt,
        userModel,
        assignDate,
      ];
}
