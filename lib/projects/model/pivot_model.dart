import 'package:equatable/equatable.dart';

class PivotModel extends Equatable {
  const PivotModel({
    this.userId,
    this.projectId,
  });

  factory PivotModel.fromJson(Map<String, dynamic> json) {
    return PivotModel(
      userId: json['user_id'] as int?,
      projectId: json['project_id'] as int?,
    );
  }

  final int? userId;
  final int? projectId;

  Map<String, dynamic> toJson() => {
        'user_id': userId,
        'project_id': projectId,
      };

  @override
  List<Object?> get props => [
        userId,
        projectId,
      ];
}
