// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:excel_app/holiday_category/model/holiday_catergory_model.dart';
import 'package:flutter/material.dart';

class ShiftConfigurationModel {
  final String? userType;
  final TextEditingController maximumPerShiftController;
  final TextEditingController minimumPerShiftController;
  final TextEditingController preferredUserPerShiftController;
  final TextEditingController? selectAvailabilityCategoryController;
  final TextEditingController avoidShiftDayaBeforeOrAfterController;
  final List<HolidayCategoryModel>? availabilityCategories;
  final int canYouProhibitedDays;

  ShiftConfigurationModel({
    required this.minimumPerShiftController,
    required this.maximumPerShiftController,
    required this.avoidShiftDayaBeforeOrAfterController,
    required this.preferredUserPerShiftController,
    this.canYouProhibitedDays = 1,
    this.userType,
    this.selectAvailabilityCategoryController,
    this.availabilityCategories,
  });

  ShiftConfigurationModel copyWith({
    String? userType,
    TextEditingController? maximumPerShiftController,
    TextEditingController? minimumPerShiftController,
    TextEditingController? preferredUserPerShiftController,
    TextEditingController? selectAvailabilityCategoryController,
    TextEditingController? avoidShiftDayaBeforeOrAfterController,
    List<HolidayCategoryModel>? availabilityCategories,
    int? canYouProhibitedDays,
  }) {
    return ShiftConfigurationModel(
      userType: userType ?? this.userType,
      maximumPerShiftController: maximumPerShiftController ?? this.maximumPerShiftController,
      minimumPerShiftController: minimumPerShiftController ?? this.minimumPerShiftController,
      preferredUserPerShiftController: preferredUserPerShiftController ?? this.preferredUserPerShiftController,
      selectAvailabilityCategoryController:
          selectAvailabilityCategoryController ?? this.selectAvailabilityCategoryController,
      avoidShiftDayaBeforeOrAfterController:
          avoidShiftDayaBeforeOrAfterController ?? this.avoidShiftDayaBeforeOrAfterController,
      availabilityCategories: availabilityCategories ?? this.availabilityCategories,
      canYouProhibitedDays: canYouProhibitedDays ?? this.canYouProhibitedDays,
    );
  }
}
