import 'package:excel_app/app/cubit/refresh_cubit.dart';
import 'package:excel_app/constants/app_colors.dart';
import 'package:excel_app/custom_calendar/cubit/custom_calendar_cubit.dart';
import 'package:excel_app/injector/injector.dart';
import 'package:excel_app/l10n/l10n_extension.dart';
import 'package:excel_app/projects/model/holiday_model.dart';
import 'package:excel_app/projects/model/project_model.dart';
import 'package:excel_app/projects/repository/i_project_repository.dart';
import 'package:excel_app/projects/widget/holiday_listview_widget.dart';
import 'package:excel_app/utility/enums/enum_file.dart';
import 'package:excel_app/utility/helpers/utility.dart';
import 'package:excel_app/widget/container_widget.dart';
import 'package:excel_app/widget/custome_button_gradiant_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';

class HolidaysListView extends StatefulWidget {
  const HolidaysListView({
    required this.onNextAndUpdateTap,
    required this.nextOnTap,
    required this.cancelOnTap,
    super.key,
    this.onDeleteTap,
    this.projectDetailData,
  });

  final void Function(DateTime value)? onDeleteTap;
  final void Function() onNextAndUpdateTap;
  final void Function() nextOnTap;
  final void Function() cancelOnTap;
  final ProjectModel? projectDetailData;

  @override
  State<HolidaysListView> createState() => _HolidaysListViewState();
}

class _HolidaysListViewState extends State<HolidaysListView> {
  // List<Holidays> selectedHolidaysDate = [];

  @override
  void initState() {
    super.initState();
  }

  final isUpdateAndNextLoading = ValueNotifier<bool>(false);

  @override
  Widget build(BuildContext context) {
    return ContainerWidget(
      margin: const EdgeInsets.only(left: 25, right: 25, bottom: 25),
      constraints: const BoxConstraints(maxWidth: 250),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Expanded(
            child: BlocSelector<CustomCalendarCubit, CustomCalendarState, List<HolidaysModel>>(
              selector: (state) => state.selectedDates,
              builder: (context, holidays) {
                return ListView.builder(
                  shrinkWrap: true,
                  itemCount: holidays.length,
                  physics: const ScrollPhysics(),
                  itemBuilder: (context, index) {
                    return HolidayListViewWidget(
                      bgColor: holidays[index].bgColor ?? AppColors.white,
                      title: holidays[index].holidayTypeViewString,
                      dates: holidays[index].dates,
                      onDeleteTap: widget.onDeleteTap,
                    );
                  },
                );
              },
            ),
          ),
          Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CustomeButtonGradiantWidget(
                    buttonText: context.l10n.cancle,
                    pading: const EdgeInsets.symmetric(horizontal: 26),
                    isUseContainerBorder: true,
                    onTap: () {
                      widget.cancelOnTap.call();
                    },
                  ),
                  const Gap(15),
                  CustomeButtonGradiantWidget(
                    isGradient: true,
                    buttonText: 'Skip',
                    pading: const EdgeInsets.symmetric(horizontal: 26),
                    onTap: () {
                      widget.nextOnTap.call();
                    },
                  ),
                ],
              ),
              // BlocSelector<CustomCalendarCubit, CustomCalendarState, bool>(
              //   selector: (state) => state.selectedDates.isNotEmpty,
              //   builder: (context, haveDates) {
              //     if (!haveDates) return const SizedBox();
              //     return Column(
              //       children: [
              //         const Gap(15),
              //         ValueListenableBuilder(
              //           valueListenable: isUpdateAndNextLoading,
              //           builder: (context, loading, _) {
              //             return CustomeButtonGradiantWidget(
              //               isGradient: true,
              //               width: 183,
              //               isLoading: loading,
              //               buttonText: 'Next & Update Project',
              //               onTap: updateProject,
              //             );
              //           },
              //         ),
              //       ],
              //     );
              //   },
              // ),
              Column(
                children: [
                  const Gap(15),
                  ValueListenableBuilder(
                    valueListenable: isUpdateAndNextLoading,
                    builder: (context, loading, _) {
                      return CustomeButtonGradiantWidget(
                        isGradient: true,
                        width: 183,
                        isLoading: loading,
                        buttonText: 'Next & Update Project',
                        onTap: updateProject,
                      );
                    },
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  Future<void> updateProject() async {
    isUpdateAndNextLoading.value = true;
    final selectedDates = context.read<CustomCalendarCubit>().state.selectedDates;
    final failOrSuccess = await getIt<IProjectRepository>().updateProject(
      projectId: widget.projectDetailData?.id.toString() ?? '',
      holidayAvailabilityList: selectedDates,
    );
    failOrSuccess.fold(
      (l) {
        Utility.toast(message: l.message);
        isUpdateAndNextLoading.value = false;
      },
      (r) {
        Utility.toast(message: r.message);
        isUpdateAndNextLoading.value = false;
        widget.onNextAndUpdateTap.call();
        context.read<RefreshCubit>().modifyProject(r.data, ProjectAction.edit);
      },
    );
  }
}
