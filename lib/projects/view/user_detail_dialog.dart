import 'package:excel_app/constants/app_constants.dart';
import 'package:excel_app/projects/model/user_setting_model.dart';
import 'package:excel_app/projects/view/user_availability_dailog_view.dart';
import 'package:excel_app/projects/view/user_detail_dailog_view.dart';
import 'package:excel_app/utility/helpers/utility.dart';
import 'package:excel_app/widget/app_choice_chip.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';

class UserDetailDialog extends StatefulWidget {
  const UserDetailDialog({super.key, this.userSettingModel, this.projectId});
  final UserSettingModel? userSettingModel;
  final int? projectId;

  @override
  State<UserDetailDialog> createState() => _UserDetailDialogState();
}

class _UserDetailDialogState extends State<UserDetailDialog> {
  final selectedMethod = ValueNotifier<String>(AppConstants.availability);
  final selectedStatus = ValueNotifier<String?>('Approved');
  final List<String> statusList = ['Approved', 'Rejected'];
  final minShiftController = TextEditingController();
  final maxShiftController = TextEditingController();
  final weekendShiftController = TextEditingController();
  final firstDateController = TextEditingController(text: Utility().dateFormat(date: DateTime.now()));
  final secondDateController = TextEditingController(text: Utility().dateFormat(date: DateTime.now()));
  final thirdDateController = TextEditingController(text: Utility().dateFormat(date: DateTime.now()));

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
      child: Container(
        width: 490,
        padding: const EdgeInsets.fromLTRB(0, 20, 0, 30),
        child: ValueListenableBuilder<String>(
          valueListenable: selectedMethod,
          builder: (context, method, _) {
            return Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'User Details',
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                      ),
                ),
                const Gap(20),
                Row(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    AppChoiceChip(
                      labelPadding: const EdgeInsets.symmetric(vertical: 4, horizontal: 18),
                      title: 'Availability',
                      isSelected: method == AppConstants.availability,
                      onSelected: () {
                        selectedMethod.value = AppConstants.availability;
                      },
                    ),
                    const Gap(6),
                    AppChoiceChip(
                      labelPadding: const EdgeInsets.symmetric(vertical: 4, horizontal: 33),
                      title: 'Details',
                      isSelected: method == AppConstants.details,
                      onSelected: () {
                        selectedMethod.value = AppConstants.details;
                      },
                    ),
                  ],
                ),
                const Gap(20),
                if (method == AppConstants.availability)
                  UserAvailabilityDailogView(
                    projectId: widget.projectId.toString(),
                    userId: widget.userSettingModel?.userModel?.id.toString() ?? '',
                  ),
                if (method == AppConstants.details)
                  Flexible(
                    child: UserDetailDailogView(
                      userSettingModel: widget.userSettingModel,
                    ),
                  ),
              ],
            );
          },
        ),
      ),
    );
  }
}
