import 'dart:developer';

import 'package:excel_app/app/cubit/refresh_cubit.dart';
import 'package:excel_app/constants/app_assets.dart';
import 'package:excel_app/constants/app_colors.dart';
import 'package:excel_app/constants/app_constants.dart';
import 'package:excel_app/injector/injector.dart';
import 'package:excel_app/l10n/l10n_extension.dart';
import 'package:excel_app/projects/model/project_model.dart';
import 'package:excel_app/projects/model/user_setting_model.dart';
import 'package:excel_app/projects/repository/i_project_repository.dart';
import 'package:excel_app/projects/view/user_detail_dialog.dart';
import 'package:excel_app/projects/widget/import_project_user.dart';
import 'package:excel_app/projects/widget/user_setting_widget_responsive.dart';
import 'package:excel_app/users/model/user_model.dart';
import 'package:excel_app/users/widget/user_alert_dialog_box.dart';
import 'package:excel_app/utility/enums/enum_file.dart';
import 'package:excel_app/utility/helpers/utility.dart';
import 'package:excel_app/widget/app_asset_image.dart';
import 'package:excel_app/widget/app_text_form_field.dart';
import 'package:excel_app/widget/container_widget.dart';
import 'package:excel_app/widget/custome_button_gradiant_widget.dart';
import 'package:excel_app/widget/dialogs.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';

class UserAndSettingView extends StatefulWidget {
  const UserAndSettingView({
    required this.onBackTap,
    required this.onUpdateTap,
    super.key,
    this.projectDetailData,
  });
  final ProjectModel? projectDetailData;
  final void Function() onBackTap;
  final void Function() onUpdateTap;

  @override
  State<UserAndSettingView> createState() => _UserAndSettingViewState();
}

class _UserAndSettingViewState extends State<UserAndSettingView> {
  final scrollPaginationController = ScrollController();
  final searchController = TextEditingController();

  final selectedUserValue = ValueNotifier<int?>(null);
  final listOfUsers = ValueNotifier<List<UserSettingModel>>([]);
  final searchOfUser = ValueNotifier<List<UserSettingModel>>([]);
  String? filePath;
  final isUpdateLoading = ValueNotifier<bool>(false);
  final formKey = GlobalKey<FormState>();
  final isDeleteLoading = ValueNotifier<bool>(false);

  @override
  void initState() {
    super.initState();
    if (widget.projectDetailData != null) getProjectData();
  }

  void _onSearchChanged() {
    final searchQuery = searchController.text.trim().toLowerCase();

    if (searchQuery.isEmpty) {
      listOfUsers.value = [...searchOfUser.value]; // Reset to original list
    } else {
      listOfUsers.value = searchOfUser.value.where((userSetting) {
        final user = userSetting.userModel;
        if (user == null) return false;

        // Add more fields to search as needed
        return user.name!.toLowerCase().contains(searchQuery) ||
            user.userRoleViewString.toLowerCase().contains(searchQuery);
      }).toList();
    }
  }

  Future<void> getProjectData() async {
    final projectUserData = widget.projectDetailData?.projectUsers;
    if (projectUserData != null && projectUserData.isNotEmpty) {
      listOfUsers.value = projectUserData.map((user) {
        log('${user.canUserProhibitedDays}extra wisw');
        return UserSettingModel(
          projectId: user.projectId,
          userModel: user.userModel,
          projectLeaderId: widget.projectDetailData?.leaderType == AppConstants.leaderUser
              ? widget.projectDetailData?.leaderUserId
              : null,
          canYouProhibitedDays: user.canUserProhibitedDays,
          maxShift: TextEditingController(text: user.maxShift?.toString() ?? ''),
          minShift: TextEditingController(text: user.minShift?.toString() ?? ''),
          maxWeekEndShift: TextEditingController(text: user.maxWeekendShift?.toString() ?? ''),
          minWeekEndShift: TextEditingController(text: user.minWeekendShift?.toString() ?? ''),
          assignDateAndControllerModel: () {
            // Get existing dates from user data
            final existingDates = user.assignDate?.map((date) {
                  return AssignDateAndControllerModel(
                    assignDateController: TextEditingController(text: Utility().dateYearFormat(date: date)),
                    assignDate: date,
                  );
                }).toList() ??
                [];

            // Ensure at least 3 date fields by default
            while (existingDates.length < 3) {
              existingDates.add(
                AssignDateAndControllerModel(
                  assignDateController: TextEditingController(),
                ),
              );
            }

            return existingDates;
          }(),
        );
      }).toList();
      searchOfUser.value = listOfUsers.value;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Flexible(
      child: ContainerWidget(
        margin: const EdgeInsets.only(left: 20, right: 20, bottom: 20),
        padding: EdgeInsets.zero,
        child: Form(
          key: formKey,
          child: ValueListenableBuilder(
            valueListenable: listOfUsers,
            builder: (context, list, _) {
              return Column(
                children: [
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.only(left: 20, right: 20, bottom: 20, top: 20),
                      child: Column(
                        children: [
                          LayoutBuilder(
                            builder: (context, constraints) {
                              final isSmallScreen = constraints.maxWidth < 800;

                              if (isSmallScreen) {
                                return Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    // First row: Title and Search
                                    Row(
                                      children: [
                                        Expanded(
                                          child: (list.isNotEmpty)
                                              ? Text(
                                                  '${list.length} Users',
                                                  style: Theme.of(context)
                                                      .textTheme
                                                      .displayMedium
                                                      ?.copyWith(fontSize: 20, letterSpacing: 0.5),
                                                )
                                              : const Text(''),
                                        ),
                                      ],
                                    ),
                                    const Gap(12),
                                    // Second row: Action buttons
                                    SingleChildScrollView(
                                      scrollDirection: Axis.horizontal,
                                      child: Row(
                                        children: [
                                          AppTextFormField(
                                            maxWidth: 200,
                                            controller: searchController,
                                            prefixIcon: AppAssets.searchIcon,
                                            hintText: 'Search',
                                            onChanged: (value) => _onSearchChanged(),
                                          ),
                                          const Gap(12),
                                          InkWell(
                                            onTap: () {
                                              DailogBox.showBluredBgDailog(
                                                context,
                                                ImportProjectUserDialog(
                                                  projectId: widget.projectDetailData?.id.toString() ?? '',
                                                ),
                                              );
                                            },
                                            child: Container(
                                              width: 140,
                                              height: 38,
                                              decoration: BoxDecoration(
                                                borderRadius: BorderRadius.circular(10),
                                                color: AppColors.gray,
                                              ),
                                              child: Row(
                                                mainAxisAlignment: MainAxisAlignment.center,
                                                children: [
                                                  const AppAssetImage(AppAssets.importUserIcon),
                                                  const Gap(8),
                                                  Text(
                                                    'Import Users',
                                                    style: Theme.of(context)
                                                        .textTheme
                                                        .headlineSmall
                                                        ?.copyWith(fontSize: 13, color: AppColors.subText),
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ),
                                          const Gap(12),
                                          _buildSelectUserButton(context, list, isSmallScreen: true),
                                        ],
                                      ),
                                    ),
                                  ],
                                );
                              }

                              // Desktop layout
                              return Row(
                                children: [
                                  Expanded(
                                    child: (list.isNotEmpty)
                                        ? Text(
                                            '${list.length} Users',
                                            style: Theme.of(context)
                                                .textTheme
                                                .displayMedium
                                                ?.copyWith(fontSize: 24, letterSpacing: 0.5),
                                          )
                                        : const Text(''),
                                  ),
                                  AppTextFormField(
                                    maxWidth: 250,
                                    controller: searchController,
                                    prefixIcon: AppAssets.searchIcon,
                                    hintText: 'Search',
                                    onChanged: (value) => _onSearchChanged(),
                                  ),
                                  const Gap(16),
                                  InkWell(
                                    onTap: () {
                                      DailogBox.showBluredBgDailog(
                                        context,
                                        ImportProjectUserDialog(
                                          projectId: widget.projectDetailData?.id.toString() ?? '',
                                        ),
                                      );
                                    },
                                    child: Container(
                                      width: 152,
                                      height: 38,
                                      decoration:
                                          BoxDecoration(borderRadius: BorderRadius.circular(10), color: AppColors.gray),
                                      child: Row(
                                        mainAxisAlignment: MainAxisAlignment.center,
                                        children: [
                                          const AppAssetImage(
                                            AppAssets.importUserIcon,
                                          ),
                                          const Gap(10),
                                          Text(
                                            'Import Users',
                                            style: Theme.of(context)
                                                .textTheme
                                                .headlineSmall
                                                ?.copyWith(fontSize: 15, color: AppColors.subText),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                  const Gap(16),
                                  _buildSelectUserButton(context, list),
                                ],
                              );
                            },
                          ),
                          const Gap(20),
                          if (list.isEmpty && searchController.text.isNotEmpty)
                            Utility.noDataWidget(text: 'No User', context: context),
                          Flexible(
                            child: Container(
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(10),
                                color: AppColors.tableGray,
                                border: list.isEmpty ? null : Border.all(color: AppColors.strokeColor),
                              ),
                              child: Column(
                                mainAxisSize: MainAxisSize.min,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  if (list.isNotEmpty)
                                    Container(
                                      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
                                      decoration: const BoxDecoration(
                                        border: Border(
                                          bottom: BorderSide(
                                            color: AppColors.strokeColor,
                                          ),
                                        ),
                                      ),
                                      child: Row(
                                        children: [
                                          Expanded(
                                            child: Text(
                                              'User',
                                              overflow: TextOverflow.ellipsis,
                                              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                                                    fontSize: 15,
                                                  ),
                                            ),
                                          ),
                                          Text(
                                            'Action',
                                            overflow: TextOverflow.ellipsis,
                                            style: Theme.of(context).textTheme.titleSmall?.copyWith(
                                                  fontSize: 15,
                                                ),
                                          ),
                                          const Gap(17),
                                        ],
                                      ),
                                    ),
                                  ValueListenableBuilder(
                                    valueListenable: selectedUserValue,
                                    builder: (context, selectedIndex, _) {
                                      return Flexible(
                                        child: ListView.builder(
                                          itemCount: list.length,
                                          shrinkWrap: true,
                                          itemBuilder: (context, index) {
                                            return UserSettingWidget(
                                              index: index,
                                              startDate: widget.projectDetailData?.startDate,
                                              endDate: widget.projectDetailData?.endDate,
                                              isSelected: list[index].isActionOpen,
                                              userSettingModel: list[index],
                                              oneEyesTap: () {
                                                DailogBox.showBluredBgDailog(
                                                  context,
                                                  UserDetailDialog(
                                                    userSettingModel: list[index],
                                                    projectId: widget.projectDetailData?.id,
                                                  ),
                                                );
                                              },
                                              onTap: () {
                                                final updatedItem = list[index].copyWith(
                                                  isActionOpen: !list[index].isActionOpen,
                                                );
                                                final updatedList = [...list];
                                                updatedList[index] = updatedItem;
                                                listOfUsers.value = updatedList;
                                              },
                                              isDeleteonTap: () {
                                                if (listOfUsers.value[index].projectId != null) {
                                                  DailogBox.showBluredBgDailog(
                                                    context,
                                                    ValueListenableBuilder(
                                                      valueListenable: isDeleteLoading,
                                                      builder: (context, loading, _) {
                                                        return UserAlertDialogBox(
                                                          imageIcon: AppAssets.closeCircleIcon,
                                                          title: 'Delete User',
                                                          message: 'Are you sure you want to delete this user?',
                                                          firstButtonTitle: 'Cancel',
                                                          secondButtonTitle: 'Delete',
                                                          isLoading: loading,
                                                          firstButtonOnTap: () {
                                                            context.pop();
                                                          },
                                                          secondButtonOnTap: () {
                                                            deleteUser(
                                                              userId: list[index].userModel?.id.toString() ?? '',
                                                            );
                                                          },
                                                        );
                                                      },
                                                    ),
                                                  );
                                                } else {
                                                  listOfUsers.value = [...listOfUsers.value]..removeWhere(
                                                      (element) => element.userModel?.id == list[index].userModel?.id,
                                                    );
                                                }
                                              },
                                            );
                                          },
                                        ),
                                      );
                                    },
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  ContainerWidget(
                    margin: EdgeInsets.zero,
                    borderRadius:
                        const BorderRadius.only(bottomLeft: Radius.circular(20), bottomRight: Radius.circular(20)),
                    isUseBoxShadow: true,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        CustomeButtonGradiantWidget(
                          width: 100,
                          buttonText: context.l10n.back,
                          isUseContainerBorder: true,
                          onTap: () {
                            widget.onBackTap.call();
                          },
                        ),
                        const Gap(15),
                        ValueListenableBuilder(
                          valueListenable: isUpdateLoading,
                          builder: (context, loading, _) {
                            return CustomeButtonGradiantWidget(
                              width: 134,
                              isGradient: true,
                              isLoading: loading,
                              buttonText: 'Update Project',
                              onTap: () {
                                if (formKey.currentState!.validate()) {
                                  updateProject();
                                }
                              },
                            );
                          },
                        ),
                      ],
                    ),
                  ),
                ],
              );
            },
          ),
        ),
      ),
    );
  }

  Future<void> updateProject() async {
    isUpdateLoading.value = true;
    final failOrSuccess = await getIt<IProjectRepository>().updateProject(
      projectId: widget.projectDetailData?.id.toString() ?? '',
      userSettingsList: listOfUsers.value,
    );
    failOrSuccess.fold(
      (l) {
        Utility.toast(message: l.message);
        isUpdateLoading.value = false;
      },
      (r) {
        isUpdateLoading.value = false;
        context.read<RefreshCubit>().modifyProject(r.data, ProjectAction.edit);
        Utility.toast(message: r.message);
        // widget.onUpdateTap.call();
      },
    );
  }

  Future<void> deleteUser({required String userId}) async {
    isDeleteLoading.value = true;
    final response = await getIt<IProjectRepository>()
        .deleteUserForProject(userId: userId, projectId: widget.projectDetailData?.id.toString() ?? '');

    await response.fold(
      (l) {
        isDeleteLoading.value = false;
        context.pop();
        Utility.toast(message: l.message);
      },
      (r) async {
        Utility.toast(message: r.message);
        context.pop();
        listOfUsers.value = [...listOfUsers.value]..removeWhere(
            (element) => element.userModel?.id.toString() == userId,
          );

// Remove the user from the projectUsers list
        final updatedUsers = widget.projectDetailData?.projectUsers.where((e) {
          log('${e.userModel?.id}$userId users ids');
          return e.userModel?.id.toString() != userId;
        }).toList();

// If users are updated, create a new project model with the updated user list
        final updatedProjectData = widget.projectDetailData?.copyWith(
          projectUsers: updatedUsers ?? [],
        );
        context.read<RefreshCubit>().modifyProject(updatedProjectData, ProjectAction.edit);
        isDeleteLoading.value = false;
      },
    );
  }

  Widget _buildSelectUserButton(BuildContext context, List<UserSettingModel> list, {bool isSmallScreen = false}) {
    if (list.isEmpty && (widget.projectDetailData?.userTypes.isEmpty ?? false)) {
      return Tooltip(
        message: 'Specify rules in shift settings to enable this.',
        child: Container(
          height: 38,
          width: isSmallScreen ? 100 : 113,
          decoration: BoxDecoration(borderRadius: BorderRadius.circular(10), color: AppColors.gray),
          child: Center(
            child: Text(
              'Select User',
              style: Theme.of(context)
                  .textTheme
                  .headlineSmall
                  ?.copyWith(fontSize: isSmallScreen ? 13 : 15, color: AppColors.subText),
            ),
          ),
        ),
      );
    }

    if (widget.projectDetailData?.userTypes.isNotEmpty ?? false) {
      return ValueListenableBuilder(
        valueListenable: listOfUsers,
        builder: (context, list, _) {
          return CustomeButtonGradiantWidget(
            onTap: () async {
              final user = list.map<UserModel>((e) => e.userModel ?? const UserModel()).toList();
              final selectedUser = await DailogBox.multipleSelectUserDailog(
                context,
                selectedUser: user,
                projectmodel: widget.projectDetailData,
                isFromUserSettings: true,
              );
              if (selectedUser != null) {
                listOfUsers.value = [
                  ...listOfUsers.value,
                  for (final user in selectedUser)
                    if (!listOfUsers.value.any((config) => config.userModel?.id == user.id))
                      UserSettingModel(
                        userModel: user,
                        maxShift: TextEditingController(),
                        minShift: TextEditingController(),
                        maxWeekEndShift: TextEditingController(),
                        minWeekEndShift: TextEditingController(),
                        assignDateAndControllerModel: [
                          AssignDateAndControllerModel(
                            assignDateController: TextEditingController(),
                          ),
                          AssignDateAndControllerModel(
                            assignDateController: TextEditingController(),
                          ),
                          AssignDateAndControllerModel(
                            assignDateController: TextEditingController(),
                          ),
                        ],
                      ),
                ];
                searchOfUser.value = [...listOfUsers.value];
              }
            },
            height: 38,
            width: isSmallScreen ? 100 : 113,
            isGradient: true,
            child: Text(
              'Select User',
              style: Theme.of(context)
                  .textTheme
                  .headlineSmall
                  ?.copyWith(fontSize: isSmallScreen ? 13 : 15, color: AppColors.white),
            ),
          );
        },
      );
    }

    return const SizedBox.shrink();
  }
}
