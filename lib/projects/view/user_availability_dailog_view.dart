// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:developer';

import 'package:collection/collection.dart';
import 'package:excel_app/constants/app_colors.dart';
import 'package:excel_app/injector/injector.dart';
import 'package:excel_app/projects/model/project_rules_category_model.dart';
import 'package:excel_app/projects/repository/i_project_repository.dart';
import 'package:excel_app/users/widget/user_availability_view.dart';
import 'package:excel_app/utility/helpers/utility.dart';
import 'package:excel_app/widget/availability_status_view.dart';
import 'package:excel_app/widget/common_dialog_box.dart';
import 'package:excel_app/widget/custome_button_gradiant_widget.dart';
import 'package:excel_app/widget/dialogs.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';

class UserProjectModelNew {
  final String? leaveCategoryName;
  final String? leaveCategoryId;
  final Color? bgColor;
  final List<ProjectRuleCategoryModel>? ruleCategoryList;
  UserProjectModelNew({
    this.leaveCategoryName,
    this.leaveCategoryId,
    this.ruleCategoryList,
    this.bgColor,
  });

  UserProjectModelNew copyWith({
    String? leaveCategoryName,
    String? leaveCategoryId,
    List<ProjectRuleCategoryModel>? ruleCategoryList,
    Color? bgColor,
  }) {
    return UserProjectModelNew(
      leaveCategoryName: leaveCategoryName ?? this.leaveCategoryName,
      leaveCategoryId: leaveCategoryId ?? this.leaveCategoryId,
      ruleCategoryList: ruleCategoryList ?? this.ruleCategoryList,
      bgColor: bgColor ?? this.bgColor,
    );
  }

  @override
  String toString() {
    return 'UserProjectModelNew(leaveCategoryName: $leaveCategoryName, leaveCategoryId: $leaveCategoryId, bgColor: $bgColor, ruleCategoryList: $ruleCategoryList)';
  }
}

class UserAvailabilityDailogView extends StatefulWidget {
  const UserAvailabilityDailogView({super.key, this.projectId, this.userId});
  final String? projectId;
  final String? userId;

  @override
  State<UserAvailabilityDailogView> createState() => _UserAvailabilityDailogViewState();
}

class _UserAvailabilityDailogViewState extends State<UserAvailabilityDailogView> {
  final isLoading = ValueNotifier<bool>(false);
  final userAvailabilityList = ValueNotifier<List<ProjectRuleCategoryModel>>([]);
  final projectAvailabilityList = ValueNotifier<List<UserProjectModelNew>>([]);
  final checkStatus = ValueNotifier<bool>(false);
  final isOkLoading = ValueNotifier<bool>(false);

  @override
  void initState() {
    super.initState();
    getUserAvailabilityDetails();
  }

  Future<void> getUserAvailabilityDetails() async {
    isLoading.value = true;
    final failOrSucess = await getIt<IProjectRepository>().getUserAvailability(
      projectId: widget.projectId.toString(),
      userId: widget.userId.toString(),
    );
    failOrSucess.fold(
      (l) {
        isLoading.value = false;
        Utility.toast(message: l.message);
      },
      (r) {
        userAvailabilityList.value = r.data;
        getInfo();
        isLoading.value = false;
      },
    );
  }

  void getInfo() {
    checkStatus.value = userAvailabilityList.value.expand((element) => element.userProjectLogs).toList().isNotEmpty;
    log('${checkStatus}check status');
    projectAvailabilityList.value = userAvailabilityList.value.fold<List<UserProjectModelNew>>([], (list, e) {
      final existingConfig = list.firstWhereOrNull(
        (config) => config.leaveCategoryName == e.leaveCategory?.name,
      );
      if (existingConfig != null) {
        existingConfig.ruleCategoryList?.add(e);
      } else {
        // If it doesn't exist, create a new entry
        list.add(
          UserProjectModelNew(
            leaveCategoryName: e.leaveCategory?.name,
            leaveCategoryId: e.leaveCategory?.id.toString(),
            ruleCategoryList: [e],
            bgColor: e.leaveCategory?.color,
          ),
        );
      }
      return list;
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    return Flexible(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ValueListenableBuilder(
            valueListenable: checkStatus,
            builder: (context, stutus, _) {
              if (stutus) {
                return Padding(
                  padding: const EdgeInsets.fromLTRB(20, 0, 20, 14),
                  child: Row(
                    children: [
                      Text(
                        'Status:',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w500),
                      ),
                      const Gap(10),
                      const AvailabilityStatusView(color: AppColors.redOrange, text: 'Rejected'),
                    ],
                  ),
                );
              }
              return const SizedBox.shrink();
            },
          ),
          Container(
            height: 1,
            color: AppColors.strokeColor,
          ),
          const Gap(14),
          Flexible(
            child: ValueListenableBuilder(
              valueListenable: projectAvailabilityList,
              builder: (context, list, _) {
                return ValueListenableBuilder(
                  valueListenable: isLoading,
                  builder: (context, loading, _) {
                    if (loading) {
                      return Utility.progressIndicator();
                    } else if (!loading && list.isEmpty) {
                      return Center(child: Utility.noDataWidget(text: 'No data found', context: context));
                    }
                    return ListView.builder(
                      padding: const EdgeInsets.symmetric(horizontal: 20),
                      shrinkWrap: true,
                      itemCount: list.length,
                      physics: const ScrollPhysics(),
                      itemBuilder: (context, index) {
                        return UserAvailabilityView(
                          key: UniqueKey(),
                          info: list[index],
                          notesOnTap: (ruleCategoryModel) {
                            DailogBox.showBluredBgDailog(
                              barrierDismissible: false,
                              context,
                              CommonDialogBox(
                                controller:
                                    TextEditingController(text: ruleCategoryModel?.userProjectLogs.first.rejectReason),
                                title: 'Reason for Rejection',
                              ),
                            );
                          },
                          onDeleteTap: (updatedModel) {
                            if (updatedModel != null) {
                              projectAvailabilityList.value = [
                                ...[...projectAvailabilityList.value]
                                    .map(
                                      (e) => e.copyWith(
                                        ruleCategoryList:
                                            e.ruleCategoryList?.where((rule) => rule.id != updatedModel.id).toList(),
                                      ),
                                    )
                                    .toList()
                                  ..removeWhere((e) => e.ruleCategoryList == null || e.ruleCategoryList!.isEmpty),
                              ];
                            }
                          },
                        );
                      },
                    );
                  },
                );
              },
            ),
          ),
          const Gap(10),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              ValueListenableBuilder(
                valueListenable: projectAvailabilityList,
                builder: (context, list, _) {
                  if (list.isNotEmpty) {
                    return ValueListenableBuilder(
                      valueListenable: isOkLoading,
                      builder: (context, loading, _) {
                        return CustomeButtonGradiantWidget(
                          onTap: userProjectUpdateAvailability,
                          isGradient: true,
                          buttonText: 'Ok',
                          isLoading: loading,
                          height: 38,
                          width: 100,
                        );
                      },
                    );
                  }
                  return const SizedBox.shrink();
                },
              ),
            ],
          ),
        ],
      ),
    );
  }

  Future<void> userProjectUpdateAvailability() async {
    isOkLoading.value = true;
    final failOrSuccess = await getIt<IProjectRepository>().updateUserProjectAvailability(
      projectId: widget.projectId ?? '',
      holidays: projectAvailabilityList.value,
    );
    failOrSuccess.fold(
      (l) {
        Utility.toast(message: l.message);
        isOkLoading.value = false;
      },
      (r) {
        Utility.toast(message: r.message);
        isOkLoading.value = false;
        context.pop();
      },
    );
  }
}
