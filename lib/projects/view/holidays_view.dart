// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:excel_app/constants/app_colors.dart';
import 'package:excel_app/constants/app_constants.dart';
import 'package:excel_app/custom_calendar/cubit/custom_calendar_cubit.dart';
import 'package:excel_app/custom_calendar/models/custom_calender_config.dart';
import 'package:excel_app/custom_calendar/views/custom_calendar.dart';
import 'package:excel_app/injector/injector.dart';
import 'package:excel_app/l10n/l10n_extension.dart';
import 'package:excel_app/projects/model/project_model.dart';
import 'package:excel_app/projects/view/holiday_list_view.dart';
import 'package:excel_app/widget/app_drop_down_widget.dart';
import 'package:excel_app/widget/container_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class HolidayViewWrapper extends StatelessWidget {
  const HolidayViewWrapper({
    required this.onNextAndUpdateTap,
    required this.nextOnTap,
    super.key,
    this.projectDetailData,
  });
  final void Function() onNextAndUpdateTap;
  final void Function() nextOnTap;
  final ProjectModel? projectDetailData;

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => getIt<CustomCalendarCubit>(param1: projectDetailData)
        ..setHolidayKey(holidayKey: AppConstants.holiday, color: AppColors.periwinkle)
        ..init(),
      child: HolidaysView(
        onNextAndUpdateTap: onNextAndUpdateTap,
        nextOnTap: nextOnTap,
        projectDetailData: projectDetailData,
      ),
    );
  }
}

class HolidaysView extends StatelessWidget {
  const HolidaysView({required this.onNextAndUpdateTap, required this.nextOnTap, super.key, this.projectDetailData});
  final void Function() onNextAndUpdateTap;
  final void Function() nextOnTap;
  final ProjectModel? projectDetailData;

  @override
  Widget build(BuildContext context) {
    return Builder(
      builder: (context) {
        return Flexible(
          child: Row(
            children: [
              Expanded(
                flex: 4,
                child: ContainerWidget(
                  margin: const EdgeInsets.only(left: 25, bottom: 25),
                  padding: const EdgeInsets.only(left: 20, right: 20, bottom: 20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Padding(
                        padding: const EdgeInsets.symmetric(vertical: 20),
                        child: Wrap(
                          crossAxisAlignment: WrapCrossAlignment.center,
                          spacing: 20,
                          runSpacing: 20,
                          children: [
                            Text('Add Holidays', style: Theme.of(context).textTheme.titleSmall),
                            BlocSelector<CustomCalendarCubit, CustomCalendarState, String?>(
                              selector: (state) => state.currentHolidayKey,
                              builder: (context, key) {
                                return AppDropDown<String>(
                                  maxWidth: 300,
                                  selectedValue: key,
                                  hintText: 'Select Type',
                                  onSelect: (newKey) {
                                    if (newKey == null) return;
                                    context
                                        .read<CustomCalendarCubit>()
                                        .setHolidayKey(holidayKey: newKey, color: AppColors.getHolidayColor(newKey));
                                  },
                                  items: AppConstants.holidaysList
                                      .map(
                                        (e) => DropdownMenuItem<String>(
                                          value: e,
                                          child: Text(
                                            AppConstants.holidayNameFromKey(e),
                                          ),
                                        ),
                                      )
                                      .toList(),
                                  validator: (p0) {
                                    if (p0 == null) {
                                      return context.l10n.pleaseSelectType;
                                    }
                                    return null;
                                  },
                                );
                              },
                            ),
                          ],
                        ),
                      ),
                      Flexible(
                        child: CustomCalender(
                          customCalendarCubit: context.read<CustomCalendarCubit>(),
                          config: CustomCalenderConfig(
                            startDate: projectDetailData?.startDate ?? DateTime.now(),
                            endDate: projectDetailData?.endDate ?? DateTime.now(),
                            firstDayOfWeek: projectDetailData?.weekdayOfFirstOfWeek ?? 0,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              HolidaysListView(
                projectDetailData: projectDetailData,
                onDeleteTap: (value) {
                  context.read<CustomCalendarCubit>().removeSelectedDate(value);
                },
                onNextAndUpdateTap: onNextAndUpdateTap.call,
                nextOnTap: nextOnTap.call,
                cancelOnTap: () {
                  context.read<CustomCalendarCubit>().init();
                },
              ),
            ],
          ),
        );
      },
    );
  }
}
