import 'dart:developer';

import 'package:easy_debounce/easy_debounce.dart';
import 'package:excel_app/app/bloc/authentication_bloc.dart';
import 'package:excel_app/calender/list_view/project_monthly_cal_view.dart';
import 'package:excel_app/calender/model/project_staff_model.dart';
import 'package:excel_app/constants/app_assets.dart';
import 'package:excel_app/constants/app_colors.dart';
import 'package:excel_app/constants/app_constants.dart';
import 'package:excel_app/constants/app_strings.dart';
import 'package:excel_app/holiday_category/model/holiday_catergory_model.dart';
import 'package:excel_app/injector/injector.dart';
import 'package:excel_app/projects/model/project_model.dart';
import 'package:excel_app/projects/model/swap_availability_model.dart';
import 'package:excel_app/projects/model/user_project_calendar_date_model.dart';
import 'package:excel_app/projects/repository/i_project_repository.dart';
import 'package:excel_app/projects/response/project_planning_response.dart';
import 'package:excel_app/projects/widget/manual_assign_dialog.dart';
import 'package:excel_app/projects/widget/user_auto_assign_conflict_dailog.dart';
import 'package:excel_app/projects/widget/user_conflict_dailog.dart';
import 'package:excel_app/users/widget/user_alert_dialog_box.dart';
import 'package:excel_app/utility/helpers/utility.dart';
import 'package:excel_app/widget/app_asset_image.dart';
import 'package:excel_app/widget/common_dialog_box.dart';
import 'package:excel_app/widget/container_widget.dart';
import 'package:excel_app/widget/custome_button_gradiant_widget.dart';
import 'package:excel_app/widget/dialogs.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';

class PlanningDetailsView extends StatefulWidget {
  const PlanningDetailsView({
    super.key,
    this.projectDetailData,
  });
  final ProjectModel? projectDetailData;

  @override
  State<PlanningDetailsView> createState() => _PlanningDetailsViewState();
}

class _PlanningDetailsViewState extends State<PlanningDetailsView> {
  final projectStartDate = ValueNotifier<DateTime>(DateTime.now());
  final projectEndDate = ValueNotifier<DateTime>(DateTime.now());
  final userData = ValueNotifier<ProjectPlanningResponse?>(null);
  final projectUserList = ValueNotifier<List<ProjectStaffMember>>([]);
  final listOfHolidayCategory = ValueNotifier<List<HolidayCategoryModel>>([]);
  final startMonthDate = ValueNotifier<DateTime>(DateTime.now());
  final endMonthDate = ValueNotifier<DateTime>(DateTime.now());
  final swapValue = ValueNotifier<SwapUserAvailabilityDate?>(null);
  final isSwapLoading = ValueNotifier<bool>(false);

  final isAlert = ValueNotifier<bool>(false);

  final rejectReason = TextEditingController();

  final isLoading = ValueNotifier<bool>(false);
  final isButtonLoading = ValueNotifier<bool>(false);
  final isApprovePublishLoading = ValueNotifier<bool>(false);
  final isManualAssignLoading = ValueNotifier<bool>(false);

  // Add a ValueNotifier to track the current project data
  final projectDetailData = ValueNotifier<ProjectModel?>(null);

  final userProjectConflictDates = ValueNotifier<List<UserProjectCalendarDateModel>>([]);
  DateTime oneMonthBeforeEndDate = DateTime.now();
  @override
  void initState() {
    super.initState();
    // Initialize the projectDetailData ValueNotifier with the widget's projectDetailData
    projectDetailData.value = widget.projectDetailData;
    _initializeDates();
    getProjectPlanningDetails();
  }

  void _initializeDates() {
    projectStartDate.value = projectDetailData.value?.startDate ?? DateTime.now();
    projectEndDate.value = projectDetailData.value?.endDate ?? DateTime.now();
    startMonthDate.value = DateTime(projectStartDate.value.year, projectStartDate.value.month);
    endMonthDate.value = DateTime(projectStartDate.value.year, projectStartDate.value.month + 1, 0);
    oneMonthBeforeEndDate = DateTime(
      projectDetailData.value?.endDate?.year ?? DateTime.now().year,
      projectDetailData.value?.endDate?.month ?? DateTime.now().month - 1,
    );
  }

  void _changeMonth(int offset) {
    projectStartDate.value = DateTime(
      projectStartDate.value.year,
      projectStartDate.value.month + offset,
    );
    startMonthDate.value = DateTime(projectStartDate.value.year, projectStartDate.value.month);
    endMonthDate.value = DateTime(projectStartDate.value.year, projectStartDate.value.month + 1, 0);
    listOfHolidayCategory.value = [];
    getProjectPlanningDetails();
  }

  Future<void> getProjectPlanningDetails() async {
    isLoading.value = true;
    final failOrSucess = await getIt<IProjectRepository>().projectPlanning(
      projectId: projectDetailData.value?.id.toString() ?? '',
      startDate: startMonthDate.value,
      endDate: endMonthDate.value,
    );
    failOrSucess.fold(
      (l) {
        isLoading.value = false;
        Utility.toast(message: l.message);
      },
      (r) {
        isLoading.value = false;
        userData.value = r;
        if (r.assignmentStatus == AppConstants.inProgress) {
          openAutoAssignConflictDailog(
            message: 'Assignment is in progress. Please check in a while...',
            autoAssignInProgress: true,
          );
        }
        projectDetails();
      },
    );
  }

  Future<void> projectDetails() async {
    final projectUserData = userData.value?.data;
    if (projectUserData != null) {
      projectUserList.value = projectUserData.map((user) {
        userProjectConflictDates.value = user.userProjectConflictDates.map((e) => e).toList();
        log('${userProjectConflictDates.value.length}userProjectConflictDates');
        if (!isAlert.value) {
          if (userProjectConflictDates.value.isNotEmpty) {
            openConflictDailog();
          }
        }

        // Extract leave categories using the model's method
        final leaveCategories = user.extractLeaveCategories();
        if (leaveCategories.isNotEmpty) {
          // Create a set of existing category IDs for quick lookup
          final existingCategoryIds = listOfHolidayCategory.value.map((cat) => cat.id).toSet();

          // Only add categories that don't already exist in the list
          final uniqueNewCategories = leaveCategories.where((cat) => !existingCategoryIds.contains(cat.id)).toList();

          if (uniqueNewCategories.isNotEmpty) {
            listOfHolidayCategory.value = [...listOfHolidayCategory.value, ...uniqueNewCategories];
          }
        }

        final userList = user;
        return ProjectStaffMember(
          userModel: userList,
        );
      }).toList();
    }
    isLoading.value = false;
  }

  void openConflictDailog() {
    isAlert.value = true;
    DailogBox.showBluredBgDailog<UserProjectCalendarDateModel?>(
      context,
      UserConflictAlertDailog(
        listConflict: userProjectConflictDates.value,
      ),
    ).then((result) {
      if (result != null) {
        if (result.date != null) {
          projectStartDate.value = DateTime(
            result.date!.year,
            result.date!.month,
          );
          startMonthDate.value = DateTime(result.date!.year, result.date!.month);
          endMonthDate.value = DateTime(result.date!.year, result.date!.month + 1, 0);
          getProjectPlanningDetails();
        }
      }
    });
  }

  void openAutoAssignConflictDailog({String? message, bool autoAssignInProgress = false}) {
    isAlert.value = true;
    DailogBox.showBluredBgDailog<void>(
      context,
      barrierDismissible: false,
      UserAutoAssignConflictAlertDailog(
        message: message,
        autoAssignInProgress: autoAssignInProgress,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AuthenticationBloc, AuthenticationState>(
      builder: (context, state) {
        return Flexible(
          child: SingleChildScrollView(
            child: ContainerWidget(
              padding: EdgeInsets.zero,
              borderRadius: const BorderRadius.only(
                bottomLeft: Radius.circular(20),
                bottomRight: Radius.circular(20),
              ),
              margin: const EdgeInsets.fromLTRB(25, 0, 25, 25),
              child: ValueListenableBuilder(
                valueListenable: isLoading,
                builder: (context, loading, _) {
                  if (!loading && projectUserList.value.isEmpty) {
                    return Column(
                      children: [
                        Center(child: Utility.noDataWidget(text: 'User not assigned', context: context)),
                        const Gap(20),
                      ],
                    );
                  }
                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Container(
                        height: 1,
                        color: AppColors.strokeColor,
                      ),
                      Padding(
                        padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 20),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            // Left side - Month selection
                            Row(
                              children: [
                                InkWell(
                                  onTap: () {
                                    EasyDebounce.debounce('change_month', const Duration(milliseconds: 500), () {
                                      if (projectStartDate.value
                                          .isAfter(projectDetailData.value?.startDate ?? DateTime.now())) {
                                        _changeMonth(-1);
                                      }
                                    });
                                  },
                                  child: AppAssetImage(
                                    AppAssets.leftArrowIcon,
                                    color: projectStartDate.value
                                            .isAfter(projectDetailData.value?.startDate ?? DateTime.now())
                                        ? AppColors.primary
                                        : AppColors.subText,
                                  ),
                                ),
                                const Gap(18),
                                Text(
                                  Utility().formatMonthYear(projectStartDate.value),
                                  style: Theme.of(context).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600),
                                ),
                                const Gap(18),
                                InkWell(
                                  onTap: () {
                                    EasyDebounce.debounce('change_month', const Duration(milliseconds: 500), () {
                                      if (projectStartDate.value.isBefore(oneMonthBeforeEndDate)) {
                                        _changeMonth(1);
                                      }
                                    });
                                  },
                                  child: AppAssetImage(
                                    AppAssets.rightArrowIcon,
                                    color: projectStartDate.value.isBefore(oneMonthBeforeEndDate)
                                        ? AppColors.primary
                                        : AppColors.subText,
                                  ),
                                ),
                              ],
                            ),
                            // Right side - Action buttons
                            Row(
                              children: [
                                ValueListenableBuilder(
                                  valueListenable: isManualAssignLoading,
                                  builder: (context, manualLoading, _) {
                                    return ValueListenableBuilder<ProjectModel?>(
                                      valueListenable: projectDetailData,
                                      builder: (context, project, _) {
                                        // Disable button when isAssigned is 0, enable when it's 1
                                        final isButtonDisabled = project?.isAssigned != 1;

                                        return CustomeButtonGradiantWidget(
                                          onTap: () {
                                            EasyDebounce.debounce(
                                              'manual_assign_dialog',
                                              const Duration(milliseconds: 500),
                                              showManualAssignDialog,
                                            );
                                          },
                                          width: 158,
                                          buttonText: 'Manual Assign',
                                          isGradient: true,
                                          height: 36,
                                          isLoading: manualLoading,
                                          isDisabled: isButtonDisabled,
                                        );
                                      },
                                    );
                                  },
                                ),
                                const Gap(16),
                                ValueListenableBuilder(
                                  valueListenable: isButtonLoading,
                                  builder: (context, loading, _) {
                                    return ValueListenableBuilder<ProjectModel?>(
                                      valueListenable: projectDetailData,
                                      builder: (context, project, _) {
                                        // Disable button when isAssigned is 0, enable when it's 1
                                        // final isButtonDisabled = project?.isAssigned != 1;

                                        return CustomeButtonGradiantWidget(
                                          onTap: () {
                                            EasyDebounce.debounce(
                                              'auto_assign_shift',
                                              const Duration(milliseconds: 500),
                                              autoAssignShift,
                                            );
                                          },
                                          width: 158,
                                          buttonText: 'Auto Assign Shift',
                                          isGradient: true,
                                          height: 36,
                                          isLoading: loading,
                                          // isDisabled: isButtonDisabled,
                                        );
                                      },
                                    );
                                  },
                                ),
                                const Gap(16),
                                if (state is Authenticated)
                                  ValueListenableBuilder(
                                    valueListenable: isApprovePublishLoading,
                                    builder: (context, loading, _) {
                                      // Determine if button should be disabled
                                      final isButtonDisabled = state.user.role == AppStrings.admin
                                          ? projectDetailData.value?.isAssigned != 1
                                          : !(projectDetailData.value?.isSubmittedForApproval == 0 &&
                                              projectDetailData.value?.isAssigned == 1);

                                      return CustomeButtonGradiantWidget(
                                        onTap: () {
                                          if (!loading) {
                                            if (state.user.role == AppStrings.admin) {
                                              if (projectDetailData.value?.isAssigned == 1) {
                                                publishApi();
                                              }
                                            } else {
                                              uploadForApprovalApi();
                                            }
                                          }
                                        },
                                        buttonText: state.user.role == AppStrings.admin
                                            ? 'Approve & Publish'
                                            : ' Upload for Approval',
                                        isGradient: true,
                                        isLoading: loading,
                                        height: 36,
                                        width: 165,
                                        isDisabled: isButtonDisabled,
                                        buttonColor: state.user.role == AppStrings.admin
                                            ? AppColors.primary
                                            : AppColors.uploadOrangeColor,
                                      );
                                    },
                                  ),
                              ],
                            ),
                          ],
                        ),
                      ),
                      Flexible(
                        child: ValueListenableBuilder(
                          valueListenable: isLoading,
                          builder: (context, loading, _) {
                            if (loading) {
                              return Utility.progressIndicator();
                            }
                            return Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                ValueListenableBuilder(
                                  valueListenable: projectStartDate,
                                  builder: (context, month, _) {
                                    return ValueListenableBuilder(
                                      valueListenable: projectUserList,
                                      builder: (context, userList, _) {
                                        return ValueListenableBuilder<ProjectModel?>(
                                          valueListenable: projectDetailData,
                                          builder: (context, project, _) {
                                            return ProjectMonthlyListCalenderView(
                                              key: UniqueKey(),
                                              isFromProjectPlanning: true,
                                              projectLeaderId: project?.leaderType == AppConstants.leaderUser
                                                  ? project?.leaderUserId ?? 0
                                                  : null,
                                              projectCalendarDates: project?.projectCalendarDates,
                                              userProjectConflictDates: userProjectConflictDates.value,
                                              currentMonth: month,
                                              staffMembers: userList,
                                              onTap: rejectLeaveDailogBox,
                                              isAdmin: state is Authenticated && state.user.role == AppConstants.admin,
                                              leaderId: project?.leaderUserId,
                                            );
                                          },
                                        );
                                      },
                                    );
                                  },
                                ),
                                const Gap(14),
                                ValueListenableBuilder(
                                  valueListenable: listOfHolidayCategory,
                                  builder: (context, cat, _) {
                                    return SizedBox(
                                      height: 18,
                                      child: ListView.builder(
                                        itemCount: cat.length,
                                        shrinkWrap: true,
                                        padding: const EdgeInsets.symmetric(horizontal: 15),
                                        scrollDirection: Axis.horizontal,
                                        itemBuilder: (context, index) {
                                          return categoryWidget(
                                            holidayCategoryModel: cat[index],
                                          );
                                        },
                                      ),
                                    );
                                  },
                                ),
                                const Gap(14),
                              ],
                            );
                          },
                        ),
                      ),
                    ],
                  );
                },
              ),
            ),
          ),
        );
      },
    );
  }

  Future<void> rejectLeaveDailogBox(UserProjectCalendarDateModel p0) async {
    await DailogBox.showBluredBgDailog(
      barrierDismissible: false,
      context,
      ValueListenableBuilder(
        valueListenable: isButtonLoading,
        builder: (context, loading, _) {
          return CommonDialogBox(
            controller: rejectReason,
            title: 'Reason for Rejection',
            isLoading: loading,
            isHideButton: p0.metaData != null,
            firstButtonOnTap: () {},
            secondButtonOnTap: () {
              rejectLeave(userProjectCalendarDate: p0);
            },
            firstButtonTitle: 'Reject Anyway',
            secondButtonTitle: 'Reject',
          );
        },
      ),
    );
  }

  Future<void> userAvailabilitySwapDailogBox() async {
    await DailogBox.showBluredBgDailog(
      context,
      barrierDismissible: false,
      ValueListenableBuilder(
        valueListenable: isSwapLoading,
        builder: (context, loading, _) {
          return UserAlertDialogBox(
            imageIcon: AppAssets.swapIcon,
            title: 'Swap User Availability Date',
            message: 'Are you sure you want to swap this user availability date?',
            firstButtonTitle: 'Cancel',
            secondButtonTitle: 'Swap',
            isLoading: loading,
            firstButtonOnTap: () {
              swapValue.value = null;
              context.pop(context);
            },
            secondButtonOnTap: swapUserAvailabilityDate,
          );
        },
      ),
    );
  }

  Future<void> showManualAssignDialog() async {
    isManualAssignLoading.value = true;

    // Show the manual assignment dialog
    await showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return ManualAssignDialog(
          currentMonth: projectStartDate.value,
          staffMembers: projectUserList.value,
          projectCalendarDates: projectDetailData.value?.projectCalendarDates,
          userProjectConflictDates: userProjectConflictDates.value,
          projectDetailData: projectDetailData.value,
          onAssignComplete: () {
            // Refresh the data after successful assignment
            listOfHolidayCategory.value = [];
            _initializeDates();
            getProjectPlanningDetails();
          },
        );
      },
    );

    isManualAssignLoading.value = false;
  }

  Future<void> autoAssignShift() async {
    isButtonLoading.value = true;

    final failOrSuccess = await getIt<IProjectRepository>().autoAssignShift(
      projectId: projectDetailData.value?.id.toString() ?? '',
    );
    failOrSuccess.fold(
      (l) {
        isButtonLoading.value = false;
        openAutoAssignConflictDailog(message: l.message);
        // getProjectPlanningDetails();
        // _initializeDates();
      },
      (r) {
        Utility.toast(message: r.message);
        isButtonLoading.value = false;
        listOfHolidayCategory.value = [];
        _initializeDates();
        getProjectPlanningDetails();
      },
    );
  }

  Future<void> rejectLeave({UserProjectCalendarDateModel? userProjectCalendarDate}) async {
    isButtonLoading.value = true;

    final failOrSuccess = await getIt<IProjectRepository>().rejectLeave(
      projectId: projectDetailData.value?.id ?? 0,
      rejectReason: rejectReason.text.trim(),
      userProjectCalendarDateId: userProjectCalendarDate?.id ?? 0,
    );
    failOrSuccess.fold(
      (l) {
        Utility.toast(message: l.message);
        context.pop();
        rejectReason.clear();
        isButtonLoading.value = false;
      },
      (r) {
        Utility.toast(message: r.message);
        context.pop();
        rejectReason.clear();
        isButtonLoading.value = false;
      },
    );
  }

  Future<void> uploadForApprovalApi({UserProjectCalendarDateModel? userProjectCalendarDate}) async {
    isApprovePublishLoading.value = true;

    final failOrSuccess = await getIt<IProjectRepository>().uploadForApproval(
      projectId: projectDetailData.value?.id.toString() ?? '',
    );
    failOrSuccess.fold(
      (l) {
        Utility.toast(message: l.message);
        isApprovePublishLoading.value = false;
      },
      (r) {
        Utility.toast(message: r.message);

        if (projectDetailData.value != null) {
          final updatedProject = projectDetailData.value?.copyWith(
            isSubmittedForApproval: 1,
          );

          projectDetailData.value = updatedProject;
        }

        isApprovePublishLoading.value = false;
      },
    );
  }

  Future<void> publishApi({UserProjectCalendarDateModel? userProjectCalendarDate}) async {
    isApprovePublishLoading.value = true;

    final failOrSuccess = await getIt<IProjectRepository>().publish(
      projectId: projectDetailData.value?.id.toString() ?? '',
    );
    failOrSuccess.fold(
      (l) {
        Utility.toast(message: l.message);

        isApprovePublishLoading.value = false;
      },
      (r) {
        Utility.toast(message: r.message);

        isApprovePublishLoading.value = false;
      },
    );
  }

  Future<void> swapUserAvailabilityDate() async {
    isSwapLoading.value = true;

    final failOrSuccess = await getIt<IProjectRepository>().swapAvailabilityDate(
      projectId: projectDetailData.value?.id.toString() ?? '',
      swapUserAvailabilityDate: swapValue.value,
    );
    failOrSuccess.fold(
      (l) {
        Utility.toast(message: l.message);
        isSwapLoading.value = false;
        swapValue.value = null;
        context.pop();
      },
      (r) {
        Utility.toast(message: r.message);
        isSwapLoading.value = false;
        swapValue.value = null;
        context.pop();
        listOfHolidayCategory.value = [];
        getProjectPlanningDetails();
      },
    );
  }

  Widget categoryWidget({HolidayCategoryModel? holidayCategoryModel}) {
    return Row(
      children: [
        Container(
          height: 18,
          width: 18,
          color: AppColors.hexToColor(holidayCategoryModel?.colorHex ?? ''),
        ),
        const Gap(8),
        Text(
          holidayCategoryModel?.name ?? '',
          style: Theme.of(context).textTheme.bodySmall,
        ),
        const Gap(20),
      ],
    );
  }
}
