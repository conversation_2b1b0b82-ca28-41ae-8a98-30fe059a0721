import 'dart:developer';

import 'package:collection/collection.dart';
import 'package:excel_app/app/cubit/refresh_cubit.dart';
import 'package:excel_app/constants/app_assets.dart';
import 'package:excel_app/constants/app_constants.dart';
import 'package:excel_app/holiday_category/model/holiday_catergory_model.dart';
import 'package:excel_app/holiday_category/repository/i_holiday_catergory_repository.dart';
import 'package:excel_app/injector/injector.dart';
import 'package:excel_app/l10n/l10n_extension.dart';
import 'package:excel_app/projects/model/project_model.dart';
import 'package:excel_app/projects/model/shift_configuration_model.dart';
import 'package:excel_app/projects/repository/i_project_repository.dart';
import 'package:excel_app/projects/widget/select_availability_dailog.dart';
import 'package:excel_app/projects/widget/select_user_type_dailog.dart';
import 'package:excel_app/projects/widget/shift_configuration_widget.dart';
import 'package:excel_app/utility/enums/enum_file.dart';
import 'package:excel_app/utility/helpers/utility.dart';
import 'package:excel_app/widget/app_asset_image.dart';
import 'package:excel_app/widget/app_text_form_field.dart';
import 'package:excel_app/widget/container_widget.dart';
import 'package:excel_app/widget/custome_button_gradiant_widget.dart';
import 'package:excel_app/widget/dialogs.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';

class ShiftSettingsView extends StatefulWidget {
  const ShiftSettingsView({
    required this.onBackTap,
    required this.onNextAndUpdateTap,
    required this.onNextTap,
    super.key,
    this.projectDetailData,
  });
  final void Function() onBackTap;
  final void Function() onNextAndUpdateTap;
  final void Function() onNextTap;

  final ProjectModel? projectDetailData;

  @override
  State<ShiftSettingsView> createState() => _ShiftSettingsViewState();
}

class _ShiftSettingsViewState extends State<ShiftSettingsView> {
  final userSelectTypeController = TextEditingController();
  final userPerShiftController = TextEditingController();
  final doctorPerShiftController = TextEditingController();
  final workingDayPerMonthDoctorController = TextEditingController();
  final firstYearResidentPerShiftController = TextEditingController();
  final workingDayPerMonthPerResidentController = TextEditingController();

  DateTime? avilabilityInputDeadlineDate;
  final availabilityInputDeadlineController = TextEditingController();
  final personLeavePerMonthController = TextEditingController();

  // final selectedLeaderType = ValueNotifier<String?>(AppConstants.doctor);

  final yearController = TextEditingController();
  final daysController = TextEditingController();

  // final selectedUserType = ValueNotifier<String?>(AppConstants.firstYearResident);

  final shiftOnOffSwitch = ValueNotifier<bool>(true);
  final formKey = GlobalKey<FormState>();

  final selectedUserTypes = ValueNotifier<List<String>?>([]);
  final shiftConfigurationList = ValueNotifier<List<ShiftConfigurationModel>?>([]);

  final isNextLoading = ValueNotifier<bool>(false);
  // List<HolidayCategoryModel> holidayCategoryList = [];

  @override
  void initState() {
    super.initState();
    if (widget.projectDetailData != null) getProjectData();
    getHolidayCategory();
  }

  final holidayCategoryList = ValueNotifier<List<HolidayCategoryModel>>([]);
  Future<void> getHolidayCategory() async {
    final failOrSucess = await getIt<IHolidayCategoryRepository>().getHolidayCategory(
      isActive: 1,
    );
    failOrSucess.fold(
      (l) {},
      (r) {
        holidayCategoryList.value = r.data;
      },
    );
  }

  Future<void> getProjectData() async {
    final projectRules = widget.projectDetailData?.projectRules ?? [];
    if (projectRules.isEmpty) return;

    // Set selected user types and controllers
    selectedUserTypes.value = projectRules.map((e) => e.userTypeViewString).toList();
    userSelectTypeController.text = selectedUserTypes.value!.join(', ');
    userPerShiftController.text = widget.projectDetailData?.userPerShift?.toString() ?? '';
    avilabilityInputDeadlineDate = widget.projectDetailData?.availabilityInputDeadline;
    availabilityInputDeadlineController.text = Utility().dateFormat(
      date: avilabilityInputDeadlineDate,
    );

    // Configure shift settings
    shiftConfigurationList.value = projectRules.map((rule) {
      final availableCategories = rule.projectRuleCategories.map((category) {
        return HolidayCategoryModel(
          controller: TextEditingController(text: category.allowedAvailableDays.toString()),
          id: category.leaveCategoryId,
          name: category.name,
        );
      }).toList();

      return ShiftConfigurationModel(
        userType: rule.userTypeViewString,
        availabilityCategories: availableCategories,
        minimumPerShiftController: TextEditingController(text: rule.minimumUserPerShift?.toString() ?? ''),
        maximumPerShiftController: TextEditingController(text: rule.maximumUserPerShift?.toString() ?? ''),
        preferredUserPerShiftController: TextEditingController(text: rule.preferredUserPerShift?.toString() ?? ''),
        avoidShiftDayaBeforeOrAfterController: TextEditingController(text: rule.bufferDays?.toString() ?? ''),
        canYouProhibitedDays: rule.canUseProhibitedDays ?? 1,
      );
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<RefreshCubit, RefreshState>(
      listener: (context, state) {
        if (state is ModifyHolidayCategory) {
          switch (state.action) {
            case HolidayCategoryAction.add:
              holidayCategoryList.value = [state.category, ...holidayCategoryList.value];
            case HolidayCategoryAction.edit:
              holidayCategoryList.value = holidayCategoryList.value
                  .map((e) {
                    if (e.id == state.category.id) {
                      if (state.category.isActive == 1) {
                        return state.category;
                      }
                      return null;
                    }
                    return e;
                  })
                  .whereType<HolidayCategoryModel>()
                  .toList();

              if (state.category.isActive == 1 && !holidayCategoryList.value.any((c) => c.id == state.category.id)) {
                holidayCategoryList.value = [...holidayCategoryList.value, state.category];
              }
          }
        }
      },
      child: Flexible(
        child: SingleChildScrollView(
          child: Form(
            key: formKey,
            // autovalidateMode: AutovalidateMode.onUserInteraction,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                ValueListenableBuilder(
                  valueListenable: selectedUserTypes,
                  builder: (context, value, _) {
                    return ContainerWidget(
                      margin: const EdgeInsets.only(bottom: 25, left: 25, right: 25),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Wrap(
                            spacing: 20,
                            runSpacing: 20,
                            children: [
                              AppTextFormField(
                                maxWidth: 300,
                                title: 'Select user type*',
                                readOnly: true,
                                suffixIcon: const Padding(
                                  padding: EdgeInsets.all(10),
                                  child: AppAssetImage(
                                    AppAssets.arrowDownIcon,
                                  ),
                                ),
                                controller: userSelectTypeController,
                                onTap: () async {
                                  if (shiftConfigurationList.value != null &&
                                      shiftConfigurationList.value!.isNotEmpty) {
                                    log('${shiftConfigurationList.value?[0].maximumPerShiftController.text}value data jay thakkar');
                                  }
                                  final list = await DailogBox.showBluredBgDailog<List<String>>(
                                    context,
                                    barrierDismissible: false,
                                    SelectUserTypeDialog(
                                      title: 'Select user type',
                                      listData: AppConstants.typeList,
                                      selectedlistData: value,
                                      leaderRoll: widget.projectDetailData?.leaderType == AppConstants.leaderUser
                                          ? widget.projectDetailData?.leader?.userRoleViewString
                                          : null,
                                    ),
                                  );

                                  if (list != null && list.isNotEmpty) {
                                    userSelectTypeController.text = list.join(', ');
                                    selectedUserTypes.value = [
                                      ...list,
                                    ];

                                    shiftConfigurationList.value = list.map((selectedUserType) {
                                      // Check if the user type already exists in the list
                                      final existingConfig = shiftConfigurationList.value?.firstWhereOrNull(
                                        (config) {
                                          return config.userType == selectedUserType;
                                        },
                                      );
                                      if (existingConfig == null) {
                                        return ShiftConfigurationModel(
                                          userType: selectedUserType,
                                          maximumPerShiftController: TextEditingController(),
                                          minimumPerShiftController: TextEditingController(),
                                          preferredUserPerShiftController: TextEditingController(),
                                          selectAvailabilityCategoryController: TextEditingController(),
                                          avoidShiftDayaBeforeOrAfterController: TextEditingController(),
                                          availabilityCategories: [],
                                        );
                                      }
                                      // If it exists, return the updated version; otherwise, return a new model
                                      return existingConfig.copyWith(
                                        userType: selectedUserType,
                                        maximumPerShiftController: existingConfig.maximumPerShiftController,
                                        minimumPerShiftController: existingConfig.minimumPerShiftController,
                                        selectAvailabilityCategoryController:
                                            existingConfig.selectAvailabilityCategoryController,
                                        avoidShiftDayaBeforeOrAfterController:
                                            existingConfig.avoidShiftDayaBeforeOrAfterController,
                                        availabilityCategories: existingConfig.availabilityCategories,
                                      );
                                    }).toList();
                                  } else {
                                    userSelectTypeController.clear();
                                    selectedUserTypes.value = [];
                                    shiftConfigurationList.value = [];
                                  }
                                },
                              ),
                              AppTextFormField(
                                controller: userPerShiftController,
                                title: 'Minimum user per shift*',
                                keyboardType: TextInputType.number,
                                inputFormatters: [
                                  FilteringTextInputFormatter.digitsOnly,
                                  LengthLimitingTextInputFormatter(4),
                                  FilteringTextInputFormatter.allow(RegExp('^[1-9][0-9]*')),
                                ],
                                maxWidth: 300,
                                validator: (value) {
                                  if (value == null || value.trim().isEmpty) {
                                    return context.l10n.pleaeseEnterUserPerShift;
                                  }
                                  return null;
                                },
                              ),
                              AppTextFormField(
                                controller: availabilityInputDeadlineController,
                                title: '${context.l10n.availabilityInputDeadline}*',
                                maxWidth: 300,
                                readOnly: true,
                                onTap: () async {
                                  final date = await Utility.datePicker(
                                    context: context,
                                    initialDate: avilabilityInputDeadlineDate,
                                    // firstDate: widget.projectDetailData?.startDate != null &&
                                    //         widget.projectDetailData!.startDate!.isAfter(DateTime.now())
                                    //     ? widget.projectDetailData!.startDate
                                    //     : DateTime.now(),
                                    // initialDate: widget.projectDetailData?.startDate == null ||
                                    //         widget.projectDetailData!.startDate!.isBefore(DateTime.now())
                                    //     ? DateTime.now()
                                    //     : widget.projectDetailData!.startDate,
                                    // lastdate: widget.projectDetailData?.endDate,
                                  );
                                  if (date != null) {
                                    avilabilityInputDeadlineDate = date;
                                    availabilityInputDeadlineController.text = Utility().dateFormat(
                                      date: date,
                                    );
                                  }
                                  // If date is null, we don't set any default value
                                  // This keeps the field empty if the user cancels the date picker
                                },
                                validator: (value) {
                                  if (value == null || value.trim().isEmpty) {
                                    return context.l10n.pleaseEnterAvailabilityInputDeadline;
                                  }
                                  return null;
                                },
                              ),
                            ],
                          ),
                        ],
                      ),
                    );
                  },
                ),
                ValueListenableBuilder(
                  valueListenable: shiftConfigurationList,
                  builder: (context, selectedConfiguration, _) {
                    return ValueListenableBuilder(
                      valueListenable: holidayCategoryList,
                      builder: (context, categoryList, _) {
                        return ListView.builder(
                          shrinkWrap: true,
                          physics: const ScrollPhysics(),
                          itemCount: selectedConfiguration?.length,
                          itemBuilder: (context, index) {
                            return ShiftConfigurationWidget(
                              shiftConfigurationList: selectedConfiguration?[index],
                              availabilityCategories: selectedConfiguration?[index].availabilityCategories ?? [],
                              canYouProhibitedDaysOnTap: (p0) {
                                shiftConfigurationList.value = shiftConfigurationList.value?.map((e) {
                                  if (e.userType == selectedConfiguration?[index].userType) {
                                    return e.copyWith(
                                      canYouProhibitedDays: p0,
                                      availabilityCategories: e.availabilityCategories,
                                    );
                                  }
                                  return e;
                                }).toList();
                              },
                              selectAvailabilityCategoryOnTap: () async {
                                if (categoryList.isNotEmpty) {
                                  final list = await DailogBox.showBluredBgDailog<List<HolidayCategoryModel>>(
                                    context,
                                    barrierDismissible: false,
                                    SelectAvailabilityCategoryDialog(
                                      title:
                                          'Select availability category for ${selectedConfiguration?[index].userType}',
                                      listData: categoryList,
                                      selectedlistData: selectedConfiguration?[index].availabilityCategories ?? [],
                                    ),
                                  );

                                  if (list != null && list.isNotEmpty) {
                                    shiftConfigurationList.value = shiftConfigurationList.value?.map((e) {
                                      if (e.userType == selectedConfiguration?[index].userType) {
                                        return e.copyWith(
                                          userType: selectedConfiguration?[index].userType,

                                          availabilityCategories: list, // Update availability categories
                                        );
                                      }
                                      return e;
                                    }).toList();
                                  } else {
                                    shiftConfigurationList.value = shiftConfigurationList.value?.map((e) {
                                      if (e.userType == selectedConfiguration?[index].userType) {
                                        return e.copyWith(
                                          userType: selectedConfiguration?[index].userType,
                                          maximumPerShiftController:
                                              selectedConfiguration?[index].maximumPerShiftController,
                                          selectAvailabilityCategoryController: TextEditingController(),
                                          availabilityCategories: [], // Update availability categories
                                        );
                                      }
                                      return e;
                                    }).toList();
                                  }
                                }
                              },
                            );
                          },
                        );
                      },
                    );
                  },
                ),
                Flexible(
                  child: ContainerWidget(
                    margin: const EdgeInsets.only(bottom: 25, left: 25, right: 25),
                    padding: const EdgeInsets.all(20),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        CustomeButtonGradiantWidget(
                          width: 100,
                          buttonText: context.l10n.back,
                          isUseContainerBorder: true,
                          onTap: () {
                            widget.onBackTap.call();
                          },
                        ),
                        const Gap(15),
                        ValueListenableBuilder(
                          valueListenable: isNextLoading,
                          builder: (context, loading, _) {
                            return CustomeButtonGradiantWidget(
                              width: 180,
                              isGradient: true,
                              buttonText: 'Next & Update Project ',
                              isLoading: loading,
                              onTap: () {
                                if (formKey.currentState!.validate()) {
                                  updateProject();
                                  // if (_validateShiftConfiguration()) {
                                  // }
                                }
                              },
                            );
                          },
                        ),
                        const Gap(15),
                        CustomeButtonGradiantWidget(
                          isGradient: true,
                          width: 100,
                          buttonText: 'Skip',
                          onTap: () {
                            widget.onNextTap.call();
                          },
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // bool _validateShiftConfiguration() {
  //   final minimumTotal = shiftConfigurationList.value?.fold(
  //         0,
  //         (int previousValue, element) => previousValue + int.parse(element.minimumPerShiftController.text),
  //       ) ??
  //       0;

  //   for (var i = 0; i < shiftConfigurationList.value!.length; i++) {
  //     final isMinimumGreater = int.parse(shiftConfigurationList.value![i].maximumPerShiftController.text) <
  //         int.parse(shiftConfigurationList.value![i].minimumPerShiftController.text);
  //     if (isMinimumGreater) {
  //       Utility.toast(message: 'Minimum per shift greater than maximum per shift');
  //       return false;
  //     }
  //   }

  //   if (minimumTotal > int.parse(userPerShiftController.text)) {
  //     Utility.toast(message: 'Sum of minimum per shift greater than user per shift');
  //     return false;
  //   }

  //   return true;
  // }

  Future<void> updateProject() async {
    isNextLoading.value = true;
    final failOrSuccess = await getIt<IProjectRepository>().updateProject(
      projectId: widget.projectDetailData?.id.toString() ?? '',
      userPerShift: userPerShiftController.text,
      availabilityInputDeadline: avilabilityInputDeadlineDate,
      shiftConfigurations: shiftConfigurationList.value,
    );
    failOrSuccess.fold(
      (l) {
        Utility.toast(message: l.message);
        isNextLoading.value = false;
      },
      (r) {
        Utility.toast(message: r.message);
        isNextLoading.value = false;
        widget.onNextAndUpdateTap.call();
        context.read<RefreshCubit>().modifyProject(r.data, ProjectAction.edit);
      },
    );
  }
}
