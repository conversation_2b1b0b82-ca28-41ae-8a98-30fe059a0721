import 'package:excel_app/constants/app_assets.dart';
import 'package:excel_app/constants/app_colors.dart';
import 'package:excel_app/constants/app_constants.dart';
import 'package:excel_app/l10n/l10n_extension.dart';
import 'package:excel_app/widget/app_asset_image.dart';
import 'package:excel_app/widget/app_choice_chip.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';

class AddProjectTitleView extends StatefulWidget {
  const AddProjectTitleView({
    super.key,
    this.title,
    this.subTitle,
    this.addProjectTitle,
    this.onSelected,
    this.projectId,
    this.titleOnTap,
  });
  final String? title;
  final String? subTitle;
  final String? addProjectTitle;
  final void Function(String)? onSelected;
  final String? projectId;
  final void Function()? titleOnTap;

  @override
  State<AddProjectTitleView> createState() => _AddProjectTitleViewState();
}

class _AddProjectTitleViewState extends State<AddProjectTitleView> {
  final userRequestSelection = ValueNotifier<String>(AppConstants.basicDetails);

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          widget.title ?? '',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.w600,
              ),
        ),
        const Gap(4),
        Row(
          children: [
            InkWell(
              onTap: widget.titleOnTap,
              child: Text(
                widget.subTitle ?? '',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(color: AppColors.subText),
              ),
            ),
            const Gap(2),
            const AppAssetImage(AppAssets.arrowRightIcon),
            const Gap(2),
            Text(
              widget.title ?? '',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: AppColors.primary,
                  ),
            ),
          ],
        ),
        const Gap(20),
        Wrap(
          spacing: 16,
          runSpacing: 16,
          crossAxisAlignment: WrapCrossAlignment.center,
          children: [
            if (widget.projectId != null)
              AppChoiceChip(
                title: 'Planning',
                isSelected: widget.addProjectTitle == AppConstants.planningDetails,
                onSelected: () {
                  userRequestSelection.value = AppConstants.planningDetails;
                  widget.onSelected?.call(AppConstants.planningDetails);
                },
              ),
            AppChoiceChip(
              title: context.l10n.basicDetails,
              isSelected: widget.addProjectTitle == AppConstants.basicDetails,
              onSelected: () {
                userRequestSelection.value = AppConstants.basicDetails;
                widget.onSelected?.call(AppConstants.basicDetails);
              },
            ),
            AppChoiceChip(
              title: context.l10n.shiftSettings,
              isSelected: widget.addProjectTitle == AppConstants.shiftSetting,
              onSelected: () {
                userRequestSelection.value = AppConstants.shiftSetting;
                widget.onSelected?.call(AppConstants.shiftSetting);
              },
            ),
            AppChoiceChip(
              title: context.l10n.holidays,
              isSelected: widget.addProjectTitle == AppConstants.holidays,
              onSelected: () {
                userRequestSelection.value = AppConstants.holidays;
                widget.onSelected?.call(AppConstants.holidays);
              },
            ),
            AppChoiceChip(
              title: context.l10n.userAndSettings,
              isSelected: widget.addProjectTitle == AppConstants.usersAndSettings,
              onSelected: () {
                userRequestSelection.value = AppConstants.usersAndSettings;
                widget.onSelected?.call(AppConstants.usersAndSettings);
              },
            ),
          ],
        ),
      ],
    );
  }
}
