import 'package:excel_app/constants/app_assets.dart';
import 'package:excel_app/constants/app_colors.dart';
import 'package:excel_app/projects/model/user_setting_model.dart';
import 'package:excel_app/widget/app_asset_image.dart';
import 'package:excel_app/widget/app_drop_down_widget.dart';
import 'package:excel_app/widget/app_text_form_field.dart';
import 'package:excel_app/widget/custom_network_image.dart';
import 'package:excel_app/widget/custome_button_gradiant_widget.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';

class UserDetailDailogView extends StatefulWidget {
  const UserDetailDailogView({super.key, this.userSettingModel});
  final UserSettingModel? userSettingModel;

  @override
  State<UserDetailDailogView> createState() => _UserDetailDailogViewState();
}

class _UserDetailDailogViewState extends State<UserDetailDailogView> {
  final prohibitedValue = ValueNotifier<bool>(false);
  final selectedStatus = ValueNotifier<String?>('Approved');
  final List<String> statusList = ['Approved', 'Rejected'];
  @override
  void initState() {
    super.initState();
    prohibitedValue.value = widget.userSettingModel?.canYouProhibitedDays == 1;
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            children: [
              ClipRRect(
                borderRadius: BorderRadius.circular(100),
                child: CustomNetworkImage(
                  imageUrl: widget.userSettingModel?.userModel?.imagePath ?? '',
                  height: 44,
                  width: 44,
                ),
              ),
              const Gap(6),
              Text(
                widget.userSettingModel?.userModel?.name ?? '',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600),
              ),
              if (widget.userSettingModel?.userModel?.id == widget.userSettingModel?.projectLeaderId) ...[
                const Gap(8),
                Container(
                  width: 40,
                  height: 18,
                  alignment: Alignment.center,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(5),
                    color: AppColors.lightMint,
                  ),
                  child: Text(
                    'L + U',
                    maxLines: 1,
                    style: Theme.of(context).textTheme.labelSmall?.copyWith(fontWeight: FontWeight.w500),
                  ),
                ),
              ],
            ],
          ),
          Container(
            height: 1,
            margin: const EdgeInsets.only(
              top: 14,
              bottom: 18,
            ),
            color: AppColors.strokeColor,
          ),
          ValueListenableBuilder<String?>(
            valueListenable: selectedStatus,
            builder: (context, selected, _) {
              return AppDropDown<String>(
                title: 'Status',
                maxWidth: double.infinity,
                onSelect: (valueOfCategory) {
                  selectedStatus.value = valueOfCategory;
                },
                selectedValue: selected,
                items: statusList
                    .map(
                      (e) => DropdownMenuItem<String>(
                        value: e,
                        child: Text(
                          e,
                        ),
                      ),
                    )
                    .toList(),
              );
            },
          ),
          const Gap(20),
          Row(
            children: [
              AppTextFormField(
                maxWidth: 136,
                title: 'Max Shift',
                readOnly: true,
                controller: TextEditingController(text: widget.userSettingModel?.maxShift.text ?? ''),
              ),
              const Gap(20),
              AppTextFormField(
                maxWidth: 136,
                title: 'Min Shift',
                readOnly: true,
                controller: TextEditingController(text: widget.userSettingModel?.minShift.text ?? ''),
              ),
            ],
          ),
          const Gap(20),
          Row(
            children: [
              AppTextFormField(
                maxWidth: 136,
                title: 'Max Weekend Shift',
                readOnly: true,
                controller: TextEditingController(text: widget.userSettingModel?.maxWeekEndShift.text ?? ''),
              ),
              const Gap(20),
              AppTextFormField(
                maxWidth: 136,
                title: 'Min Weekend Shift',
                readOnly: true,
                controller: TextEditingController(text: widget.userSettingModel?.minWeekEndShift.text ?? ''),
              ),
            ],
          ),
          const Gap(20),
          Text(
            'Automatic assignation',
            style: Theme.of(context).textTheme.titleSmall,
          ),
          const Gap(6),
          SizedBox(
            height: 64,
            child: ListView.separated(
              separatorBuilder: (context, index) => const Gap(20),
              itemCount: 3,
              shrinkWrap: true,
              scrollDirection: Axis.horizontal,
              itemBuilder: (context, index) {
                // Ensure the list has enough elements by adding default models if necessary
                while ((widget.userSettingModel?.assignDateAndControllerModel?.length ?? 0) <= index) {
                  widget.userSettingModel?.assignDateAndControllerModel?.add(
                    AssignDateAndControllerModel(
                      assignDateController: TextEditingController(),
                    ),
                  );
                }

                final assignDateInfo = widget.userSettingModel!.assignDateAndControllerModel![index];

                return AppTextFormField(
                  maxWidth: 136,
                  controller: assignDateInfo.assignDateController,
                  title: 'Select Date',
                  style: Theme.of(context).textTheme.bodyMedium,
                  readOnly: true,
                  contentPadding: const EdgeInsets.all(10),
                  suffixIcon: const Padding(
                    padding: EdgeInsets.all(13),
                    child: AppAssetImage(
                      AppAssets.calenderIcon,
                    ),
                  ),
                );
              },
            ),
          ),
          const Gap(16),
          Row(
            children: [
              ValueListenableBuilder<bool?>(
                valueListenable: prohibitedValue,
                builder: (context, value, _) {
                  return Container(
                    constraints: const BoxConstraints.tightFor(width: 35, height: 25),
                    child: FittedBox(
                      child: CupertinoSwitch(
                        activeTrackColor: AppColors.primary,
                        value: value ?? false,
                        onChanged: (value) {
                          // prohibitedValue.value = !value;
                        },
                      ),
                    ),
                  );
                },
              ),
              const Gap(10),
              Text(
                'Can use Prohibited days',
                style: Theme.of(context).textTheme.titleSmall,
              ),
            ],
          ),
          const Gap(26),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CustomeButtonGradiantWidget(
                onTap: () {
                  Navigator.pop(context);
                },
                buttonText: 'Cancel',
                isUseContainerBorder: true,
                height: 38,
                width: 100,
              ),
              const Gap(15),
              CustomeButtonGradiantWidget(
                onTap: () {
                  Navigator.pop(context);
                },
                buttonText: 'Ok',
                isGradient: true,
                height: 38,
                width: 100,
              ),
            ],
          ),
        ],
      ),
    );
  }
}
