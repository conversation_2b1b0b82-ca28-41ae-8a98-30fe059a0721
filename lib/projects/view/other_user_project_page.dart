import 'package:easy_debounce/easy_debounce.dart';
import 'package:excel_app/app/bloc/authentication_bloc.dart';
import 'package:excel_app/app/cubit/refresh_cubit.dart';
import 'package:excel_app/app/routes/app_route.dart';
import 'package:excel_app/constants/app_assets.dart';
import 'package:excel_app/constants/app_colors.dart';
import 'package:excel_app/constants/app_constants.dart';
import 'package:excel_app/data_table/data_table.dart';
import 'package:excel_app/data_table/data_table_title_widget.dart';
import 'package:excel_app/data_table/widget/chip_text_widget.dart';
import 'package:excel_app/injector/injector.dart';
import 'package:excel_app/l10n/l10n_extension.dart';
import 'package:excel_app/projects/model/project_model.dart';
import 'package:excel_app/projects/repository/i_project_repository.dart';
import 'package:excel_app/users/model/user_model.dart';
import 'package:excel_app/users/widget/user_alert_dialog_box.dart';
import 'package:excel_app/utility/enums/enum_file.dart';
import 'package:excel_app/utility/helpers/utility.dart';
import 'package:excel_app/utility/pagination_mixin.dart';
import 'package:excel_app/widget/action_button_widget.dart';
import 'package:excel_app/widget/app_asset_image.dart';
import 'package:excel_app/widget/app_text_form_field.dart';
import 'package:excel_app/widget/common_button.dart';
import 'package:excel_app/widget/container_widget.dart';
import 'package:excel_app/widget/custom_network_image.dart';
import 'package:excel_app/widget/dialogs.dart';
import 'package:excel_app/widget/no_data_available_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:visibility_detector/visibility_detector.dart';

class OtherUserProjectsPage extends StatefulWidget {
  const OtherUserProjectsPage({
    super.key,
  });

  @override
  State<OtherUserProjectsPage> createState() => _OtherUserProjectsPageState();
}

class _OtherUserProjectsPageState extends State<OtherUserProjectsPage> with PaginatisonMixin {
  final searchController = TextEditingController();
  final isLoading = ValueNotifier<bool>(false);
  final isLoadingMore = ValueNotifier<bool>(false);
  final projects = ValueNotifier<List<ProjectModel>>([]);
  final orderBy = ValueNotifier<String?>(null);
  final orderDirection = ValueNotifier<String?>(null);
  int page = 0;
  bool stopPagination = false;
  final isDeleteLoading = ValueNotifier<bool>(false);
  late UserModel loginUser;

  Future<void> getProjects() async {
    if (isLoading.value || isLoadingMore.value) return;
    if (page == 0) {
      isLoading.value = true;
    } else {
      isLoadingMore.value = true;
    }
    page += 1;
    final failOrSucess = await getIt<IProjectRepository>().getProject(
      page: page,
      perPage: 20,
      orderBy: orderBy.value,
      orderDirection: orderDirection.value,
      search: searchController.text.trim(),
    );

    failOrSucess.fold(
      (l) {
        isLoading.value = false;
        isLoadingMore.value = false;
      },
      (r) {
        stopPagination = r.data.length < 20;
        projects.value = [...projects.value, ...r.data];
        isLoading.value = false;
        isLoadingMore.value = false;
      },
    );
  }

  void _refresh() {
    page = 0;
    projects.value = [];
    getProjects();
  }

  @override
  void initState() {
    loginUser = (context.read<AuthenticationBloc>().state as Authenticated).user;
    initiatePagination();
    getProjects();
    super.initState();
  }

  @override
  void dispose() {
    disposePagination();
    super.dispose();
  }

  @override
  void onReachedLast() {
    if (stopPagination || isLoadingMore.value || isLoading.value) return;
    getProjects();
  }

  @override
  Widget build(BuildContext context) {
    return ContainerWidget(
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: Text(
                  'Projects',
                  style: Theme.of(context).textTheme.displayMedium?.copyWith(fontSize: 24, letterSpacing: 0.5),
                ),
              ),
              AppTextFormField(
                maxWidth: 300,
                controller: searchController,
                prefixIcon: AppAssets.searchIcon,
                hintText: 'Search',
                onChanged: (text) {
                  EasyDebounce.debounce(
                    'search',
                    const Duration(milliseconds: 800),
                    _refresh,
                  );
                },
              ),
            ],
          ),
          const Gap(16),
          Builder(
            builder: (context) {
              return BlocListener<RefreshCubit, RefreshState>(
                listener: (context, state) {
                  if (state is ModifyProject) {
                    switch (state.action) {
                      case ProjectAction.add:
                        projects.value = [state.project, ...projects.value];
                      case ProjectAction.edit:
                        projects.value = projects.value.map((e) {
                          if (e.id == state.project.id) {
                            return state.project;
                          }
                          return e;
                        }).toList();
                    }
                  }
                },
                child: Flexible(
                  child: ValueListenableBuilder(
                    valueListenable: isLoading,
                    builder: (context, loading, _) {
                      return ValueListenableBuilder<List<ProjectModel>>(
                        valueListenable: projects,
                        builder: (context, list, _) {
                          if (loading) {
                            return ContainerWidget(child: Utility.progressIndicator());
                          }
                          if (!loading && list.isEmpty) {
                            return const NoDataAvailableWidget(
                              message: 'No Project Available',
                            );
                          }
                          return ValueListenableBuilder<String?>(
                            valueListenable: orderBy,
                            builder: (context, orderByValue, _) {
                              return ValueListenableBuilder<String?>(
                                valueListenable: orderDirection,
                                builder: (context, direction, _) {
                                  return ValueListenableBuilder<bool>(
                                    valueListenable: isLoadingMore,
                                    builder: (context, loadingMore, _) {
                                      return VisibilityDetector(
                                        key: const Key('other-user-projects-page'),
                                        onVisibilityChanged: (info) {
                                          if (info.visibleFraction > 0.5) {
                                            onReachedLast();
                                          }
                                        },
                                        child: CustomDataTable(
                                          scrollController: scrollPaginationController,
                                          isPageLoading: loadingMore,
                                          columns: [
                                            DataColumn(
                                              onSort: (columnIndex, ascending) {
                                                orderBy.value = 'name';
                                                orderDirection.value = direction == 'DESC' ? 'ASC' : 'DESC';
                                                _refresh();
                                              },
                                              label: DataTableTitleWidget(
                                                title: context.l10n.name,
                                                isTitle: true,
                                                isDiscending: orderByValue == 'name' ? direction == 'DESC' : null,
                                              ),
                                            ),
                                            DataColumn(
                                              onSort: (columnIndex, ascending) {
                                                orderBy.value = 'leader_name';
                                                orderDirection.value = direction == 'DESC' ? 'ASC' : 'DESC';
                                                _refresh();
                                              },
                                              label: DataTableTitleWidget(
                                                title: context.l10n.leader,
                                                isTitle: true,
                                                isDiscending:
                                                    orderByValue == 'leader_name' ? direction == 'DESC' : null,
                                              ),
                                            ),
                                            DataColumn(
                                              label: DataTableTitleWidget(
                                                title: context.l10n.duration,
                                                isTitle: true,
                                              ),
                                            ),
                                            DataColumn(
                                              label: DataTableTitleWidget(
                                                title: context.l10n.availabilityStatus,
                                                isTitle: true,
                                              ),
                                            ),
                                            DataColumn(
                                              label: DataTableTitleWidget(
                                                title: context.l10n.availability,
                                                isTitle: true,
                                              ),
                                            ),
                                            DataColumn(
                                              label: DataTableTitleWidget(
                                                title: context.l10n.action,
                                                isTitle: true,
                                              ),
                                            ),
                                          ],
                                          rows: List.generate(
                                            list.length,
                                            (index) {
                                              final project = list[index];

                                              return DataRow(
                                                color: index.isEven
                                                    ? WidgetStateProperty.all(AppColors.white)
                                                    : WidgetStateProperty.all(AppColors.tableGray),
                                                cells: [
                                                  DataCell(
                                                    DataTableTitleWidget(
                                                      title: project.name ?? '',
                                                      bottomWidget: project.projectStatus != null
                                                          ? ChipTextWidget(
                                                              text: project.projectStatus!,
                                                              backgroundColor: AppColors.skinColor,
                                                            )
                                                          : null,
                                                    ),
                                                  ),
                                                  DataCell(
                                                    DataTableTitleWidget(
                                                      title: project.leader?.name ?? '',
                                                      titleLeading: ClipRRect(
                                                        borderRadius: BorderRadius.circular(100),
                                                        child: CustomNetworkImage(
                                                          imageUrl: project.leader?.imagePath ?? '',
                                                          height: 24,
                                                          width: 24,
                                                        ),
                                                      ),
                                                      bottomWidget: ChipTextWidget(
                                                        text: project.leaderTypeViewString,
                                                        backgroundColor: project.leaderTypeViewBackgroundColor,
                                                      ),
                                                    ),
                                                  ),
                                                  DataCell(
                                                    DataTableTitleWidget(
                                                      title: project.duration,
                                                    ),
                                                  ),
                                                  DataCell(
                                                    // project.leader?.id == loginUser.id &&
                                                    //         project.leaderType == AppConstants.onlyLeader
                                                    //     ? const DataTableTitleWidget(
                                                    //         title: '-',
                                                    //       )
                                                    //     :
                                                    project.availabilityStatusView,
                                                  ),
                                                  DataCell(
                                                    (project.availabilityInputDeadline != null &&
                                                            project.availabilityInputDeadline!.isBefore(
                                                              DateTime.now().subtract(const Duration(days: 1)),
                                                            ))
                                                        ? const DataTableTitleWidget(
                                                            title: '-',
                                                          )
                                                        : (loginUser.id != project.leader?.id ||
                                                                (loginUser.id == project.leader?.id &&
                                                                    ((project.leaderType == AppConstants.leaderUser) ||
                                                                        (project.leaderType ==
                                                                            AppConstants.onlyLeader))))
                                                            ? Column(
                                                                crossAxisAlignment: CrossAxisAlignment.start,
                                                                children: [
                                                                  if (project.isSubmittedForApproval == 0 &&
                                                                      project.isApproved == 0 &&
                                                                      (project.leaderType == AppConstants.leaderUser))
                                                                    CommonButton(
                                                                      text: '+ Add',
                                                                      onTap: () {
                                                                        context.goNamed(
                                                                          AppRoutes.addAvailability.name,
                                                                          pathParameters: <String, String>{
                                                                            'projectId': project.id.toString(),
                                                                          },
                                                                        );
                                                                      },
                                                                      maximumSize: const Size(70, 30),
                                                                      padding: EdgeInsets.zero,
                                                                      textStyle: Theme.of(context)
                                                                          .textTheme
                                                                          .bodyMedium
                                                                          ?.copyWith(
                                                                            color: AppColors.white,
                                                                          ),
                                                                    )
                                                                  else if (project.isSubmittedForApproval == 1 &&
                                                                      (project.isApproved == 0 ||
                                                                          project.isApproved == 0) &&
                                                                      (project.leaderType == AppConstants.leaderUser))
                                                                    CommonButton(
                                                                      text: 'Edit',
                                                                      onTap: () {
                                                                        context.goNamed(
                                                                          AppRoutes.addAvailability.name,
                                                                          pathParameters: <String, String>{
                                                                            'projectId': project.id.toString(),
                                                                          },
                                                                        );
                                                                      },
                                                                      maximumSize: const Size(70, 30),
                                                                      padding: EdgeInsets.zero,
                                                                      textStyle: Theme.of(context)
                                                                          .textTheme
                                                                          .bodyMedium
                                                                          ?.copyWith(
                                                                            color: AppColors.white,
                                                                          ),
                                                                    ),
                                                                  const Gap(10),
                                                                  ChipTextWidget(
                                                                    text: 'Due on ${project.deadline}',
                                                                    backgroundColor: AppColors.gray,
                                                                    textColor: AppColors.subText,
                                                                  ),
                                                                ],
                                                              )
                                                            : const DataTableTitleWidget(
                                                                title: '-',
                                                              ),
                                                  ),
                                                  DataCell(
                                                    Row(
                                                      children: [
                                                        if (project.leader?.id == loginUser.id) ...[
                                                          ActionButtonWidget(
                                                            toolTipMessage: context.l10n.edit,
                                                            icon: const AppAssetImage(
                                                              AppAssets.editIcon,
                                                            ),
                                                            onTap: () {
                                                              context.goNamed(
                                                                AppRoutes.editProject.name,
                                                                pathParameters: {
                                                                  'projectId': project.id.toString(),
                                                                },
                                                              );
                                                            },
                                                          ),
                                                          ActionButtonWidget(
                                                            toolTipMessage: 'Delete',
                                                            icon: const AppAssetImage(
                                                              AppAssets.deleteIcon,
                                                            ),
                                                            onTap: () {
                                                              DailogBox.showBluredBgDailog(
                                                                context,
                                                                ValueListenableBuilder(
                                                                  valueListenable: isDeleteLoading,
                                                                  builder: (context, loading, _) {
                                                                    return UserAlertDialogBox(
                                                                      imageIcon: AppAssets.closeCircleIcon,
                                                                      title: 'Delete Project',
                                                                      message:
                                                                          'Are you sure you want to delete this project?',
                                                                      firstButtonTitle: 'Cancel',
                                                                      secondButtonTitle: 'Delete',
                                                                      isLoading: loading,
                                                                      firstButtonOnTap: () {
                                                                        context.pop();
                                                                      },
                                                                      secondButtonOnTap: () {
                                                                        deleteProject(
                                                                          projectId: project.id.toString(),
                                                                        );
                                                                      },
                                                                    );
                                                                  },
                                                                ),
                                                              );
                                                            },
                                                          ),
                                                        ],
                                                      ],
                                                    ),
                                                  ),
                                                ],
                                              );
                                            },
                                          ),
                                          lastWidget: const SizedBox.shrink(),
                                        ),
                                      );
                                    },
                                  );
                                },
                              );
                            },
                          );
                        },
                      );
                    },
                  ),
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Future<void> deleteProject({required String projectId}) async {
    isDeleteLoading.value = true;
    final response = await getIt<IProjectRepository>().deleteProject(projectId: projectId);

    await response.fold(
      (l) {
        isDeleteLoading.value = false;
        Utility.toast(message: l.message);
      },
      (r) async {
        Utility.toast(message: r.message);
        context.pop();
        projects.value = [...projects.value]..removeWhere((e) => e.id.toString() == projectId);
        isDeleteLoading.value = false;
      },
    );
  }
}
