import 'package:excel_app/constants/app_colors.dart';
import 'package:excel_app/widget/container_widget.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';

class NoDataAvailableWidget extends StatelessWidget {
  const NoDataAvailableWidget({super.key, this.message});
  final String? message;

  @override
  Widget build(BuildContext context) {
    return ContainerWidget(
      padding: const EdgeInsets.all(24),
      margin: const EdgeInsets.all(24),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.folder_off_outlined,
            size: 64,
            color: AppColors.primary,
          ),
          const Gap(24),
          Text(
            message ?? '',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  color: AppColors.primary,
                  fontWeight: FontWeight.bold,
                ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
