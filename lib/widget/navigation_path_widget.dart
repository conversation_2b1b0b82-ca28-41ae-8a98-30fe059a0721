import 'package:excel_app/constants/app_assets.dart';
import 'package:excel_app/constants/app_colors.dart';
import 'package:excel_app/widget/app_asset_image.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';

class NavigationPathWidget extends StatelessWidget {
  const NavigationPathWidget({
    super.key,
    this.firstTitle,
    this.secondTitle,
    this.thirdTitle,
    this.secondTitleColor,
    this.mainTitle,
    this.titleOnTap,
    this.secondTitleOnTap,
  });
  final String? mainTitle;
  final String? firstTitle;
  final String? secondTitle;
  final String? thirdTitle;
  final Color? secondTitleColor;
  final void Function()? titleOnTap;
  final void Function()? secondTitleOnTap;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          mainTitle ?? '',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.w600,
              ),
        ),
        const Gap(4),
        Row(
          children: [
            InkWell(
              onTap: () {
                context.pop();
              },
              child: Text(
                firstTitle ?? '',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(color: AppColors.subText),
              ),
            ),
            const Gap(2),
            const AppAssetImage(AppAssets.arrowRightIcon),
            const Gap(2),
            InkWell(
              onTap: () {
                context.pop();
              },
              child: Text(
                secondTitle ?? '',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: secondTitleColor ?? AppColors.subText,
                    ),
              ),
            ),
            if (thirdTitle != null) ...[
              const Gap(2),
              const AppAssetImage(AppAssets.arrowRightIcon),
              const Gap(2),
              Text(
                thirdTitle ?? '',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: AppColors.primary,
                    ),
              ),
            ],
          ],
        ),
      ],
    );
  }
}
