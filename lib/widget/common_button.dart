import 'package:excel_app/utility/helpers/utility.dart';
import 'package:flutter/material.dart';

class CommonButton extends StatelessWidget {
  const CommonButton({
    required this.text,
    required this.onTap,
    super.key,
    this.isLoading = false,
    this.backgroundColor,
    this.removeShadow = false,
    this.textColor,
    this.maximumSize,
    this.textStyle,
    this.padding = const EdgeInsets.symmetric(vertical: 5),
  });
  final String text;
  final void Function()? onTap;
  final bool isLoading;
  final Color? backgroundColor;
  final bool removeShadow;
  final Color? textColor;
  final Size? maximumSize;
  final TextStyle? textStyle;
  final EdgeInsetsGeometry padding;

  @override
  Widget build(BuildContext context) {
    return ConstrainedBox(
      constraints: BoxConstraints(
        maxWidth: maximumSize?.width ?? double.infinity,
        maxHeight: maximumSize?.height ?? double.infinity,
      ),
      child: ElevatedButton(
        style: ElevatedButton.styleFrom(
          backgroundColor: backgroundColor,
          elevation: removeShadow ? 0 : null,
          shadowColor: removeShadow ? Colors.transparent : null,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
        ),
        onPressed: isLoading ? () {} : onTap,
        child: Padding(
          padding: padding,
          child: isLoading
              ? Utility.progressIndicator()
              : Text(
                  text,
                  style: textStyle ??
                      Theme.of(context).elevatedButtonTheme.style?.textStyle?.resolve({})?.copyWith(
                        color: textColor,
                        fontWeight: FontWeight.w500,
                      ),
                ),
        ),
      ),
    );
  }
}
