// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:developer';

import 'package:easy_debounce/easy_debounce.dart';
import 'package:excel_app/constants/app_assets.dart';
import 'package:excel_app/constants/app_constants.dart';
import 'package:excel_app/injector/injector.dart';
import 'package:excel_app/projects/model/project_model.dart';
import 'package:excel_app/projects/repository/i_project_repository.dart';
import 'package:excel_app/utility/helpers/utility.dart';
import 'package:excel_app/utility/pagination_mixin.dart';
import 'package:excel_app/widget/app_asset_image.dart';
import 'package:excel_app/widget/app_text_form_field.dart';
import 'package:excel_app/widget/custome_button_gradiant_widget.dart';
import 'package:excel_app/widget/popup_wrapper.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';

class MultipleSelectProjectDailog extends StatefulWidget {
  const MultipleSelectProjectDailog({
    super.key,
    this.selectedUser = const [],
    this.projectmodel,
    this.isFromUserSettings = false,
  });
  final List<ProjectModel>? selectedUser;
  final ProjectModel? projectmodel;
  final bool isFromUserSettings;

  @override
  State<MultipleSelectProjectDailog> createState() => MultipleSelectProjectDailogState();
}

class MultipleSelectProjectDailogState extends State<MultipleSelectProjectDailog> with PaginatisonMixin {
  final searchController = TextEditingController();

  bool isLoading = false;

  bool isLoadingMore = false;

  bool hasReachedMax = false;

  int page = 1;

  List<ProjectModel> projects = [];
  List<ProjectModel> allProjects = []; // Store all projects for local search
  final selectedUsers = ValueNotifier<List<ProjectModel>>([]);

  @override
  void initState() {
    super.initState();
    initiatePagination();
    selectedUsers.value = widget.selectedUser ?? [];
    load();
  }

  @override
  void dispose() {
    disposePagination();
    searchController.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return PopUpWrapper(
      constraints: const BoxConstraints(maxWidth: 400, maxHeight: 500),
      physics: const NeverScrollableScrollPhysics(),
      children: [
        Text(
          'Select Projects',
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
          style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
        ),
        const Gap(20),
        AppTextFormField(
          controller: searchController,
          prefixIcon: AppAssets.searchIcon,
          onChanged: (p0) {
            EasyDebounce.debounce(
              'search',
              const Duration(milliseconds: 300),
              _onSearchChanged,
            );
          },
          hintText: 'Search',
        ),
        const Gap(20),
        if (isLoading) ...[
          Utility.progressIndicator(),
          const Gap(20),
        ] else if (!isLoading && projects.isEmpty) ...[
          const Gap(20),
          Text(
            searchController.text.trim().isNotEmpty ? 'No projects found for' : 'No projects found',
            style: Theme.of(context).textTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),
          const Gap(40),
        ] else
          Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ValueListenableBuilder<List<ProjectModel>>(
                valueListenable: selectedUsers,
                builder: (context, selected, _) {
                  return SizedBox(
                    height: 290, // Set a fixed height
                    child: ListView.builder(
                      controller: scrollPaginationController,
                      shrinkWrap: true,
                      itemBuilder: (context, index) {
                        if (index == projects.length) {
                          return Utility.progressIndicator();
                        }
                        return InkWell(
                          onTap: () {
                            if ((widget.projectmodel?.leaderType == AppConstants.leaderUser) &&
                                (widget.projectmodel?.leaderUserId == projects[index].id)) {
                              return;
                            }
                            if (selected.any((element) => element.id == projects[index].id)) {
                              selectedUsers.value =
                                  selected.where((element) => element.id != projects[index].id).toList();
                            } else {
                              selectedUsers.value = [...selected, projects[index]];
                            }
                          },
                          child: Padding(
                            padding: const EdgeInsets.symmetric(vertical: 12),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                AppAssetImage(
                                  selected.any((element) => element.id == projects[index].id)
                                      ? AppAssets.selectedCheckboxIcon
                                      : AppAssets.unselectedCheckboxIcon,
                                ),
                                const Gap(14),
                                Text(
                                  projects[index].name ?? '',
                                  style: Theme.of(context).textTheme.bodyMedium,
                                ),
                              ],
                            ),
                          ),
                        );
                      },
                      itemCount: projects.length + (isLoadingMore ? 1 : 0),
                    ),
                  );
                },
              ),
            ],
          ),
        const Gap(18),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CustomeButtonGradiantWidget(
              buttonText: 'Cancel',
              isUseContainerBorder: true,
              width: 100,
              height: 38,
              onTap: () {
                Navigator.pop(context);
              },
            ),
            const Gap(15),
            CustomeButtonGradiantWidget(
              buttonText: 'Add',
              isGradient: true,
              width: 100,
              height: 38,
              onTap: () {
                Navigator.pop(context, selectedUsers.value);
              },
            ),
          ],
        ),
        const Gap(18),
      ],
    );
  }

  void _notify() {
    if (mounted) {
      setState(() {});
    }
  }

  /// Handle search functionality with local filtering
  void _onSearchChanged() {
    final searchQuery = searchController.text.trim().toLowerCase();

    if (searchQuery.isEmpty) {
      // Show all projects when search is empty
      projects = List.from(allProjects);
    } else {
      // Filter projects based on search query
      projects = allProjects.where((project) {
        final projectName = project.name?.toLowerCase() ?? '';
        return projectName.contains(searchQuery);
      }).toList();
    }

    _notify();
  }

  Future<void> load() async {
    isLoading = true;
    _notify();
    allProjects.clear();
    page = 1;

    // Keep loading pages until we have enough approved projects or reach the end
    while (true) {
      final failOrSuccess = await getIt<IProjectRepository>().getProject(
        page: page,
        perPage: 20,
      );

      final result = failOrSuccess.fold(
        (l) {
          isLoading = false;
          _notify();
          return null;
        },
        (r) => r,
      );

      if (result == null) break;

      // Filter and add approved projects
      final approvedProjects = result.data.where((project) => project.isApproved == 1).toList();
      allProjects.addAll(approvedProjects);

      // If we've reached the end of the list or have enough projects, stop loading
      if (result.data.length < 20 || allProjects.length >= 20) {
        hasReachedMax = result.data.length < 20;
        break;
      }

      page++;
    }

    // Display all approved projects
    projects = List.from(allProjects);
    isLoading = false;
    _notify();
  }

  Future<void> loadMore() async {
    if (isLoadingMore || hasReachedMax) return;

    isLoadingMore = true;
    _notify();

    // Keep loading pages until we get some approved projects or reach the end
    var foundApproved = false;

    while (!foundApproved && !hasReachedMax) {
      final failOrSuccess = await getIt<IProjectRepository>().getProject(
        page: page,
        perPage: 20, // Load 10 at a time to be consistent with initial load
      );

      final result = failOrSuccess.fold(
        (l) {
          isLoadingMore = false;
          _notify();
          return null;
        },
        (r) => r,
      );

      if (result == null) break;

      // Filter for approved projects
      final newApprovedProjects = result.data.where((project) => project.isApproved == 1).toList();

      if (newApprovedProjects.isNotEmpty) {
        allProjects.addAll(newApprovedProjects);
        projects = List.from(allProjects);
        foundApproved = true;
      }

      // Update pagination state
      hasReachedMax = result.data.length < 20;
      page++;

      // If we've reached the end, stop trying
      if (hasReachedMax) break;
    }

    isLoadingMore = false;
    _notify();
  }

  @override
  void onReachedLast() {
    log('updated value$isLoadingMore $isLoading $hasReachedMax');
    if (isLoadingMore || isLoading || hasReachedMax) return;

    EasyDebounce.debounce('Select__Project_Pagination', const Duration(milliseconds: 500), loadMore);
  }
}
