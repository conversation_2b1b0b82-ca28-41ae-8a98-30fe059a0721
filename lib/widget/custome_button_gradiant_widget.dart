import 'package:excel_app/constants/app_colors.dart';
import 'package:excel_app/utility/helpers/utility.dart';
import 'package:flutter/material.dart';

class CustomeButtonGradiantWidget extends StatelessWidget {
  const CustomeButtonGradiantWidget({
    this.buttonTextColor,
    super.key,
    this.child,
    this.height,
    this.isGradient = false,
    this.width,
    this.pading,
    this.margin,
    this.onTap,
    this.buttonText,
    this.isLoading = false,
    this.isUseContainerBorder = false,
    this.preIcon,
    this.textColor,
    this.fontSize,
    this.borderRadius,
    this.buttonColor = AppColors.primary,
    this.loadingSize,
    this.isDisabled = false,
  });
  final Widget? child;
  final double? height;
  final double? width;
  final bool isGradient;
  final EdgeInsetsGeometry? pading;
  final EdgeInsetsGeometry? margin;
  final String? buttonText;
  final void Function()? onTap;
  final bool isLoading;
  final bool isUseContainerBorder;
  final Widget? preIcon;
  final Color? textColor;
  final Color buttonColor;
  final Color? buttonTextColor;
  final double? fontSize;
  final BorderRadiusGeometry? borderRadius;
  final double? loadingSize;
  final bool isDisabled;

  @override
  Widget build(BuildContext context) {
    // Determine if button should be disabled
    final isButtonDisabled = isDisabled || onTap == null;

    if (!isGradient) {
      return InkWell(
        onTap: isButtonDisabled ? null : onTap,
        borderRadius: BorderRadius.circular(10),
        child: Container(
          padding: pading,
          height: height ?? 44,
          width: width,
          margin: margin,
          alignment: Alignment.center,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10),
            border: isUseContainerBorder ? Border.all(color: AppColors.strokeColor) : null,
            color: isButtonDisabled ? AppColors.disableColor : null,
          ),
          child: (buttonText != null && !isLoading)
              ? Row(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    preIcon ?? const SizedBox.shrink(),
                    Text(
                      buttonText ?? '',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontSize: fontSize ?? 15,
                            color: isButtonDisabled ? AppColors.subText : (textColor ?? AppColors.subText),
                            fontWeight: FontWeight.w500,
                          ),
                    ),
                  ],
                )
              : isLoading
                  ? Utility.progressIndicator(
                      size: loadingSize,
                    )
                  : child,
        ),
      );
    }

    return InkWell(
      onTap: isButtonDisabled ? null : onTap,
      child: Container(
        height: height ?? 44,
        width: width,
        margin: margin,
        padding: pading,
        alignment: Alignment.center,
        decoration: BoxDecoration(
          borderRadius: borderRadius ?? BorderRadius.circular(10),
          color: isButtonDisabled ? AppColors.disableColor : buttonColor,
          gradient: isButtonDisabled
              ? null
              : LinearGradient(
                  colors: [
                    buttonColor.withOpacity2(0.7),
                    buttonColor,
                    buttonColor,
                  ],
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                ),
          boxShadow: isButtonDisabled
              ? null
              : [
                  BoxShadow(
                    color: AppColors.white.withOpacity2(0.25),
                    offset: const Offset(0, 11),
                    blurRadius: 13.8,
                    spreadRadius: -1,
                  ),
                  BoxShadow(
                    color: AppColors.black.withOpacity2(0.1),
                    offset: const Offset(0, 4),
                    blurRadius: 4,
                  ),
                ],
        ),
        child: (buttonText != null && !isLoading)
            ? Text(
                buttonText ?? '',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontSize: 15,
                      color: isButtonDisabled ? AppColors.subText : (buttonTextColor ?? AppColors.white),
                    ),
              )
            : isLoading
                ? Utility.progressIndicator(
                    color: isButtonDisabled ? AppColors.subText : AppColors.white,
                    size: loadingSize,
                  )
                : child,
      ),
    );
  }
}
