// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:animate_do/animate_do.dart';
import 'package:excel_app/constants/app_colors.dart';
import 'package:flutter/material.dart';

class PopUpWrapper extends StatelessWidget {
  const PopUpWrapper({
    required this.children,
    super.key,
    this.childrenPadding = const EdgeInsets.symmetric(horizontal: 30),
    this.showCancelButton = true,
    this.constraints,
    this.crossAxisAlignment = CrossAxisAlignment.center,
    this.physics,
    this.scrollController,
  });
  final List<Widget> children;
  final EdgeInsets childrenPadding;
  final bool showCancelButton;
  final BoxConstraints? constraints;
  final CrossAxisAlignment crossAxisAlignment;
  final ScrollPhysics? physics;
  final ScrollController? scrollController;

  @override
  Widget build(BuildContext context) {
    return FadeIn(
      child: Dialog(
        insetPadding: const EdgeInsets.all(20),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
        child: ConstrainedBox(
          constraints: constraints ?? const BoxConstraints(maxWidth: 400),
          child: SingleChildScrollView(
            physics: physics,
            controller: scrollController,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                if (showCancelButton)
                  Align(
                    alignment: Alignment.centerRight,
                    child: Padding(
                      padding: const EdgeInsets.fromLTRB(16, 16, 16, 6),
                      child: InkWell(
                        customBorder: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(50),
                        ),
                        onTap: () {
                          Navigator.pop(context);
                        },
                        child: Icon(
                          Icons.cancel_rounded,
                          color: AppColors.black.withOpacity2(0.5),
                        ),
                      ),
                    ),
                  ),
                Padding(
                  padding: childrenPadding,
                  child: Column(
                    crossAxisAlignment: crossAxisAlignment,
                    children: children,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
