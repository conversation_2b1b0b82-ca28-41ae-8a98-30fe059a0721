import 'dart:async';

import 'package:excel_app/dashboard/widget/custom_duration_dialog.dart';
import 'package:excel_app/home/<USER>/user_swap_model.dart';
import 'package:excel_app/home/<USER>/swap_available_person_dailog_view.dart';
import 'package:excel_app/home/<USER>/swap_shift_calender_view.dart';
import 'package:excel_app/home/<USER>/select_project_dialog.dart';
import 'package:excel_app/projects/model/project_model.dart';
import 'package:excel_app/users/model/user_model.dart';
import 'package:excel_app/widget/blur_widget.dart';
import 'package:excel_app/widget/multiple_user_select_dailog.dart';
import 'package:excel_app/widget/select_user_dialog.dart';
import 'package:flutter/material.dart';

class DailogBox {
  static Future<T?> showBluredBgDailog<T extends Object?>(
    BuildContext context,
    Widget child, {
    double blurValue = 3.5,
    Color barrierColor = Colors.black38,
    bool barrierDismissible = true,
  }) {
    return showGeneralDialog<T>(
      context: context,
      barrierLabel: '',
      barrierColor: barrierColor,
      barrierDismissible: barrierDismissible,
      transitionBuilder: (ctx, anim1, anim2, animChild) => BlurWidget(
        opacity: anim1,
        signmaX: blurValue * anim1.value,
        signmaY: blurValue * anim2.value,
        child: animChild,
      ),
      pageBuilder: (context, anim1, anim2) => child,
    );
  }

  static Future<UserModel?> selectUserDailog(
    BuildContext context, {
    UserModel? selectedUser,
  }) {
    return showBluredBgDailog<UserModel?>(
      context,
      SelectUserDailog(selectedUser: selectedUser),
      blurValue: 1.5,
    );
  }

  static Future<UserModel?> selectUserRoleDailog(
    BuildContext context, {
    UserModel? selectedUser,
  }) {
    return showBluredBgDailog<UserModel?>(
      context,
      SelectUserDailog(selectedUser: selectedUser),
      blurValue: 1.5,
    );
  }

  static Future<List<UserModel>?> multipleSelectUserDailog(
    BuildContext context, {
    List<UserModel>? selectedUser,
    ProjectModel? projectmodel,
  }) {
    return showBluredBgDailog<List<UserModel>?>(
      context,
      MultipleSelectUserDailog(
        selectedUser: selectedUser,
        projectmodel: projectmodel,
      ),
      blurValue: 1.5,
    );
  }

  static Future<ProjectModel?> selectProjectDailog(
    BuildContext context, {
    ProjectModel? selectedProject,
  }) {
    return showBluredBgDailog<ProjectModel?>(
      context,
      SelectProjectDialog(selectedProject: selectedProject),
      blurValue: 1.5,
    );
  }

  static Future<ProjectModel?> selectSwapAvailabilityDailog(
    BuildContext context, {
    UserSwapModel? userSwapModel,
  }) {
    return showBluredBgDailog<ProjectModel?>(
      context,
      SwapShiftCalenderView(
        userSwapModel: userSwapModel,
      ),
      blurValue: 1.5,
    );
  }

  static Future<List<UserModel>?> swapAvailablePersonDailog(
    BuildContext context, {
    UserSwapModel? userSwapModel,
  }) {
    return showBluredBgDailog<List<UserModel>?>(
      context,
      SwapAvaiblePersonDailog(
        userSwapModel: userSwapModel,
      ),
      blurValue: 1.5,
    );
  }

  static Future<String?> selectDurationDailog(
    BuildContext context, {
    String? selectedDuration,
  }) {
    final completer = Completer<String?>();

    showBluredBgDailog<String?>(
      context,
      SelectDurationDialog(
        selectedDuration: selectedDuration,
        onDurationSelected: completer.complete,
      ),
      blurValue: 1.5,
    ).then((value) {
      if (!completer.isCompleted) {
        completer.complete(value);
      }
    });

    return completer.future;
  }
}
