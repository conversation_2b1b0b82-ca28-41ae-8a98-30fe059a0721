import 'package:excel_app/app/bloc/authentication_bloc.dart';
import 'package:excel_app/auth/repository/i_auth_repository.dart';
import 'package:excel_app/injector/injector.dart';
import 'package:excel_app/utility/helpers/utility.dart';
import 'package:excel_app/widget/custome_button_gradiant_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:google_sign_in/google_sign_in.dart';

class LogoutDailog extends StatefulWidget {
  const LogoutDailog({super.key});

  @override
  State<LogoutDailog> createState() => LogoutDailogState();
}

class LogoutDailogState extends State<LogoutDailog> {
  final isLoading = ValueNotifier<bool>(false);

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
      child: Si<PERSON>Box(
        width: 390,
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 30),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Are you sure you want\n to log out?',
                textAlign: TextAlign.center,
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                    ),
              ),
              const Gap(30),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CustomeButtonGradiantWidget(
                    buttonText: 'Cancel',
                    isUseContainerBorder: true,
                    width: 100,
                    height: 38,
                    onTap: () {
                      Navigator.pop(context);
                    },
                  ),
                  const Gap(15),
                  ValueListenableBuilder(
                    valueListenable: isLoading,
                    builder: (context, loading, _) {
                      return CustomeButtonGradiantWidget(
                        isLoading: loading,
                        buttonText: 'Yes, Logout',
                        isGradient: true,
                        width: 116,
                        height: 38,
                        onTap: logoutApi,
                      );
                    },
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> logoutApi() async {
    isLoading.value = true;
    final response = await getIt<IAuthRepository>().logout();
    final googleSignIn = GoogleSignIn();

    await response.fold(
      (l) {
        isLoading.value = false;
        Utility.toast(message: l.message);
      },
      (r) async {
        Navigator.pop(context);
        context.read<AuthenticationBloc>().add(const Logout());
        await googleSignIn.signOut();
        Utility.toast(message: r.message);
        isLoading.value = false;
      },
    );
  }
}
