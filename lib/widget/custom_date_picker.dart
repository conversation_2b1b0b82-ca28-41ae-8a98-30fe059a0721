import 'package:excel_app/constants/app_colors.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

class CustomDatePicker extends StatefulWidget {
  const CustomDatePicker({
    super.key,
    this.initialStartDate,
    this.initialEndDate,
    this.firstDate,
    this.lastDate,
    this.onDateRangeSelected,
    this.isRangeMode = true,
  });

  final DateTime? initialStartDate;
  final DateTime? initialEndDate;
  final DateTime? firstDate;
  final DateTime? lastDate;
  final Function(DateTime startDate, DateTime endDate)? onDateRangeSelected;
  final bool isRangeMode;

  @override
  State<CustomDatePicker> createState() => _CustomDatePickerState();

  static Future<DateTimeRange?> showRange(
    BuildContext context, {
    DateTime? initialStartDate,
    DateTime? initialEndDate,
    DateTime? firstDate,
    DateTime? lastDate,
  }) async {
    return showDialog<DateTimeRange>(
      context: context,
      builder: (context) => Dialog(
        insetPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 24),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        clipBehavior: Clip.antiAlias,
        child: CustomDatePicker(
          initialStartDate: initialStartDate ?? DateTime.now(),
          initialEndDate: initialEndDate,
          firstDate: firstDate ?? DateTime(2000),
          lastDate: lastDate ?? DateTime(2100),
          onDateRangeSelected: (startDate, endDate) {
            Navigator.of(context).pop(DateTimeRange(start: startDate, end: endDate));
          },
        ),
      ),
    );
  }

  static Future<DateTime?> show(
    BuildContext context, {
    DateTime? initialDate,
    DateTime? firstDate,
    DateTime? lastDate,
  }) async {
    return showDialog<DateTime>(
      context: context,
      builder: (context) => Dialog(
        insetPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 24),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        clipBehavior: Clip.antiAlias,
        child: CustomDatePicker(
          initialStartDate: initialDate ?? DateTime.now(),
          firstDate: firstDate ?? DateTime(2000),
          lastDate: lastDate ?? DateTime(2100),
          isRangeMode: false,
          onDateRangeSelected: (startDate, endDate) {
            Navigator.of(context).pop(startDate);
          },
        ),
      ),
    );
  }
}

class _CustomDatePickerState extends State<CustomDatePicker> {
  late DateTime? _startDate;
  late DateTime? _endDate;
  late DateTime _currentMonth;
  late final ValueNotifier<DateTimeRange?> _selectedRangeNotifier;

  @override
  void initState() {
    super.initState();
    _startDate = widget.initialStartDate ?? DateTime.now();
    _endDate = widget.initialEndDate;
    _currentMonth = DateTime(_startDate!.year, _startDate!.month);
    _selectedRangeNotifier = ValueNotifier<DateTimeRange?>(
      _startDate != null && _endDate != null ? DateTimeRange(start: _startDate!, end: _endDate!) : null,
    );
  }

  @override
  void dispose() {
    _selectedRangeNotifier.dispose();
    super.dispose();
  }

  void _selectDate(DateTime date) {
    setState(() {
      if (widget.isRangeMode) {
        if (_startDate == null || (_startDate != null && _endDate != null)) {
          // Start new selection
          _startDate = date;
          _endDate = null;
        } else if (_endDate == null) {
          // Complete the range
          if (date.isBefore(_startDate!)) {
            _endDate = _startDate;
            _startDate = date;
          } else {
            _endDate = date;
          }
        }

        _selectedRangeNotifier.value =
            _startDate != null && _endDate != null ? DateTimeRange(start: _startDate!, end: _endDate!) : null;
      } else {
        // Single date mode
        _startDate = date;
        _endDate = date;
        _selectedRangeNotifier.value = DateTimeRange(start: date, end: date);
      }
    });
  }

  void _previousMonth() {
    setState(() {
      _currentMonth = DateTime(_currentMonth.year, _currentMonth.month - 1);
    });
  }

  void _nextMonth() {
    setState(() {
      _currentMonth = DateTime(_currentMonth.year, _currentMonth.month + 1);
    });
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: 500,
      height: 350,
      child: Row(
        children: [
          // Left sidebar with selected date
          Container(
            width: 150,
            color: AppColors.primary,
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'SELECT DATE',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 16),
                ValueListenableBuilder<DateTimeRange?>(
                  valueListenable: _selectedRangeNotifier,
                  builder: (context, selectedRange, _) {
                    if (widget.isRangeMode) {
                      if (_startDate != null && _endDate != null) {
                        return Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'From: ${DateFormat('EEE, d MMM').format(_startDate!)}',
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 16,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              'To: ${DateFormat('EEE, d MMM').format(_endDate!)}',
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 16,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        );
                      } else if (_startDate != null) {
                        return Text(
                          'From: ${DateFormat('EEE, d MMM').format(_startDate!)}',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                          ),
                        );
                      } else {
                        return const Text(
                          'Select start date',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                          ),
                        );
                      }
                    } else {
                      return Text(
                        _startDate != null
                            ? '${DateFormat('EEE').format(_startDate!)}, ${_startDate!.day} ${DateFormat('MMM').format(_startDate!)}'
                            : 'Select date',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                        ),
                      );
                    }
                  },
                ),
                const Spacer(),
                IconButton(
                  onPressed: () {
                    // Edit button - not implemented in this example
                  },
                  icon: const Icon(
                    Icons.edit,
                    color: Colors.white,
                  ),
                ),
              ],
            ),
          ),
          // Right side with calendar
          Expanded(
            child: Column(
              children: [
                // Month selector
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        DateFormat('MMMM yyyy').format(_currentMonth),
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      Row(
                        children: [
                          IconButton(
                            icon: const Icon(Icons.chevron_left),
                            onPressed: _previousMonth,
                            splashRadius: 20,
                          ),
                          IconButton(
                            icon: const Icon(Icons.chevron_right),
                            onPressed: _nextMonth,
                            splashRadius: 20,
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                // Calendar grid
                Expanded(
                  child: _buildCalendarGrid(),
                ),
                // Action buttons
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      TextButton(
                        onPressed: () {
                          Navigator.of(context).pop();
                        },
                        child: const Text(
                          'CANCEL',
                          style: TextStyle(
                            color: AppColors.primary,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                      const SizedBox(width: 16),
                      TextButton(
                        onPressed: () {
                          if (widget.isRangeMode && _startDate != null && _endDate != null) {
                            widget.onDateRangeSelected?.call(_startDate!, _endDate!);
                          } else if (!widget.isRangeMode && _startDate != null) {
                            widget.onDateRangeSelected?.call(_startDate!, _startDate!);
                          }
                        },
                        child: const Text(
                          'OK',
                          style: TextStyle(
                            color: AppColors.primary,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCalendarGrid() {
    final daysInMonth = DateTime(_currentMonth.year, _currentMonth.month + 1, 0).day;
    final firstDayOfMonth = DateTime(_currentMonth.year, _currentMonth.month);
    final firstWeekday = firstDayOfMonth.weekday % 7; // 0 for Sunday, 1 for Monday, etc.

    return Column(
      children: [
        // Weekday headers
        Row(
          children: List.generate(7, (index) {
            final weekdayNames = ['S', 'M', 'T', 'W', 'T', 'F', 'S'];
            return Expanded(
              child: Center(
                child: Text(
                  weekdayNames[index],
                  style: const TextStyle(
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            );
          }),
        ),
        const SizedBox(height: 8),
        // Calendar days
        Expanded(
          child: GridView.builder(
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 7,
              childAspectRatio: 1.5,
            ),
            itemCount: 42, // 6 weeks * 7 days
            itemBuilder: (context, index) {
              // Calculate the day number
              final dayNumber = index - firstWeekday + 1;

              if (dayNumber < 1 || dayNumber > daysInMonth) {
                return const SizedBox.shrink();
              }

              final date = DateTime(_currentMonth.year, _currentMonth.month, dayNumber);

              // Determine if this date is selected, start, end, or in range
              final isStartDate = _startDate != null &&
                  _startDate!.year == date.year &&
                  _startDate!.month == date.month &&
                  _startDate!.day == date.day;

              final isEndDate = _endDate != null &&
                  _endDate!.year == date.year &&
                  _endDate!.month == date.month &&
                  _endDate!.day == date.day;

              var isInRange = false;
              if (widget.isRangeMode && _startDate != null && _endDate != null) {
                isInRange = date.isAfter(_startDate!) && date.isBefore(_endDate!);
              }

              final isSelected = isStartDate || isEndDate;

              var backgroundColor = Colors.transparent;
              if (isSelected) {
                backgroundColor = AppColors.primary;
              } else if (isInRange) {
                backgroundColor = AppColors.primary.withValues(alpha: 0.2);
              }

              return InkWell(
                onTap: () => _selectDate(date),
                child: Container(
                  margin: const EdgeInsets.all(2),
                  decoration: BoxDecoration(
                    color: backgroundColor,
                    shape: BoxShape.circle,
                  ),
                  child: Center(
                    child: Text(
                      dayNumber.toString(),
                      style: TextStyle(
                        color: isSelected ? Colors.white : Colors.black,
                        fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                      ),
                    ),
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }
}
