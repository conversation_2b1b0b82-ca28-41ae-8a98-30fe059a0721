import 'package:excel_app/constants/app_colors.dart';
import 'package:flutter/material.dart';

class ActionButtonWidget extends StatefulWidget {
  const ActionButtonWidget({
    required this.icon,
    super.key,
    this.toolTipMessage,
    this.onTap,
  });

  final String? toolTipMessage;
  final void Function()? onTap;
  final Widget icon;

  @override
  ActionButtonWidgetState createState() => ActionButtonWidgetState();
}

class ActionButtonWidgetState extends State<ActionButtonWidget> {
  bool isHovered = false;

  @override
  Widget build(BuildContext context) {
    return Tooltip(
      margin: EdgeInsets.zero,
      message: widget.toolTipMessage ?? '',
      decoration: const BoxDecoration(
        color: AppColors.primary,
        borderRadius: BorderRadius.all(Radius.circular(8)),
      ),
      child: MouseRegion(
        onEnter: (_) => setState(() => isHovered = true),
        onExit: (_) => setState(() => isHovered = false),
        child: Material(
          color: Colors.transparent,
          shape: const CircleBorder(),
          child: InkWell(
            onTap: widget.onTap,
            customBorder: const CircleBorder(),
            splashColor: Colors.transparent, // Optional: remove ripple effect
            highlightColor: Colors.transparent, // Optional: remove highlight effect
            child: Container(
              padding: const EdgeInsets.all(8), // Adjust for icon alignment
              decoration: BoxDecoration(
                color: isHovered ? AppColors.border : AppColors.transparent,
                shape: BoxShape.circle,
              ),
              child: widget.icon,
            ),
          ),
        ),
      ),
    );
  }
}
