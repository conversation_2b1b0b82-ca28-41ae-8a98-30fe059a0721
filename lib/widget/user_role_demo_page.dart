import 'package:excel_app/constants/app_colors.dart';
import 'package:excel_app/widget/dialogs.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';

class UserRoleDemoPage extends StatefulWidget {
  const UserRoleDemoPage({super.key});

  @override
  State<UserRoleDemoPage> createState() => _UserRoleDemoPageState();
}

class _UserRoleDemoPageState extends State<UserRoleDemoPage> {
  String? selectedRole;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('User Role Dialog Demo'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
      ),
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.person,
                size: 80,
                color: AppColors.primary,
              ),
              const Gap(24),
              
              Text(
                'Simple User Role Dialog Demo',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
                textAlign: TextAlign.center,
              ),
              const Gap(16),
              
              Text(
                'Click the button below to open the user role selection dialog',
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  color: Colors.grey[600],
                ),
                textAlign: TextAlign.center,
              ),
              const Gap(32),
              
              // Selected role display
              if (selectedRole != null) ...[
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: AppColors.primary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: AppColors.primary),
                  ),
                  child: Column(
                    children: [
                      const Icon(
                        Icons.check_circle,
                        color: AppColors.primary,
                        size: 32,
                      ),
                      const Gap(8),
                      Text(
                        'Selected Role:',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Colors.grey[600],
                        ),
                      ),
                      const Gap(4),
                      Text(
                        _getRoleDisplayName(selectedRole!),
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: AppColors.primary,
                        ),
                      ),
                    ],
                  ),
                ),
                const Gap(24),
              ],
              
              // Buttons
              Column(
                children: [
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton.icon(
                      onPressed: () => _showUserRoleDialog(false),
                      icon: const Icon(Icons.person),
                      label: const Text('Select User Role'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.primary,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                    ),
                  ),
                  const Gap(16),
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton.icon(
                      onPressed: () => _showUserRoleDialog(true),
                      icon: const Icon(Icons.group),
                      label: const Text('Select Role (with All option)'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.grey[600],
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                    ),
                  ),
                  const Gap(16),
                  if (selectedRole != null)
                    SizedBox(
                      width: double.infinity,
                      child: OutlinedButton.icon(
                        onPressed: () {
                          setState(() {
                            selectedRole = null;
                          });
                        },
                        icon: const Icon(Icons.clear),
                        label: const Text('Clear Selection'),
                        style: OutlinedButton.styleFrom(
                          foregroundColor: Colors.red,
                          side: const BorderSide(color: Colors.red),
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                      ),
                    ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _showUserRoleDialog(bool showAllOption) async {
    final result = await DailogBox.simpleUserRoleDialog(
      context,
      selectedRole: selectedRole,
      title: showAllOption ? 'Select User Role (All Available)' : 'Select User Role',
      showAllOption: showAllOption,
    );

    if (result != null) {
      setState(() {
        selectedRole = result;
      });
    }
  }

  String _getRoleDisplayName(String role) {
    switch (role) {
      case 'DOCTOR':
        return 'Doctor';
      case 'FIRSTYEAR':
        return 'First Year';
      case 'SECONDYEAR':
        return 'Second Year';
      case 'THIRDYEAR':
        return 'Third Year';
      case 'FORTHYEAR':
        return 'Fourth Year';
      case 'ALL':
        return 'All Roles';
      default:
        return role;
    }
  }
}
