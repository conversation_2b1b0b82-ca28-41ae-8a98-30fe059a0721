import 'package:excel_app/constants/app_colors.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:responsive_builder/responsive_builder.dart';

class DashBoardScrollItemView extends StatelessWidget {
  const DashBoardScrollItemView({required this.child, super.key});
  final Widget child;

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final horizontalScrollController = ScrollController();
    final verticalScrollController = ScrollController();

    // For small screens, show a centered message
    if (screenWidth < 800) {
      return Scaffold(
        body: Center(
          child: Container(
            padding: const EdgeInsets.all(24),
            margin: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity2(0.1),
                  blurRadius: 10,
                  spreadRadius: 1,
                ),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(
                  Icons.desktop_windows_outlined,
                  size: 64,
                  color: AppColors.primary,
                ),
                const Gap(24),
                Text(
                  'Desktop View Recommended',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                        color: AppColors.primary,
                        fontWeight: FontWeight.bold,
                      ),
                  textAlign: TextAlign.center,
                ),
                const Gap(16),
                const Text(
                  'This dashboard is optimized for desktop screens (800px+).',
                  style: TextStyle(fontSize: 16),
                  textAlign: TextAlign.center,
                ),
                const Gap(12),
                const Text(
                  'For the best experience, please use a larger screen or desktop device.',
                  style: TextStyle(fontSize: 14),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ),
      );
    }

    // For normal screens, show the appropriate view based on device type
    return getValueForScreenType(
      context: context,
      mobile: RawScrollbar(
        trackVisibility: false,
        thumbVisibility: true,
        controller: horizontalScrollController,
        thumbColor: AppColors.primary.withOpacity2(0.6),
        thickness: 10,
        crossAxisMargin: 1,
        radius: const Radius.circular(20),
        child: SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          controller: horizontalScrollController,
          child: RawScrollbar(
            thumbVisibility: true,
            controller: verticalScrollController,
            thumbColor: AppColors.primary.withOpacity2(0.6),
            thickness: 10,
            crossAxisMargin: 1,
            radius: const Radius.circular(20),
            child: SingleChildScrollView(
              controller: verticalScrollController,
              child: ConstrainedBox(
                constraints: BoxConstraints(
                  minWidth: 100,
                  maxWidth: screenWidth < 420 ? 420 : screenWidth,
                  minHeight: 770,
                  maxHeight: screenHeight < 770 ? 770 : screenHeight,
                ),
                child: child,
              ),
            ),
          ),
        ),
      ),
      tablet: RawScrollbar(
        trackVisibility: false,
        thumbVisibility: true,
        controller: horizontalScrollController,
        thumbColor: AppColors.primary.withOpacity2(0.6),
        thickness: 10,
        crossAxisMargin: 1,
        radius: const Radius.circular(20),
        child: SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          controller: horizontalScrollController,
          child: RawScrollbar(
            thumbVisibility: true,
            controller: verticalScrollController,
            thumbColor: AppColors.primary.withOpacity2(0.6),
            thickness: 10,
            crossAxisMargin: 1,
            radius: const Radius.circular(20),
            child: SingleChildScrollView(
              controller: verticalScrollController,
              child: ConstrainedBox(
                constraints: BoxConstraints(
                  minWidth: 600,
                  maxWidth: screenWidth < 600 ? 600 : screenWidth,
                  minHeight: 770,
                  maxHeight: screenHeight < 770 ? 770 : screenHeight,
                ),
                child: child,
              ),
            ),
          ),
        ),
      ),
      desktop: RawScrollbar(
        trackVisibility: false,
        thumbVisibility: true,
        controller: horizontalScrollController,
        thumbColor: AppColors.primary.withOpacity2(0.6),
        thickness: 10,
        crossAxisMargin: 1,
        radius: const Radius.circular(20),
        child: SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          controller: horizontalScrollController,
          child: RawScrollbar(
            thumbVisibility: true,
            controller: verticalScrollController,
            thumbColor: AppColors.primary.withOpacity2(0.6),
            thickness: 10,
            crossAxisMargin: 1,
            radius: const Radius.circular(20),
            child: SingleChildScrollView(
              controller: verticalScrollController,
              child: ConstrainedBox(
                constraints: BoxConstraints(
                  minWidth: 1260,
                  maxWidth: screenWidth < 1260 ? 1260 : screenWidth,
                  minHeight: 770,
                  maxHeight: screenHeight < 770 ? 770 : screenHeight,
                ),
                child: child,
              ),
            ),
          ),
        ),
      ),
    );
  }
}
