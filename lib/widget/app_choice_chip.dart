import 'package:excel_app/constants/app_colors.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';

class AppChoiceChip extends StatelessWidget {
  const AppChoiceChip({
    required this.title,
    super.key,
    this.isSelected = false,
    this.icon,
    this.labelPadding,
    this.onSelected,
  });
  final String title;
  final bool isSelected;

  final Widget? icon;
  final EdgeInsetsGeometry? labelPadding;
  final void Function()? onSelected;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onSelected,
      child: Container(
        height: 32,
        padding: labelPadding ??
            const EdgeInsets.symmetric(
              horizontal: 18,
            ),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(100),
          color: isSelected ? AppColors.primary : AppColors.gray,
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (icon != null) ...[
              icon!,
              const Gap(3),
            ],
            Text(
              title,
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                    color: isSelected ? AppColors.white : AppColors.subText,
                  ),
            ),
          ],
        ),
      ),
    );
  }
}
