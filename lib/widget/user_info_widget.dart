import 'package:excel_app/constants/app_assets.dart';
import 'package:excel_app/constants/app_colors.dart';
import 'package:excel_app/widget/app_asset_image.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';

class UserInfoWidget extends StatefulWidget {
  const UserInfoWidget({super.key, this.userName});
  final String? userName;

  @override
  State<UserInfoWidget> createState() => _UserInfoWidgetState();
}

class _UserInfoWidgetState extends State<UserInfoWidget> {
  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        Container(
          height: 38,
          width: 38,
          decoration: const BoxDecoration(shape: BoxShape.circle, color: AppColors.background),
        ),
        const Gap(10),
        Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              widget.userName ?? '',
              style: Theme.of(context).textTheme.displayMedium?.copyWith(
                    fontSize: 14,
                    color: AppColors.blue,
                  ),
            ),
            const Gap(2),
            Text(
              'SUPER  ADMIN',
              style: Theme.of(context).textTheme.displayMedium?.copyWith(
                    fontSize: 10,
                    color: AppColors.primary,
                  ),
            ),
          ],
        ),
        const Gap(19),
        const AppAssetImage(
          AppAssets.downArrowIcon,
          width: 10,
          height: 5,
        ),
      ],
    );
  }
}
