import 'package:excel_app/constants/app_assets.dart';
import 'package:excel_app/widget/app_asset_image.dart';
import 'package:excel_app/widget/app_text_form_field.dart';
import 'package:excel_app/widget/custome_button_gradiant_widget.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';

class CommonDialogBox extends StatefulWidget {
  const CommonDialogBox({
    required this.controller,
    super.key,
    this.title,
    this.firstButtonTitle,
    this.secondButtonTitle,
    this.firstButtonOnTap,
    this.secondButtonOnTap,
    this.isLoading = false,
    this.isHideButton = false,
  });
  final String? title;
  final TextEditingController controller;
  final String? firstButtonTitle;
  final String? secondButtonTitle;
  final VoidCallback? firstButtonOnTap;
  final VoidCallback? secondButtonOnTap;
  final bool isLoading;
  final bool isHideButton;

  @override
  State<CommonDialogBox> createState() => _CommonDialogBoxState();
}

class _CommonDialogBoxState extends State<CommonDialogBox> {
  final formKey = GlobalKey<FormState>();
  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20),
      ),
      child: Padding(
        padding: const EdgeInsets.fromLTRB(20, 20, 0, 30),
        child: Form(
          key: formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Stack(
                alignment: Alignment.centerRight,
                children: [
                  SizedBox(
                    width: 400,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Flexible(
                          child: Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 40),
                            child: Text(
                              widget.title ?? '',
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                                    fontSize: 18,
                                    fontWeight: FontWeight.w600,
                                  ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  IconButton(
                    onPressed: () {
                      widget.controller.clear();
                      context.pop();
                    },
                    icon: const SizedBox(
                      height: 20,
                      width: 20,
                      child: AppAssetImage(AppAssets.closeIcon),
                    ),
                  ),
                ],
              ),
              const Gap(20),
              Padding(
                padding: const EdgeInsets.only(right: 20),
                child: AppTextFormField(
                  controller: widget.controller,
                  maxLines: 5,
                  enabled: true,
                  hintText: 'Write here...',
                  validator: (p0) {
                    if (p0 == null || p0.isEmpty) {
                      return 'Please enter some text';
                    }
                    return null;
                  },
                ),
              ),
              if (widget.firstButtonOnTap != null || widget.secondButtonOnTap != null) const Gap(30),
              if (!widget.isHideButton)
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    if (widget.firstButtonOnTap != null)
                      CustomeButtonGradiantWidget(
                        buttonText: widget.firstButtonTitle,
                        isUseContainerBorder: true,
                        width: 141,
                        height: 38,
                        onTap: widget.firstButtonOnTap,
                      ),
                    if (widget.firstButtonOnTap != null) ...[
                      const Gap(15),
                      CustomeButtonGradiantWidget(
                        buttonText: widget.secondButtonTitle,
                        isGradient: true,
                        width: 100,
                        height: 38,
                        isLoading: widget.isLoading,
                        onTap: () {
                          if (formKey.currentState!.validate()) {
                            widget.secondButtonOnTap?.call();
                          }
                        },
                      ),
                    ],
                  ],
                ),
            ],
          ),
        ),
      ),
    );
  }
}
