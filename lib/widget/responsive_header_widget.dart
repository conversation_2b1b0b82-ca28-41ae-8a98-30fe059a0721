import 'package:excel_app/constants/app_assets.dart';
import 'package:excel_app/constants/app_colors.dart';
import 'package:excel_app/widget/app_asset_image.dart';
import 'package:excel_app/widget/app_text_form_field.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';

/// A reusable responsive header widget that adapts layout based on screen size
///
/// For large screens: Shows left content and right actions in a horizontal row
/// For small screens: Shows left content centered, then right actions in a scrollable row below
class ResponsiveHeaderWidget extends StatelessWidget {
  const ResponsiveHeaderWidget({
    required this.leftContent,
    required this.rightActions,
    super.key,
    this.breakpoint = 900,
    this.padding = const EdgeInsets.symmetric(vertical: 16, horizontal: 20),
    this.spacing = 16,
    this.actionSpacing = 16,
    this.smallScreenActionSpacing = 12,
    this.centerLeftContentOnSmallScreen = true,
  });

  /// The content to show on the left side (e.g., month navigation, title)
  final Widget leftContent;

  /// The list of action widgets to show on the right side (e.g., buttons)
  final List<Widget> rightActions;

  /// Screen width breakpoint to switch between layouts (default: 900px)
  final double breakpoint;

  /// Padding around the entire widget
  final EdgeInsets padding;

  /// Spacing between left content and right actions on large screens
  final double spacing;

  /// Spacing between action buttons on large screens
  final double actionSpacing;

  /// Spacing between action buttons on small screens
  final double smallScreenActionSpacing;

  /// Whether to center the left content on small screens
  final bool centerLeftContentOnSmallScreen;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: padding,
      child: LayoutBuilder(
        builder: (context, constraints) {
          final isSmallScreen = constraints.maxWidth < breakpoint;

          if (isSmallScreen) {
            return _buildVerticalLayout();
          } else {
            return _buildHorizontalLayout();
          }
        },
      ),
    );
  }

  Widget _buildVerticalLayout() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Left content (centered or left-aligned based on preference)
        if (centerLeftContentOnSmallScreen) Center(child: leftContent) else leftContent,
        Gap(spacing),
        // Right actions in scrollable row with overflow protection
        if (rightActions.isNotEmpty)
          ConstrainedBox(
            constraints: const BoxConstraints(maxHeight: 50), // Prevent vertical overflow
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: IntrinsicHeight(
                child: Row(
                  children: _buildActionWidgetsWithSpacing(smallScreenActionSpacing),
                ),
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildHorizontalLayout() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        // Left content
        leftContent,
        // Right actions
        if (rightActions.isNotEmpty)
          Row(
            children: _buildActionWidgetsWithSpacing(actionSpacing),
          ),
      ],
    );
  }

  List<Widget> _buildActionWidgetsWithSpacing(double spacing) {
    final widgets = <Widget>[];

    for (var i = 0; i < rightActions.length; i++) {
      widgets.add(rightActions[i]);

      // Add spacing between widgets (except after the last one)
      if (i < rightActions.length - 1) {
        widgets.add(Gap(spacing));
      }
    }

    return widgets;
  }
}

/// A specialized responsive header for month navigation with action buttons
class ResponsiveMonthHeaderWidget extends StatelessWidget {
  const ResponsiveMonthHeaderWidget({
    required this.monthText,
    required this.onPreviousMonth,
    required this.onNextMonth,
    required this.actionButtons,
    super.key,
    this.canGoPrevious = true,
    this.canGoNext = true,
    this.previousIcon,
    this.nextIcon,
    this.breakpoint = 900,
    this.padding = const EdgeInsets.symmetric(vertical: 16, horizontal: 20),
  });

  /// The month text to display (e.g., "September 2025")
  final String monthText;

  /// Callback when previous month button is tapped
  final VoidCallback onPreviousMonth;

  /// Callback when next month button is tapped
  final VoidCallback onNextMonth;

  /// List of action buttons to show on the right
  final List<Widget> actionButtons;

  /// Whether the previous month button should be enabled
  final bool canGoPrevious;

  /// Whether the next month button should be enabled
  final bool canGoNext;

  /// Custom icon for previous button (optional)
  final Widget? previousIcon;

  /// Custom icon for next button (optional)
  final Widget? nextIcon;

  /// Screen width breakpoint to switch between layouts
  final double breakpoint;

  /// Padding around the widget
  final EdgeInsets padding;

  @override
  Widget build(BuildContext context) {
    return ResponsiveHeaderWidget(
      breakpoint: breakpoint,
      padding: padding,
      leftContent: _buildMonthNavigation(context),
      rightActions: actionButtons,
      centerLeftContentOnSmallScreen: false, // Keep month navigation left-aligned
    );
  }

  Widget _buildMonthNavigation(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        InkWell(
          onTap: canGoPrevious ? onPreviousMonth : null,
          child: previousIcon ??
              AppAssetImage(
                AppAssets.leftArrowIcon,
                color: canGoPrevious ? AppColors.primary : AppColors.subText,
              ),
        ),
        const Gap(18),
        Text(
          monthText,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
        ),
        const Gap(18),
        InkWell(
          onTap: canGoNext ? onNextMonth : null,
          child: nextIcon ??
              AppAssetImage(
                AppAssets.rightArrowIcon,
                color: canGoNext ? AppColors.primary : AppColors.subText,
              ),
        ),
      ],
    );
  }
}

/// A specialized responsive header for user settings with search and action buttons
class ResponsiveUserSettingsHeaderWidget extends StatelessWidget {
  const ResponsiveUserSettingsHeaderWidget({
    required this.userCount,
    required this.searchController,
    required this.onSearchChanged,
    required this.actionButtons,
    super.key,
    this.breakpoint = 800,
    this.padding = const EdgeInsets.only(left: 20, right: 20, bottom: 20, top: 20),
  });

  /// Number of users to display in the title
  final int userCount;

  /// Search text controller
  final TextEditingController searchController;

  /// Callback when search text changes
  final VoidCallback onSearchChanged;

  /// List of action buttons (e.g., Import Users, Select User)
  final List<Widget> actionButtons;

  /// Screen width breakpoint to switch between layouts
  final double breakpoint;

  /// Padding around the widget
  final EdgeInsets padding;

  @override
  Widget build(BuildContext context) {
    return ResponsiveHeaderWidget(
      breakpoint: breakpoint,
      padding: padding,
      leftContent: _buildTitleAndSearch(context),
      rightActions: actionButtons,
      centerLeftContentOnSmallScreen: false,
    );
  }

  Widget _buildTitleAndSearch(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final isSmallScreen = constraints.maxWidth < breakpoint;

        if (isSmallScreen) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Title
              if (userCount > 0)
                Text(
                  '$userCount Users',
                  style: Theme.of(context).textTheme.displayMedium?.copyWith(fontSize: 20, letterSpacing: 0.5),
                ),
              const Gap(12),
              // Search field - flexible width for small screens
              ConstrainedBox(
                constraints: BoxConstraints(
                  maxWidth: constraints.maxWidth * 0.8, // Use 80% of available width
                  minWidth: 150, // Minimum width
                ),
                child: _buildSearchField(context),
              ),
            ],
          );
        }

        // Desktop layout
        return Row(
          children: [
            Expanded(
              child: userCount > 0
                  ? Text(
                      '$userCount Users',
                      style: Theme.of(context).textTheme.displayMedium?.copyWith(fontSize: 24, letterSpacing: 0.5),
                    )
                  : const Text(''),
            ),
            ConstrainedBox(
              constraints: const BoxConstraints(maxWidth: 250, minWidth: 200),
              child: _buildSearchField(context),
            ),
          ],
        );
      },
    );
  }

  Widget _buildSearchField(BuildContext context) {
    return AppTextFormField(
      controller: searchController,
      prefixIcon: AppAssets.searchIcon,
      hintText: 'Search',
      onChanged: (value) => onSearchChanged(),
    );
  }
}

/// A reusable button widget for user settings actions
class UserSettingsActionButton extends StatelessWidget {
  const UserSettingsActionButton({
    required this.onTap,
    required this.text,
    required this.icon,
    super.key,
    this.width = 152,
    this.height = 38,
    this.fontSize = 15,
    this.isSmallScreen = false,
  });

  final VoidCallback onTap;
  final String text;
  final String icon;
  final double width;
  final double height;
  final double fontSize;
  final bool isSmallScreen;

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        // Determine if we're in a small screen context
        final effectiveWidth = constraints.maxWidth < 800 ? 140.0 : width;
        final effectiveFontSize = constraints.maxWidth < 800 ? 13.0 : fontSize;
        final effectiveGap = constraints.maxWidth < 800 ? 8.0 : 10.0;

        return InkWell(
          onTap: onTap,
          child: Container(
            width: effectiveWidth,
            height: height,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10),
              color: AppColors.gray,
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              mainAxisSize: MainAxisSize.min,
              children: [
                AppAssetImage(icon),
                Gap(effectiveGap),
                Flexible(
                  child: Text(
                    text,
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                          fontSize: effectiveFontSize,
                          color: AppColors.subText,
                        ),
                    overflow: TextOverflow.ellipsis,
                    maxLines: 1,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
