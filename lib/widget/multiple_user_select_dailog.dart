// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:developer';

import 'package:easy_debounce/easy_debounce.dart';
import 'package:excel_app/constants/app_assets.dart';
import 'package:excel_app/constants/app_colors.dart';
import 'package:excel_app/constants/app_constants.dart';
import 'package:excel_app/injector/injector.dart';
import 'package:excel_app/projects/model/project_model.dart';
import 'package:excel_app/users/model/user_model.dart';
import 'package:excel_app/users/repository/i_user_repository.dart';
import 'package:excel_app/utility/helpers/utility.dart';
import 'package:excel_app/utility/pagination_mixin.dart';
import 'package:excel_app/widget/app_asset_image.dart';
import 'package:excel_app/widget/app_text_form_field.dart';
import 'package:excel_app/widget/custome_button_gradiant_widget.dart';
import 'package:excel_app/widget/popup_wrapper.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';

class SampleUserModel {
  SampleUserModel({this.userType, this.id, this.name});
  final int? id;
  final String? name;
  final String? userType;
}

class MultipleSelectUserDailog extends StatefulWidget {
  const MultipleSelectUserDailog({
    super.key,
    this.selectedUser = const [],
    this.projectmodel,
    this.isFromUserSettings = false,
  });
  final List<UserModel>? selectedUser;
  final ProjectModel? projectmodel;
  final bool isFromUserSettings;

  @override
  State<MultipleSelectUserDailog> createState() => MultipleSelectUserDailogState();
}

class MultipleSelectUserDailogState extends State<MultipleSelectUserDailog> with PaginatisonMixin {
  final searchController = TextEditingController();

  bool isLoading = false;

  bool isLoadingMore = false;

  bool hasReachedMax = false;

  int page = 1;

  List<UserModel> users = [];
  final selectedUsers = ValueNotifier<List<UserModel>>([]);

  @override
  void initState() {
    super.initState();
    initiatePagination();
    selectedUsers.value = widget.selectedUser ?? [];
    load();
  }

  @override
  void dispose() {
    disposePagination();
    searchController.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return PopUpWrapper(
      constraints: const BoxConstraints(maxWidth: 400, maxHeight: 500),
      physics: const NeverScrollableScrollPhysics(),
      children: [
        Text(
          'Select Users',
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
          style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
        ),
        const Gap(20),
        AppTextFormField(
          controller: searchController,
          prefixIcon: AppAssets.searchIcon,
          onChanged: (p0) {
            EasyDebounce.debounce(
              'search',
              const Duration(milliseconds: 800),
              load,
            );
          },
          hintText: 'Search',
        ),
        const Gap(20),
        if (isLoading) ...[
          Utility.progressIndicator(),
          const Gap(20),
        ] else if (!isLoading && users.isEmpty) ...[
          const Gap(20),
          Text('No User Found', style: Theme.of(context).textTheme.bodyMedium),
          const Gap(40),
        ] else
          Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ValueListenableBuilder<List<UserModel>>(
                valueListenable: selectedUsers,
                builder: (context, selected, _) {
                  return SizedBox(
                    height: 290, // Set a fixed height
                    child: ListView.builder(
                      controller: scrollPaginationController,
                      shrinkWrap: true,
                      itemBuilder: (context, index) {
                        if (index == users.length) {
                          return Utility.progressIndicator();
                        }
                        return InkWell(
                          onTap: () {
                            if ((widget.projectmodel?.leaderType == AppConstants.leaderUser) &&
                                (widget.projectmodel?.leaderUserId == users[index].id)) {
                              return;
                            }
                            if (selected.any((element) => element.id == users[index].id)) {
                              selectedUsers.value = selected.where((element) => element.id != users[index].id).toList();
                            } else {
                              selectedUsers.value = [...selected, users[index]];
                            }
                          },
                          child: Padding(
                            padding: const EdgeInsets.symmetric(vertical: 12),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                AppAssetImage(
                                  selected.any((element) => element.id == users[index].id)
                                      ? AppAssets.selectedCheckboxIcon
                                      : AppAssets.unselectedCheckboxIcon,
                                ),
                                const Gap(14),
                                Text(
                                  users[index].name ?? '',
                                  style: Theme.of(context).textTheme.bodyMedium,
                                ),
                                const Gap(6),
                                Container(
                                  height: 18,
                                  width: 18,
                                  alignment: Alignment.center,
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(4),
                                    color: users[index].getColor.withOpacity2(.2),
                                  ),
                                  child: Text(
                                    users[index].firstLetterForRole,
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                    style: Theme.of(context).textTheme.labelSmall?.copyWith(
                                          color: users[index].getColor,
                                          fontSize: 11,
                                          fontWeight: FontWeight.w500,
                                        ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        );
                      },
                      itemCount: users.length + (isLoadingMore ? 1 : 0),
                    ),
                  );
                },
              ),
            ],
          ),
        const Gap(18),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CustomeButtonGradiantWidget(
              buttonText: 'Cancel',
              isUseContainerBorder: true,
              width: 100,
              height: 38,
              onTap: () {
                Navigator.pop(context);
              },
            ),
            const Gap(15),
            CustomeButtonGradiantWidget(
              buttonText: 'Add',
              isGradient: true,
              width: 100,
              height: 38,
              onTap: () {
                Navigator.pop(context, selectedUsers.value);
              },
            ),
          ],
        ),
        const Gap(18),
      ],
    );
  }

  void _notify() {
    if (mounted) {
      setState(() {});
    }
  }

  Future<void> load() async {
    isLoading = true;
    _notify();
    hasReachedMax = true;
    final failOrSuccess = await getIt<IUserRepository>().getUsers(
      search: searchController.text.trim(),
      isActive: 1,
      leaderId: widget.projectmodel?.leaderType == AppConstants.onlyLeader ? widget.projectmodel?.leaderUserId : null,
      isFromUserSettings: widget.isFromUserSettings,
      projectId: widget.projectmodel?.id,
    );
    failOrSuccess.fold(
      (l) {
        isLoading = false;
        _notify();
      },
      (r) {
        isLoading = false;
        hasReachedMax = r.data.length < 20;
        users = r.data;
        page = 2;
        _notify();
      },
    );
  }

  Future<void> loadMore() async {
    isLoadingMore = true;
    _notify();

    final failOrSuccess = await getIt<IUserRepository>().getUsers(
      page: page,
      search: searchController.text.trim(),
      isActive: 1,
      leaderId: widget.projectmodel?.leaderType == AppConstants.onlyLeader ? widget.projectmodel?.leaderUserId : null,
      isFromUserSettings: true,
      projectId: widget.projectmodel?.id,
    );
    failOrSuccess.fold(
      (l) {
        isLoadingMore = false;
        _notify();
      },
      (r) {
        isLoadingMore = false;
        hasReachedMax = r.data.length < 20;
        page += 1;
        users.addAll(r.data);
        _notify();
      },
    );
  }

  @override
  void onReachedLast() {
    log('updated value$isLoadingMore $isLoading $hasReachedMax');
    if (isLoadingMore || isLoading || hasReachedMax) return;

    EasyDebounce.debounce('Select__User_Pagination', const Duration(milliseconds: 500), loadMore);
  }
}
