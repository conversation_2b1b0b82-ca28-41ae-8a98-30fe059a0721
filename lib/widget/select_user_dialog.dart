// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:easy_debounce/easy_debounce.dart';
import 'package:excel_app/constants/app_colors.dart';
import 'package:excel_app/injector/injector.dart';
import 'package:excel_app/users/model/user_model.dart';
import 'package:excel_app/users/repository/i_user_repository.dart';
import 'package:excel_app/utility/helpers/utility.dart';
import 'package:excel_app/utility/pagination_mixin.dart';
import 'package:excel_app/widget/app_text_form_field.dart';
import 'package:excel_app/widget/popup_wrapper.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';

class SelectUserDailog extends StatefulWidget {
  const SelectUserDailog({
    super.key,
    this.selectedUser,
  });
  final UserModel? selectedUser;

  @override
  State<SelectUserDailog> createState() => SelectUserDailogState();
}

class SelectUserDailogState extends State<SelectUserDailog> with PaginatisonMixin {
  final searchController = TextEditingController();

  bool isLoading = false;

  bool isLoadingMore = false;

  bool hasReachedMax = false;

  int page = 1;

  List<UserModel> users = <UserModel>[];

  @override
  void initState() {
    super.initState();
    initiatePagination();
    load();
  }

  @override
  void dispose() {
    disposePagination();
    searchController.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return PopUpWrapper(
      constraints: const BoxConstraints(maxWidth: 400, maxHeight: 500),
      physics: const NeverScrollableScrollPhysics(),
      children: [
        Text(
          'Select User',
          style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                fontWeight: FontWeight.w600,
                fontSize: 18,
              ),
        ),
        const Gap(16),
        AppTextFormField(
          hintText: 'Search Users',
          controller: searchController,
          onChanged: (value) {
            EasyDebounce.debounce('Search_Users', const Duration(milliseconds: 500), load);
          },
        ),
        const Gap(10),
        if (isLoading) ...[
          Utility.progressIndicator(),
          const Gap(20),
        ] else if (!isLoading && users.isEmpty) ...[
          const Gap(20),
          Text('No User Found', style: Theme.of(context).textTheme.bodyMedium),
          const Gap(40),
        ] else
          SizedBox(
            height: 290,
            child: ListView.builder(
              itemCount: isLoadingMore ? users.length + 1 : users.length,
              controller: scrollPaginationController,
              shrinkWrap: true,
              physics: const AlwaysScrollableScrollPhysics(),
              itemBuilder: (context, index) {
                if (index == users.length && isLoadingMore) {
                  return Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Gap(10),
                      Utility.progressIndicator(),
                      const Gap(10),
                    ],
                  );
                }
                return InkWell(
                  onTap: () {
                    Navigator.pop(context, users[index]);
                  },
                  child: Container(
                    padding: const EdgeInsets.all(10),
                    margin: const EdgeInsets.symmetric(vertical: 4),
                    decoration: BoxDecoration(
                      color: widget.selectedUser?.id == users[index].id
                          ? AppColors.primary.withAlpha(20)
                          : AppColors.transparent,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: widget.selectedUser?.id == users[index].id ? AppColors.primary : AppColors.gray,
                      ),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          users[index].name ?? '',
                          style: Theme.of(context).textTheme.bodyMedium,
                        ),
                        Text(
                          users[index].email ?? '',
                          style: Theme.of(context).textTheme.bodySmall,
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
          ),
      ],
    );
  }

  void _notify() {
    if (mounted) {
      setState(() {});
    }
  }

  Future<void> load() async {
    isLoading = true;
    _notify();
    hasReachedMax = true;
    final failOrSuccess = await getIt<IUserRepository>().getUsers(
      search: searchController.text.trim(),
      isActive: 1,
    );
    failOrSuccess.fold(
      (l) {
        isLoading = false;
        _notify();
      },
      (r) {
        isLoading = false;
        hasReachedMax = r.data.length < 20;
        users = r.data;
        page = 2;
        _notify();
      },
    );
  }

  Future<void> loadMore() async {
    isLoadingMore = true;
    _notify();
    await Future<void>.delayed(const Duration(milliseconds: 10));
    scrollPaginationController.jumpTo(scrollPaginationController.position.maxScrollExtent);

    final failOrSuccess = await getIt<IUserRepository>().getUsers(
      page: page,
      search: searchController.text.trim(),
      isActive: 1,
    );
    failOrSuccess.fold(
      (l) {
        isLoadingMore = false;
        _notify();
      },
      (r) {
        isLoadingMore = false;
        hasReachedMax = r.data.length < 20;
        page += 1;
        users.addAll(r.data);
        _notify();
      },
    );
  }

  @override
  void onReachedLast() {
    if (isLoadingMore || isLoading || hasReachedMax) return;

    EasyDebounce.debounce('Select__User_Pagination', const Duration(milliseconds: 500), loadMore);
  }
}
