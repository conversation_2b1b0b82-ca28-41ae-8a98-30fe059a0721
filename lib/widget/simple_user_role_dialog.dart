import 'package:excel_app/constants/app_constants.dart';
import 'package:excel_app/widget/custome_button_gradiant_widget.dart';
import 'package:excel_app/widget/popup_wrapper.dart';
import 'package:flutter/material.dart';

class SimpleUserRoleDialog extends StatefulWidget {
  const SimpleUserRoleDialog({super.key, this.selectedRoles, this.title});
  final List<String>? selectedRoles;
  final String? title;

  @override
  State<SimpleUserRoleDialog> createState() => _SimpleUserRoleDialogState();
}

class _SimpleUserRoleDialogState extends State<SimpleUserRoleDialog> {
  @override
  Widget build(BuildContext context) {
    return PopUpWrapper(
      constraints: const BoxConstraints(maxWidth: 500, maxHeight: 500),
      physics: const NeverScrollableScrollPhysics(),
      children: [
        const Text(
          'Select User Roles',
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
        ),
        const SizedBox(height: 20),
        SizedBox(
          width: 460,
          height: 300,
          child: GridView.builder(
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              crossAxisSpacing: 16,
              mainAxisSpacing: 16,
              childAspectRatio: 3.2,
            ),
            itemCount: AppConstants.typeList.length,
            itemBuilder: (context, index) {
              return Container(
                width: 220,
                height: 70,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(10),
                  color: Colors.grey,
                ),
                child: Center(
                  child: Text(
                    AppConstants.typeList[index],
                    style: const TextStyle(color: Colors.white),
                  ),
                ),
              );
            },
          ),
        ),
        const SizedBox(height: 20),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CustomeButtonGradiantWidget(
              buttonText: 'Cancel',
              isUseContainerBorder: true,
              width: 100,
              height: 38,
              onTap: () {
                Navigator.pop(context);
              },
            ),
            const SizedBox(width: 15),
            CustomeButtonGradiantWidget(
              buttonText: 'Add',
              isGradient: true,
              width: 100,
              height: 38,
          ],
        ),
      ],
    );
  }
}
