import 'package:excel_app/constants/app_colors.dart';
import 'package:excel_app/widget/popup_wrapper.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';

class SimpleUserRoleDialog extends StatefulWidget {
  const SimpleUserRoleDialog({
    super.key,
    this.selectedRoles,
    this.onRolesSelected,
    this.title = 'Select User Roles',
  });

  final List<String>? selectedRoles;
  final Function(List<String>)? onRolesSelected;
  final String title;

  @override
  State<SimpleUserRoleDialog> createState() => _SimpleUserRoleDialogState();
}

class _SimpleUserRoleDialogState extends State<SimpleUserRoleDialog> {
  final selectedRoles = ValueNotifier<List<String>>([]);

  // Define user roles with their display names and colors
  final List<UserRoleItem> userRoles = [
    UserRoleItem(
      role: 'DOCTOR',
      displayName: 'Doctor',
      shortName: 'D',
      color: AppColors.deepSkyBlue,
      icon: Icons.medical_services,
    ),
    UserRoleItem(
      role: 'FIRSTYEAR',
      displayName: 'First Year',
      shortName: 'F',
      color: AppColors.sunShade,
      icon: Icons.school,
    ),
    UserRoleItem(
      role: 'SECONDYEAR',
      displayName: 'Second Year',
      shortName: 'S',
      color: AppColors.davyGray,
      icon: Icons.school,
    ),
    UserRoleItem(
      role: 'THIRDYEAR',
      displayName: 'Third Year',
      shortName: 'T',
      color: AppColors.mediumGreen,
      icon: Icons.school,
    ),
    UserRoleItem(
      role: 'FORTHYEAR',
      displayName: 'Fourth Year',
      shortName: 'F',
      color: AppColors.tyrianPurple,
      icon: Icons.school,
    ),
  ];

  @override
  void initState() {
    super.initState();
    selectedRoles.value = widget.selectedRoles ?? [];
  }

  @override
  Widget build(BuildContext context) {
    return StatefulBuilder(
      builder: (context, stateUpdate) {
        return PopUpWrapper(
          constraints: const BoxConstraints(maxWidth: 400, maxHeight: 500),
          showCancelButton: false,
          children: [
            // Header
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  widget.title,
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.w600,
                        fontSize: 18,
                        color: Colors.black87,
                      ),
                ),
                IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: const Icon(Icons.close, size: 22, color: Colors.grey),
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(),
                  splashRadius: 20,
                ),
              ],
            ),
            const Gap(20),

            // Add "All" option if requested
            if (widget.showAllOption) ...[
              ValueListenableBuilder<String>(
                valueListenable: selectedRole,
                builder: (context, selected, _) {
                  final isSelected = selected == 'ALL';
                  return _buildRoleItem(
                    context: context,
                    roleItem: UserRoleItem(
                      role: 'ALL',
                      displayName: 'All Roles',
                      shortName: 'A',
                      color: AppColors.primary,
                      icon: Icons.group,
                    ),
                    isSelected: isSelected,
                    onTap: () {
                      stateUpdate(() {
                        selectedRole.value = 'ALL';
                      });
                      widget.onRoleSelected?.call('ALL');
                      Navigator.pop(context, 'ALL');
                    },
                  );
                },
              ),
              const Gap(12),
              const Divider(),
              const Gap(12),
            ],

            // User roles list
            ValueListenableBuilder<String>(
              valueListenable: selectedRole,
              builder: (context, selected, _) {
                return Column(
                  children: userRoles.map((roleItem) {
                    final isSelected = selected == roleItem.role;
                    return Padding(
                      padding: const EdgeInsets.only(bottom: 12),
                      child: _buildRoleItem(
                        context: context,
                        roleItem: roleItem,
                        isSelected: isSelected,
                        onTap: () {
                          stateUpdate(() {
                            selectedRole.value = roleItem.role;
                          });
                          widget.onRoleSelected?.call(roleItem.role);
                          Navigator.pop(context, roleItem.role);
                        },
                      ),
                    );
                  }).toList(),
                );
              },
            ),
          ],
        );
      },
    );
  }

  Widget _buildRoleItem({
    required BuildContext context,
    required UserRoleItem roleItem,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: isSelected ? roleItem.color.withValues(alpha: 0.1) : Colors.grey[50],
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected ? roleItem.color : Colors.grey[300]!,
            width: isSelected ? 2 : 1,
          ),
          boxShadow: isSelected
              ? [
                  BoxShadow(
                    color: roleItem.color.withValues(alpha: 0.2),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ]
              : null,
        ),
        child: Row(
          children: [
            // Role icon/avatar
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: roleItem.color,
                shape: BoxShape.circle,
              ),
              child: Center(
                child: roleItem.role == 'ALL'
                    ? Icon(
                        roleItem.icon,
                        color: Colors.white,
                        size: 20,
                      )
                    : Text(
                        roleItem.shortName,
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
              ),
            ),
            const Gap(16),

            // Role name
            Expanded(
              child: Text(
                roleItem.displayName,
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                      color: isSelected ? roleItem.color : Colors.black87,
                    ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class UserRoleItem {
  UserRoleItem({
    required this.role,
    required this.displayName,
    required this.shortName,
    required this.color,
    required this.icon,
  });
  final String role;
  final String displayName;
  final String shortName;
  final Color color;
  final IconData icon;
}
