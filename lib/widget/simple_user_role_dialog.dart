import 'package:excel_app/constants/app_colors.dart';
import 'package:excel_app/constants/app_constants.dart';
import 'package:excel_app/widget/popup_wrapper.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';

class SimpleUserRoleDialog extends StatefulWidget {
  const SimpleUserRoleDialog({
    super.key,
    this.selectedRoles,
    this.onRolesSelected,
    this.title = 'Select User Roles',
  });

  final List<String>? selectedRoles;
  final void Function(List<String>)? onRolesSelected;
  final String title;

  @override
  State<SimpleUserRoleDialog> createState() => _SimpleUserRoleDialogState();
}

class _SimpleUserRoleDialogState extends State<SimpleUserRoleDialog> {
  final selectedRoles = ValueNotifier<List<String>>([]);

  // Get user roles from constants and create role items
  List<UserRoleItem> get userRoles {
    final colors = [
      AppColors.deepSkyBlue,
      AppColors.sunShade,
      AppColors.davyGray,
      AppColors.mediumGreen,
      AppColors.tyrianPurple,
      AppColors.primary,
      Colors.orange,
    ];

    return AppConstants.typeList.asMap().entries.map((entry) {
      final index = entry.key;
      final role = entry.value;
      return UserRoleItem(
        role: role,
        displayName: role,
        shortName: _getShortName(role),
        color: colors[index % colors.length],
        icon: _getIconForRole(role),
      );
    }).toList();
  }

  String _getShortName(String role) {
    if (role.toLowerCase().contains('doctor')) {
      return role.contains('1') ? 'D1' : 'D2';
    } else if (role.toLowerCase().contains('first')) {
      return 'F';
    } else if (role.toLowerCase().contains('second')) {
      return 'S';
    } else if (role.toLowerCase().contains('third')) {
      return 'T';
    } else if (role.toLowerCase().contains('last')) {
      return 'L';
    } else if (role.toLowerCase().contains('student')) {
      return 'St';
    }
    return role.substring(0, 1).toUpperCase();
  }

  IconData _getIconForRole(String role) {
    if (role.toLowerCase().contains('doctor')) {
      return Icons.medical_services;
    } else if (role.toLowerCase().contains('resident')) {
      return Icons.school;
    } else if (role.toLowerCase().contains('student')) {
      return Icons.person;
    }
    return Icons.work;
  }

  @override
  void initState() {
    super.initState();
    selectedRoles.value = widget.selectedRoles ?? [];
  }

  @override
  Widget build(BuildContext context) {
    return StatefulBuilder(
      builder: (context, stateUpdate) {
        return PopUpWrapper(
          constraints: const BoxConstraints(
            minWidth: 400,
            maxWidth: 500,
            minHeight: 300,
            maxHeight: 600,
          ),
          showCancelButton: false,
          children: [
            // Header
            SizedBox(
              width: double.infinity,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Text(
                      widget.title,
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                            fontWeight: FontWeight.w600,
                            fontSize: 18,
                            color: Colors.black87,
                          ),
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.pop(context),
                    icon: const Icon(Icons.close, size: 22, color: Colors.grey),
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(minWidth: 24, minHeight: 24),
                    splashRadius: 20,
                  ),
                ],
              ),
            ),
            const Gap(16),

            // User roles list in grid layout (smaller boxes)
            ValueListenableBuilder<List<String>>(
              valueListenable: selectedRoles,
              builder: (context, selected, _) {
                return ConstrainedBox(
                  constraints: const BoxConstraints(
                    maxHeight: 400,
                    maxWidth: 460,
                  ),
                  child: GridView.builder(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: 2,
                      childAspectRatio: 3, // Make boxes smaller
                      crossAxisSpacing: 12,
                      mainAxisSpacing: 12,
                    ),
                    itemCount: userRoles.length,
                    itemBuilder: (context, index) {
                      final roleItem = userRoles[index];
                      final isSelected = selected.contains(roleItem.role);

                      return _buildRoleItem(
                        context: context,
                        roleItem: roleItem,
                        isSelected: isSelected,
                        onTap: () {
                          stateUpdate(() {
                            final currentRoles = List<String>.from(selectedRoles.value);
                            if (isSelected) {
                              currentRoles.remove(roleItem.role);
                            } else {
                              currentRoles.add(roleItem.role);
                            }
                            selectedRoles.value = currentRoles;
                          });
                        },
                      );
                    },
                  ),
                );
              },
            ),
            const Gap(20),

            // Action buttons
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: Text(
                    'Cancel',
                    style: TextStyle(color: Colors.grey[600]),
                  ),
                ),
                ElevatedButton(
                  onPressed: () {
                    // If no roles selected, automatically select all roles
                    final finalRoles = selectedRoles.value.isEmpty ? AppConstants.typeList : selectedRoles.value;
                    widget.onRolesSelected?.call(finalRoles);
                    Navigator.pop(context, finalRoles);
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary,
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: const Text('Apply'),
                ),
              ],
            ),
          ],
        );
      },
    );
  }

  Widget _buildRoleItem({
    required BuildContext context,
    required UserRoleItem roleItem,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: isSelected ? roleItem.color.withValues(alpha: 0.1) : Colors.grey[50],
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected ? roleItem.color : Colors.grey[300]!,
            width: isSelected ? 2 : 1,
          ),
          boxShadow: isSelected
              ? [
                  BoxShadow(
                    color: roleItem.color.withValues(alpha: 0.2),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ]
              : null,
        ),
        child: Row(
          children: [
            // Role icon/avatar (smaller)
            Container(
              width: 28,
              height: 28,
              decoration: BoxDecoration(
                color: roleItem.color,
                shape: BoxShape.circle,
              ),
              child: Center(
                child: Text(
                  roleItem.shortName,
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 12,
                  ),
                ),
              ),
            ),
            const Gap(8),

            // Role name (smaller text)
            Expanded(
              child: Text(
                roleItem.displayName,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                      color: isSelected ? roleItem.color : Colors.black87,
                      fontSize: 12,
                    ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ),

            // Selection indicator (smaller)
            if (isSelected)
              Icon(
                Icons.check_circle,
                color: roleItem.color,
                size: 18,
              ),
          ],
        ),
      ),
    );
  }
}

class UserRoleItem {
  UserRoleItem({
    required this.role,
    required this.displayName,
    required this.shortName,
    required this.color,
    required this.icon,
  });
  final String role;
  final String displayName;
  final String shortName;
  final Color color;
  final IconData icon;
}
