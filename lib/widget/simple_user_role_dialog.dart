import 'dart:developer';

import 'package:excel_app/constants/app_colors.dart';
import 'package:excel_app/constants/app_constants.dart';
import 'package:excel_app/widget/custome_button_gradiant_widget.dart';
import 'package:excel_app/widget/popup_wrapper.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';

class SimpleUserRoleDialog extends StatefulWidget {
  const SimpleUserRoleDialog({super.key, this.selectedRoles, this.title});
  final List<String>? selectedRoles;
  final String? title;

  @override
  State<SimpleUserRoleDialog> createState() => _SimpleUserRoleDialogState();
}

class _SimpleUserRoleDialogState extends State<SimpleUserRoleDialog> {
  final selectedRoles = ValueNotifier<List<String>>([]);

  @override
  void initState() {
    super.initState();
    selectedRoles.value = widget.selectedRoles ?? [];
  }

  @override
  void dispose() {
    selectedRoles.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return PopUpWrapper(
      constraints: const BoxConstraints(maxWidth: 500, maxHeight: 550),
      physics: const NeverScrollableScrollPhysics(),
      children: [
        const Text(
          'Select User Roles',
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
        ),
        const SizedBox(height: 20),
        SizedBox(
          width: 460,
          height: 350,
          child: ValueListenableBuilder<List<String>>(
            valueListenable: selectedRoles,
            builder: (context, selected, _) {
              return GridView.builder(
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 2,
                  crossAxisSpacing: 16,
                  mainAxisSpacing: 16,
                  childAspectRatio: 2.9,
                ),
                itemCount: AppConstants.typeList.length,
                itemBuilder: (context, index) {
                  final role = AppConstants.typeList[index];
                  final isSelected = selected.contains(role);
                  return InkWell(
                    onTap: () {
                      final currentRoles = List<String>.from(selectedRoles.value);
                      if (isSelected) {
                        currentRoles.remove(role);
                      } else {
                        currentRoles.add(role);
                      }
                      selectedRoles.value = currentRoles;
                    },
                    child: Container(
                      width: 220,
                      height: 70,
                      decoration: BoxDecoration(
                        color: selectedRoles.value.contains(role)
                            ? AppColors.primary.withAlpha(20)
                            : AppColors.transparent,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: selectedRoles.value.contains(role) ? AppColors.primary : AppColors.gray,
                        ),
                      ),
                      child: Center(
                        child: Text(
                          role,
                          style: Theme.of(context).textTheme.bodyMedium,
                        ),
                      ),
                    ),
                  );
                },
              );
            },
          ),
        ),
        const Gap(20),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CustomeButtonGradiantWidget(
              buttonText: 'Cancel',
              isUseContainerBorder: true,
              width: 100,
              height: 38,
              onTap: () {
                Navigator.pop(context);
              },
            ),
            const SizedBox(width: 15),
            CustomeButtonGradiantWidget(
              buttonText: 'Add',
              isGradient: true,
              width: 100,
              height: 38,
              onTap: () {
                final finalRoles = selectedRoles.value.isEmpty ? ['All users'] : selectedRoles.value;
                log('${finalRoles}finalRoles');
                Navigator.pop(context, finalRoles);
              },
            ),
          ],
        ),
        const Gap(20),
      ],
    );
  }
}
