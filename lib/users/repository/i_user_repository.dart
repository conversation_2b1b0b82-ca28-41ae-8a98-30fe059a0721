import 'package:excel_app/constants/app_strings.dart';
import 'package:excel_app/users/response/add_user_response.dart';
import 'package:excel_app/users/response/common_response.dart';
import 'package:excel_app/users/response/excle_download_response.dart';
import 'package:excel_app/users/response/login_response.dart';
import 'package:excel_app/users/response/user_list_response.dart';
import 'package:excel_app/utility/extentions/fpdart_extentions.dart';
import 'package:excel_app/utility/extentions/string_extentions.dart';
import 'package:excel_app/utility/model/app_file.dart';
import 'package:excel_app/utility/network/client.dart';
import 'package:http/http.dart' as http;
import 'package:http_parser/http_parser.dart';
import 'package:injectable/injectable.dart';

part 'user_repository.dart';

abstract class IUserRepository {
  IUserRepository(this.client);
  final Client client;

  ApiResult<LoginResponse> detail();

  ApiResult<UserListResponse> getUsers({
    int page = 1,
    int perPage = 20,
    String? orderBy,
    String? orderDirection,
    String? search,
    int? isActive,
    int? leaderId,
    bool isFromUserSettings = false,
    int? projectId,
  });

  ApiResult<AddUserResponse> createUsers({
    required String name,
    required String email,
    required int annualVacationDays,
    required String role,
    AppFile? image,
  });

  ApiResult<AddUserResponse> editUser({
    String? name,
    int? annualVacationDays,
    String? userId,
    AppFile? image,
    int? userActive,
    String? role,
  });

  ApiResult<LoginResponse> userDetails({
    required String userId,
  });

  ApiResult<AddUserResponse> deleteUser({
    required String userId,
  });

  ApiResult<CommonResponse> importUser({
    AppFile? excleUserFile,
  });

  ApiResult<ExcleDownloadResponse> downloadExcleFile({
    bool isProjectUsers,
  });

  ApiResult<CommonResponse> changePassword({
    required String oldPassword,
    required String newPassword,
  });
}
