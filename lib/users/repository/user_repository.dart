part of 'i_user_repository.dart';

@Injectable(as: IUserRepository)
class UserRepository extends IUserRepository {
  UserRepository(super.client);

  @override
  ApiResult<LoginResponse> detail() async {
    final response = await client.get(
      url: AppStrings.detail,
    );

    return response.parseResponse(LoginResponse.fromJson);
  }

  @override
  ApiResult<UserListResponse> getUsers({
    int page = 1,
    int perPage = 20,
    String? orderBy,
    String? search,
    String? orderDirection,
    int? isActive,
    int? leaderId,
    bool isFromUserSettings = false,
    int? projectId,
  }) async {
    final response = await client.get(
      url: AppStrings.users,
      params: {
        'page': page.toString(),
        'per_page': perPage.toString(),
        if (orderBy != null) 'orderBy': orderBy,
        if (orderDirection != null) 'orderDirection': orderDirection,
        if (search.isPureValid) 'search': search,
        if (isActive != null) 'is_active': isActive.toString(),
        if (leaderId != null) 'user_id_to_ignore': leaderId.toString(),
        if (isFromUserSettings) 'is_user_settings': '1',
        if (projectId != null) 'project_id': projectId.toString(),
      },
    );

    return response.parseResponse(UserListResponse.fromJson);
  }

  @override
  ApiResult<AddUserResponse> createUsers({
    required String name,
    required String email,
    required int annualVacationDays,
    required String role,
    AppFile? image,
  }) async {
    final response = await client.multipart(
      url: AppStrings.createUser,
      requests: {
        'name': name,
        'email': email,
        'annual_vacation_days': annualVacationDays.toString(),
        'role': role,
        // 'is_from_web': '1',
      },
      webFiles: [
        if (image != null)
          http.MultipartFile.fromBytes(
            'image',
            image.bytes,
            filename: image.name,
            contentType: image.mimeType != null ? MediaType.parse(image.mimeType!) : null,
          ),
      ],
    );

    return response.parseResponse(AddUserResponse.fromJson);
  }

  @override
  ApiResult<AddUserResponse> editUser({
    String? name,
    int? annualVacationDays,
    String? userId,
    AppFile? image,
    String? role,
    int? userActive,
  }) async {
    final response = await client.multipart(
      url: AppStrings.editUser,
      requests: {
        if (userId != null) 'user_id': userId,
        if (name != null) 'name': name,
        if (annualVacationDays != null) 'annual_vacation_days': annualVacationDays.toString(),
        if (userActive != null) 'is_active': userActive.toString(),
        if (role != null) 'role': role,
      },
      webFiles: [
        if (image != null)
          http.MultipartFile.fromBytes(
            'image',
            image.bytes,
            filename: image.name,
            contentType: image.mimeType != null ? MediaType.parse(image.mimeType!) : null,
          ),
      ],
    );

    return response.parseResponse(AddUserResponse.fromJson);
  }

  @override
  ApiResult<LoginResponse> userDetails({required String userId}) async {
    final response = await client.get(
      url: AppStrings.userDetails,
      params: {
        'user_id': userId,
      },
    );

    return response.parseResponse(LoginResponse.fromJson);
  }

  @override
  ApiResult<AddUserResponse> deleteUser({required String userId}) async {
    final response = await client.delete(
      url: AppStrings.deleteUser(userId),
    );

    return response.parseResponse(AddUserResponse.fromJson);
  }

  @override
  ApiResult<CommonResponse> importUser({
    AppFile? excleUserFile,
  }) async {
    final response = await client.multipart(
      url: AppStrings.importUser,
      requests: {
        'is_from_web': '1',
      },
      webFiles: [
        if (excleUserFile != null)
          http.MultipartFile.fromBytes(
            'file',
            excleUserFile.bytes,
            filename: excleUserFile.name,
            contentType: excleUserFile.mimeType != null ? MediaType.parse(excleUserFile.mimeType!) : null,
          ),
      ],
    );

    return response.parseResponse(CommonResponse.fromJson);
  }

  @override
  ApiResult<CommonResponse> changePassword({
    required String oldPassword,
    required String newPassword,
  }) async {
    final response = await client.post(
      url: AppStrings.changePassword,
      requests: {
        'old_password': oldPassword,
        'new_password': newPassword,
      },
    );

    return response.parseResponse(CommonResponse.fromJson);
  }

  @override
  ApiResult<ExcleDownloadResponse> downloadExcleFile({bool isProjectUsers = false}) async {
    final response = await client.post(
      url: AppStrings.downloadExcleFile,
      requests: {
        if (isProjectUsers) 'key': 'project_user_import_sample_file',
      },
    );
    return response.parseResponse(ExcleDownloadResponse.fromJson);
  }
}
