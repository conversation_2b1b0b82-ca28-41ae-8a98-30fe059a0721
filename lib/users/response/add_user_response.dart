import 'package:equatable/equatable.dart';
import 'package:excel_app/users/model/user_model.dart';

class AddUserResponse extends Equatable {
  const AddUserResponse({
    this.message,
    this.data,
    this.status,
  });

  factory AddUserResponse.fromJson(Map<String, dynamic> json) {
    return AddUserResponse(
      message: json['message'] as String?,
      data: json['data'] == null ? null : UserModel.fromJson(json['data'] as Map<String, dynamic>),
      status: json['status'] as String?,
    );
  }

  final String? message;
  final UserModel? data;
  final String? status;

  Map<String, dynamic> toJson() => {
        'message': message,
        'data': data?.toJson(),
        'status': status,
      };

  @override
  List<Object?> get props => [
        message,
        data,
        status,
      ];
}
