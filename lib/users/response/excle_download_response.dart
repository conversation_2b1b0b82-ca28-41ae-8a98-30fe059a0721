import 'package:equatable/equatable.dart';

class ExcleDownloadResponse extends Equatable {
  const ExcleDownloadResponse({
    this.path,
    this.message,
    this.status,
  });

  factory ExcleDownloadResponse.fromJson(Map<String, dynamic> json) {
    return ExcleDownloadResponse(
      path: json['path'] as String?,
      message: json['message'] as String?,
      status: json['status'] as String?,
    );
  }

  final String? path;
  final String? message;
  final String? status;

  Map<String, dynamic> toJson() => {
        'path': path,
        'message': message,
        'status': status,
      };

  @override
  List<Object?> get props => [
        path,
        message,
        status,
      ];
}
