import 'package:equatable/equatable.dart';

class CommonResponse extends Equatable {
  const CommonResponse({
    required this.message,
    required this.status,
  });

  factory CommonResponse.fromJson(Map<String, dynamic> json) {
    return CommonResponse(
      message: json['message'] as String?,
      status: json['status'] as String?,
    );
  }

  final String? message;
  final String? status;

  Map<String, dynamic> toJson() => {
        'message': message,
        'status': status,
      };

  @override
  List<Object?> get props => [
        message,
        status,
      ];
}
