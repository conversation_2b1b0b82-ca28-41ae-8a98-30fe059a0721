// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:equatable/equatable.dart';
import 'package:excel_app/users/model/user_model.dart';

class LoginResponse extends Equatable {
  const LoginResponse({
    this.message,
    this.status,
    this.token,
    this.user,
  });

  factory LoginResponse.fromJson(Map<String, dynamic> json) {
    return LoginResponse(
      message: json['message'] as String?,
      status: json['status'] as String?,
      token: json['token'] as String?,
      user: json['data'] == null ? null : UserModel.fromJson(json['data'] as Map<String, dynamic>),
    );
  }

  final String? message;
  final String? status;
  final String? token;
  final UserModel? user;

  LoginResponse copyWith({
    String? message,
    String? status,
    String? token,
    UserModel? user,
  }) {
    return LoginResponse(
      message: message ?? this.message,
      status: status ?? this.status,
      token: token ?? this.token,
      user: user ?? this.user,
    );
  }

  Map<String, dynamic> toJ<PERSON>() => {
        'message': message,
        'status': status,
        'token': token,
        'data': user,
      };

  @override
  List<Object?> get props => [message, status, token, user];

  @override
  bool get stringify => true;
}
