import 'dart:developer';

import 'package:easy_debounce/easy_debounce.dart';
import 'package:excel_app/app/cubit/refresh_cubit.dart';
import 'package:excel_app/constants/app_colors.dart';
import 'package:excel_app/constants/app_constants.dart';
import 'package:excel_app/injector/injector.dart';
import 'package:excel_app/l10n/l10n_extension.dart';
import 'package:excel_app/users/repository/i_user_repository.dart';
import 'package:excel_app/users/response/login_response.dart';
import 'package:excel_app/utility/enums/enum_file.dart';
import 'package:excel_app/utility/helpers/utility.dart';
import 'package:excel_app/utility/model/app_file.dart';
import 'package:excel_app/widget/app_drop_down_widget.dart';
import 'package:excel_app/widget/app_text_form_field.dart';
import 'package:excel_app/widget/container_widget.dart';
import 'package:excel_app/widget/custom_network_image.dart';
import 'package:excel_app/widget/custome_button_gradiant_widget.dart';
import 'package:excel_app/widget/navigation_path_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:image_picker/image_picker.dart';

class AddUserPage extends StatefulWidget {
  const AddUserPage({super.key, this.userId});
  final String? userId;
  @override
  State<AddUserPage> createState() => _AddUserPageState();
}

class _AddUserPageState extends State<AddUserPage> {
  final nameController = TextEditingController();
  final emailController = TextEditingController();
  final annualVacationController = TextEditingController();
  final passwordVisbility = ValueNotifier<bool>(true);
  final _formKey = GlobalKey<FormState>();
  final pickedImageFile = ValueNotifier<AppFile?>(null);
  final userImage = ValueNotifier<String?>(null);
  final scrollController = ScrollController();

  final selectedType = ValueNotifier<String?>(null);
  final isLoading = ValueNotifier<bool>(false);
  final isButtonLoading = ValueNotifier<bool>(false);

  LoginResponse? userData;

  @override
  void dispose() {
    nameController.dispose();
    emailController.dispose();
    passwordVisbility.dispose();
    selectedType.dispose();
    _formKey.currentState?.dispose();
    pickedImageFile.dispose();
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
    if (widget.userId != null) getUserDetails();
  }

  Future<void> getUserDetails() async {
    isLoading.value = true;

    final failOrSucess = await getIt<IUserRepository>().userDetails(
      userId: widget.userId ?? '',
    );
    failOrSucess.fold(
      (l) {
        isLoading.value = false;
      },
      (r) {
        userData = r;
        getUserData();
        isLoading.value = false;
      },
    );
  }

  Future<void> getUserData() async {
    nameController.text = userData?.user?.name ?? '';
    emailController.text = userData?.user?.email ?? '';
    if (userData?.user?.annualVacationDays != null) {
      annualVacationController.text = userData?.user?.annualVacationDays.toString() ?? '';
    }
    userImage.value = userData?.user?.imagePath ?? '';
    if (userData?.user?.userRoleViewString != null) selectedType.value = userData?.user?.userRoleViewString ?? '';
  }

  @override
  Widget build(BuildContext context) {
    return ContainerWidget(
      child: Form(
        key: _formKey,
        child: ValueListenableBuilder(
          valueListenable: isLoading,
          builder: (context, loading, _) {
            if (loading) return const Center(child: CircularProgressIndicator());
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                NavigationPathWidget(
                  mainTitle: widget.userId != null ? context.l10n.editUser : context.l10n.addUser,
                  firstTitle: context.l10n.users,
                  secondTitle: widget.userId != null ? context.l10n.editUser : context.l10n.addUser,
                  secondTitleColor: AppColors.primary,
                ),
                const Gap(30),
                InkWell(
                  onTap: () async {
                    final pickedImage = await ImagePicker().pickImage(source: ImageSource.gallery);
                    if (pickedImage != null) {
                      final fileBytes = await pickedImage.readAsBytes();
                      pickedImageFile.value = AppFile(
                        bytes: fileBytes,
                        name: pickedImage.name,
                        mimeType: pickedImage.mimeType ?? '',
                        path: pickedImage.path,
                      );
                      userImage.value = null;
                      // log('${pickedImageFile.value}pickedImageFile');
                    }
                  },
                  child: ValueListenableBuilder(
                    valueListenable: userImage,
                    builder: (context, userImage, _) {
                      if (userImage != null) {
                        return Container(
                          clipBehavior: Clip.hardEdge,
                          height: 95,
                          width: 95,
                          decoration: const BoxDecoration(shape: BoxShape.circle),
                          child: CustomNetworkImage(
                            imageUrl: userImage,
                            height: 24,
                            width: 24,
                          ),
                        );
                      }
                      return ValueListenableBuilder<AppFile?>(
                        valueListenable: pickedImageFile,
                        builder: (context, file, _) {
                          if (file?.bytes != null) {
                            return Container(
                              clipBehavior: Clip.hardEdge,
                              height: 95,
                              width: 95,
                              decoration: const BoxDecoration(shape: BoxShape.circle),
                              child: Image.memory(
                                file!.bytes,
                                fit: BoxFit.cover,
                              ),
                            );
                          }
                          return Container(
                            height: 95,
                            width: 95,
                            decoration: const BoxDecoration(
                              shape: BoxShape.circle,
                              color: AppColors.inputFieldBg,
                            ),
                            alignment: Alignment.center,
                            child: Text(
                              '+${context.l10n.add}',
                              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                    fontWeight: FontWeight.w500,
                                    color: AppColors.primary,
                                  ),
                            ),
                          );
                        },
                      );
                    },
                  ),
                ),
                const Gap(20),
                AppTextFormField(
                  controller: nameController,
                  title: '${context.l10n.name}*',
                  inputFormatters: [
                    LengthLimitingTextInputFormatter(100),
                  ],
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return context.l10n.pleaseEnterName;
                    }
                    return null;
                  },
                ),
                const Gap(20),
                AppTextFormField(
                  controller: emailController,
                  title: '${context.l10n.email}*',
                  readOnly: widget.userId != null,
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return context.l10n.pleaseEnterEmail;
                    } else if (!Utility.isValidEmail(value.trim())) {
                      return context.l10n.pleaseEnterValidEmail;
                    }
                    return null;
                  },
                ),
                const Gap(20),
                AppTextFormField(
                  controller: annualVacationController,
                  title: 'Annual Vacation*',
                  keyboardType: TextInputType.number,
                  inputFormatters: [
                    FilteringTextInputFormatter.digitsOnly,
                    LengthLimitingTextInputFormatter(2),
                  ],
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'Please enter annual vacation';
                    }
                    return null;
                  },
                ),
                const Gap(20),
                ValueListenableBuilder<String?>(
                  valueListenable: selectedType,
                  builder: (context, type, _) {
                    return AppDropDown<String>(
                      title: '${context.l10n.type}*',
                      hintText: context.l10n.select,
                      selectedValue: type,
                      onSelect: (valueOfCategory) {
                        selectedType.value = valueOfCategory;
                      },
                      items: AppConstants.typeList
                          .map(
                            (e) => DropdownMenuItem<String>(
                              value: e,
                              child: Text(
                                e,
                              ),
                            ),
                          )
                          .toList(),
                      validator: (p0) {
                        if (p0 == null || p0.trim().isEmpty) {
                          return context.l10n.pleaseSelectType;
                        }
                        return null;
                      },
                    );
                  },
                ),
                const Gap(20),
                ValueListenableBuilder<bool>(
                  valueListenable: isButtonLoading,
                  builder: (context, loading, _) {
                    return CustomeButtonGradiantWidget(
                      onTap: () {
                        if (!loading) {
                          EasyDebounce.debounce(
                            'edit-create-user',
                            const Duration(milliseconds: 500),
                            () {
                              if (widget.userId != null) {
                                editUser();
                              } else {
                                createUser();
                              }
                            },
                          );
                        }
                      },
                      height: 38,
                      width: 100,
                      isGradient: true,
                      isLoading: loading,
                      child: Text(
                        widget.userId != null ? context.l10n.edit : context.l10n.add,
                        style:
                            Theme.of(context).textTheme.headlineSmall?.copyWith(fontSize: 15, color: AppColors.white),
                      ),
                    );
                  },
                ),
              ],
            );
          },
        ),
      ),
    );
  }

  Future<void> createUser() async {
    if (_formKey.currentState!.validate()) {
      log('${pickedImageFile.value}image file name');
      isButtonLoading.value = true;
      final response = await getIt<IUserRepository>().createUsers(
        name: nameController.text,
        email: emailController.text,
        annualVacationDays: int.tryParse(annualVacationController.text) ?? 0,
        role: userRoleViewString,
        image: pickedImageFile.value,
      );
      await response.fold(
        (l) {
          isButtonLoading.value = false;
          Utility.toast(message: l.message);
        },
        (r) async {
          isButtonLoading.value = false;
          if (r.data != null) {
            context.read<RefreshCubit>().modifyUser(r.data, UserAction.add);
          }
          Utility.toast(message: r.message);
          context.pop();
        },
      );
    }
  }

  Future<void> editUser() async {
    if (_formKey.currentState!.validate()) {
      isButtonLoading.value = true;
      final response = await getIt<IUserRepository>().editUser(
        userId: widget.userId ?? '',
        name: nameController.text,
        annualVacationDays: int.tryParse(annualVacationController.text) ?? 0,
        role: userRoleViewString,
        image: pickedImageFile.value,
      );
      await response.fold(
        (l) {
          isButtonLoading.value = false;
          Utility.toast(message: l.message);
        },
        (r) async {
          isButtonLoading.value = false;
          if (r.data != null) {
            context.read<RefreshCubit>().modifyUser(r.data, UserAction.edit);
          }
          Utility.toast(message: r.message);
          context.pop();
        },
      );
    }
  }

  String get userRoleViewString {
    switch (selectedType.value) {
      case AppConstants.doctor1:
        return AppConstants.doctorOne;
      case AppConstants.doctor2:
        return AppConstants.doctorTwo;
      case AppConstants.firstYearResident:
        return AppConstants.firstYear;
      case AppConstants.secondYearResident:
        return AppConstants.secondYear;
      case AppConstants.thirdYearResident:
        return AppConstants.thirdYear;
      case AppConstants.lastYearResident:
        return AppConstants.lastYear;
      default:
        return AppConstants.student;
    }
  }
}
