import 'dart:developer';

import 'package:easy_debounce/easy_debounce.dart';
import 'package:excel_app/app/cubit/refresh_cubit.dart';
import 'package:excel_app/app/routes/app_route.dart';
import 'package:excel_app/constants/app_assets.dart';
import 'package:excel_app/constants/app_colors.dart';
import 'package:excel_app/data_table/data_table.dart';
import 'package:excel_app/data_table/data_table_title_widget.dart';
import 'package:excel_app/injector/injector.dart';
import 'package:excel_app/l10n/l10n_extension.dart';
import 'package:excel_app/users/model/user_model.dart';
import 'package:excel_app/users/repository/i_user_repository.dart';
import 'package:excel_app/users/widget/import_user.dart';
import 'package:excel_app/utility/enums/enum_file.dart';
import 'package:excel_app/utility/extentions/string_extentions.dart';
import 'package:excel_app/utility/helpers/utility.dart';
import 'package:excel_app/utility/pagination_mixin.dart';
import 'package:excel_app/widget/action_button_widget.dart';
import 'package:excel_app/widget/app_asset_image.dart';
import 'package:excel_app/widget/app_text_form_field.dart';
import 'package:excel_app/widget/container_widget.dart';
import 'package:excel_app/widget/custom_network_image.dart';
import 'package:excel_app/widget/custome_button_gradiant_widget.dart';
import 'package:excel_app/widget/dialogs.dart';
import 'package:excel_app/widget/no_data_available_widget.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';

class UsersPage extends StatefulWidget {
  const UsersPage({super.key});

  @override
  State<UsersPage> createState() => _UsersPageState();
}

class _UsersPageState extends State<UsersPage> with PaginatisonMixin {
  final searchController = TextEditingController();

  final isLoading = ValueNotifier<bool>(false);
  final isLoadingMore = ValueNotifier<bool>(false);
  final isDeleteLoading = ValueNotifier<bool>(false);

  final users = ValueNotifier<List<UserModel>>([]);
  final orderBy = ValueNotifier<String?>(null);
  final orderDirection = ValueNotifier<String?>(null);
  int page = 0;
  bool stopPagination = false;

  Future<void> getUsers() async {
    if (isLoading.value || isLoadingMore.value) return;
    if (page == 0) {
      isLoading.value = true;
    } else {
      isLoadingMore.value = true;
    }
    page += 1;

    final failOrSucess = await getIt<IUserRepository>().getUsers(
      page: page,
      orderBy: orderBy.value,
      orderDirection: orderDirection.value,
      search: searchController.text,
    );
    failOrSucess.fold(
      (l) {
        isLoading.value = false;
        isLoadingMore.value = false;
      },
      (r) {
        stopPagination = r.data.length < 20;
        users.value = [...users.value, ...r.data];
        isLoading.value = false;
        isLoadingMore.value = false;
      },
    );
  }

  void _refresh() {
    page = 0;
    users.value = [];
    getUsers();
  }

  @override
  void initState() {
    initiatePagination();
    getUsers();
    super.initState();
  }

  @override
  void dispose() {
    disposePagination();
    super.dispose();
  }

  @override
  void onReachedLast() {
    log('test onreached last');
    if (stopPagination || isLoadingMore.value || isLoading.value) return;
    EasyDebounce.debounce('Select__User_Pagination', const Duration(milliseconds: 500), getUsers);
  }

  @override
  Widget build(BuildContext context) {
    return ContainerWidget(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            children: [
              Expanded(
                child: Text(
                  'Users',
                  style: Theme.of(context).textTheme.displayMedium?.copyWith(fontSize: 24, letterSpacing: 0.5),
                ),
              ),
              AppTextFormField(
                maxWidth: 250,
                controller: searchController,
                prefixIcon: AppAssets.searchIcon,
                onChanged: (p0) {
                  EasyDebounce.debounce(
                    'search',
                    const Duration(milliseconds: 800),
                    _refresh,
                  );
                },
                hintText: 'Search',
              ),
              const Gap(16),
              InkWell(
                onTap: () {
                  DailogBox.showBluredBgDailog(context, const ImportUserToAddProjectDialog());
                },
                child: Container(
                  width: 152,
                  height: 38,
                  decoration: BoxDecoration(borderRadius: BorderRadius.circular(10), color: AppColors.gray),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const AppAssetImage(
                        AppAssets.importUserIcon,
                      ),
                      const Gap(10),
                      Text(
                        'Import Users',
                        style:
                            Theme.of(context).textTheme.headlineSmall?.copyWith(fontSize: 15, color: AppColors.subText),
                      ),
                    ],
                  ),
                ),
              ),
              const Gap(16),
              CustomeButtonGradiantWidget(
                onTap: () {
                  context.goNamed(AppRoutes.addUser.name);
                },
                height: 38,
                width: 113,
                isGradient: true,
                child: Text(
                  '+ New User',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(fontSize: 15, color: AppColors.white),
                ),
              ),
            ],
          ),
          const Gap(16),
          Builder(
            builder: (context) {
              return BlocListener<RefreshCubit, RefreshState>(
                listener: (context, state) {
                  if (state is ModifyUser) {
                    switch (state.action) {
                      case UserAction.add:
                        users.value = [state.user, ...users.value];
                      case UserAction.edit:
                        users.value = users.value.map((e) {
                          if (e.id == state.user.id) {
                            return state.user;
                          }
                          return e;
                        }).toList();
                      case UserAction.delete:
                        users.value = [...users.value]..removeWhere((e) => e.id == state.user.id);
                      case UserAction.import:
                        _refresh();
                    }
                  }
                },
                child: Flexible(
                  child: ValueListenableBuilder(
                    valueListenable: isLoading,
                    builder: (context, loading, _) {
                      if (loading) {
                        return ContainerWidget(child: Utility.progressIndicator());
                      }
                      return ValueListenableBuilder(
                        valueListenable: users,
                        builder: (context, list, _) {
                          if (!loading && list.isEmpty) {
                            return const NoDataAvailableWidget(
                              message: 'No User Available',
                            );
                          }
                          return ValueListenableBuilder(
                            valueListenable: orderBy,
                            builder: (context, orderByValue, _) {
                              return ValueListenableBuilder(
                                valueListenable: orderDirection,
                                builder: (context, direction, _) {
                                  return ValueListenableBuilder<bool>(
                                    valueListenable: isLoadingMore,
                                    builder: (context, loadingMore, _) {
                                      return CustomDataTable(
                                        scrollController: scrollPaginationController,
                                        isPageLoading: loadingMore,
                                        columns: [
                                          DataColumn(
                                            onSort: (columnIndex, ascending) {
                                              orderBy.value = 'name';
                                              orderDirection.value = direction == 'DESC' ? 'ASC' : 'DESC';
                                              _refresh();
                                            },
                                            label: DataTableTitleWidget(
                                              title: context.l10n.user,
                                              isTitle: true,
                                              isDiscending: orderByValue == 'name' ? direction == 'DESC' : null,
                                            ),
                                          ),
                                          DataColumn(
                                            onSort: (columnIndex, ascending) {
                                              orderBy.value = 'email';
                                              orderDirection.value = direction == 'DESC' ? 'ASC' : 'DESC';
                                              _refresh();
                                            },
                                            label: DataTableTitleWidget(
                                              title: context.l10n.email,
                                              isTitle: true,
                                              isDiscending: orderByValue == 'email' ? direction == 'DESC' : null,
                                            ),
                                          ),
                                          DataColumn(
                                            onSort: (columnIndex, ascending) {
                                              orderBy.value = 'role';
                                              orderDirection.value = direction == 'DESC' ? 'ASC' : 'DESC';
                                              _refresh();
                                            },
                                            label: DataTableTitleWidget(
                                              title: context.l10n.type,
                                              isTitle: true,
                                              isDiscending: orderByValue == 'role' ? direction == 'DESC' : null,
                                            ),
                                          ),
                                          DataColumn(
                                            label: DataTableTitleWidget(
                                              title: context.l10n.action,
                                              isTitle: true,
                                            ),
                                          ),
                                        ],
                                        rows: List.generate(
                                          list.length,
                                          (index) {
                                            final user = list[index];
                                            return DataRow(
                                              color: index.isEven
                                                  ? WidgetStateProperty.all(AppColors.white)
                                                  : WidgetStateProperty.all(AppColors.tableGray),
                                              cells: [
                                                DataCell(
                                                  DataTableTitleWidget(
                                                    title: user.name ?? '',
                                                    titleLeading: ClipRRect(
                                                      borderRadius: BorderRadius.circular(100),
                                                      child: CustomNetworkImage(
                                                        imageUrl: user.imagePath ?? '',
                                                        height: 24,
                                                        width: 24,
                                                      ),
                                                    ),
                                                  ),
                                                ),
                                                DataCell(
                                                  DataTableTitleWidget(
                                                    title: user.email ?? '',
                                                  ),
                                                ),
                                                DataCell(
                                                  DataTableTitleWidget(
                                                    title: user.userRoleViewString.capitalizeFirstofEach,
                                                  ),
                                                ),
                                                DataCell(
                                                  Row(
                                                    mainAxisSize: MainAxisSize.min,
                                                    children: [
                                                      ActionButtonWidget(
                                                        toolTipMessage: context.l10n.edit,
                                                        icon: const AppAssetImage(
                                                          AppAssets.editIcon,
                                                        ),
                                                        onTap: () {
                                                          context.goNamed(
                                                            AppRoutes.editUser.name,
                                                            pathParameters: {
                                                              'userId': user.id.toString(),
                                                            },
                                                          );
                                                        },
                                                      ),
                                                      // const Gap(5),
                                                      Transform.scale(
                                                        scale: 0.7,
                                                        child: CupertinoSwitch(
                                                          value: user.isActive == 1,
                                                          activeTrackColor: AppColors.primary,
                                                          onChanged: (value) {
                                                            EasyDebounce.debounce(
                                                                'Switch_User_Active', const Duration(milliseconds: 100),
                                                                () {
                                                              users.value = users.value
                                                                  .map(
                                                                    (e) => e.id == user.id
                                                                        ? e.copyWith(isActive: value ? 1 : 0)
                                                                        : e,
                                                                  )
                                                                  .toList();
                                                              editUser(
                                                                userId: user.id.toString(),
                                                                userActive: value ? 1 : 0,
                                                              );
                                                            });
                                                          },
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                ),
                                              ],
                                            );
                                          },
                                        ),
                                        lastWidget: const SizedBox.shrink(),
                                      );
                                    },
                                  );
                                },
                              );
                            },
                          );
                        },
                      );
                    },
                  ),
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Future<void> editUser({required String userId, required int userActive}) async {
    final response = await getIt<IUserRepository>().editUser(
      userId: userId,
      userActive: userActive,
    );
    await response.fold(
      (l) {
        users.value = users.value
            .map(
              (e) => e.id.toString() == userId ? e.copyWith(isActive: userActive == 1 ? 0 : 1) : e,
            )
            .toList();
      },
      (r) async {
        if (r.data != null) {
          context.read<RefreshCubit>().modifyUser(r.data, UserAction.edit);
        }
      },
    );
  }

  Future<void> deleteUser({required String userId}) async {
    isDeleteLoading.value = true;
    final response = await getIt<IUserRepository>().deleteUser(userId: userId);

    await response.fold(
      (l) {
        isDeleteLoading.value = false;
        Utility.toast(message: l.message);
      },
      (r) async {
        Utility.toast(message: r.message);
        context.pop();
        context.read<RefreshCubit>().modifyUser(r.data, UserAction.delete);
        isDeleteLoading.value = false;
      },
    );
  }
}
