// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:ui';

import 'package:equatable/equatable.dart';
import 'package:excel_app/chat/model/pivot_chat_model.dart';
import 'package:excel_app/constants/app_colors.dart';
import 'package:excel_app/constants/app_constants.dart';
import 'package:excel_app/projects/model/project_model.dart';
import 'package:excel_app/projects/model/user_project_calendar_date_model.dart';

class UserModel extends Equatable {
  const UserModel({
    this.id,
    this.imagePath,
    this.thumbnailPath,
    this.name,
    this.role,
    this.email,
    this.annualVacationDays,
    this.isActive,
    this.emailVerifiedAt,
    this.createdAt,
    this.updatedAt,
    this.pivot,
    this.isConnectedToGoogleCalendar,
    this.isRequested = false,
    this.projects = const [],
    this.userProjectCalendarDates = const [],
    this.userProjectAvailableDates = const [],
  });

  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      id: json['id'] as int?,
      imagePath: json['image_path'] as String?,
      thumbnailPath: json['thumbnail_path'] as String?,
      name: json['name'] as String?,
      role: json['role'] as String?,
      email: json['email'] as String?,
      annualVacationDays: json['annual_vacation_days'] as int?,
      isActive: json['is_active'] as int?,
      emailVerifiedAt: json['email_verified_at'] as String?,
      createdAt: json['created_at'] as String?,
      updatedAt: json['updated_at'] as String?,
      pivot: json['pivot'] == null ? null : PivotChatModel.fromJson(json['pivot'] as Map<String, dynamic>),
      isConnectedToGoogleCalendar: json['is_connected_to_google_calendar'] as bool?,
      isRequested: json['is_requested'] as bool? ?? false,
      projects: json['projects'] == null
          ? []
          : List<ProjectModel>.from(
              (json['projects'] as List<dynamic>).map(
                (e) => ProjectModel.fromJson(e as Map<String, dynamic>),
              ),
            ),
      userProjectCalendarDates: json['user_project_calendar_dates'] == null
          ? []
          : List<UserProjectCalendarDateModel>.from(
              (json['user_project_calendar_dates'] as List<dynamic>).map(
                (e) => UserProjectCalendarDateModel.fromJson(e as Map<String, dynamic>),
              ),
            ),
      userProjectAvailableDates: json['user_project_available_dates'] == null
          ? []
          : List<UserProjectCalendarDateModel>.from(
              (json['user_project_available_dates'] as List<dynamic>).map(
                (e) => UserProjectCalendarDateModel.fromJson(e as Map<String, dynamic>),
              ),
            ),
    );
  }

  final int? id;
  final String? imagePath;
  final String? thumbnailPath;
  final String? name;
  final String? role;
  final String? email;
  final int? annualVacationDays;
  final int? isActive;
  final String? emailVerifiedAt;
  final String? createdAt;
  final String? updatedAt;
  final bool? isConnectedToGoogleCalendar;
  final bool isRequested;
  final PivotChatModel? pivot;
  final List<ProjectModel> projects;
  final List<UserProjectCalendarDateModel>? userProjectCalendarDates;
  final List<UserProjectCalendarDateModel>? userProjectAvailableDates;
  UserModel copyWith({
    int? id,
    String? imagePath,
    String? thumbnailPath,
    String? name,
    String? role,
    String? email,
    int? annualVacationDays,
    int? isActive,
    String? emailVerifiedAt,
    String? createdAt,
    String? updatedAt,
    PivotChatModel? pivot,
    bool? isConnectedToGoogleCalendar,
    bool? isRequested,
    List<ProjectModel>? projects,
    List<UserProjectCalendarDateModel>? userProjectCalendarDates,
    List<UserProjectCalendarDateModel>? userProjectAvailableDates,
  }) {
    return UserModel(
      id: id ?? this.id,
      imagePath: imagePath ?? this.imagePath,
      thumbnailPath: thumbnailPath ?? this.thumbnailPath,
      name: name ?? this.name,
      role: role ?? this.role,
      email: email ?? this.email,
      annualVacationDays: annualVacationDays ?? this.annualVacationDays,
      isActive: isActive ?? this.isActive,
      emailVerifiedAt: emailVerifiedAt ?? this.emailVerifiedAt,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      pivot: pivot ?? this.pivot,
      isConnectedToGoogleCalendar: isConnectedToGoogleCalendar ?? this.isConnectedToGoogleCalendar,
      isRequested: isRequested ?? this.isRequested,
      projects: projects ?? this.projects,
      userProjectCalendarDates: userProjectCalendarDates ?? this.userProjectCalendarDates,
      userProjectAvailableDates: userProjectAvailableDates ?? this.userProjectAvailableDates,
    );
  }

  Map<String, dynamic> toJson() => {
        'id': id,
        'image_path': imagePath,
        'thumbnail_path': thumbnailPath,
        'name': name,
        'role': role,
        'email': email,
        'annual_vacation_days': annualVacationDays,
        'is_active': isActive,
        'email_verified_at': emailVerifiedAt,
        'created_at': createdAt,
        'updated_at': updatedAt,
        'is_connected_to_google_calendar': isConnectedToGoogleCalendar,
        'is_requested': isRequested,
        'pivot': pivot?.toJson(),
        'projects': projects.map((x) => x.toJson()).toList(),
        'user_project_calendar_dates': userProjectCalendarDates?.map((x) => x.toJson()).toList(),
        'user_project_available_dates': userProjectAvailableDates?.map((x) => x.toJson()).toList(),
      };

  @override
  List<Object?> get props => [
        id,
        imagePath,
        thumbnailPath,
        name,
        role,
        email,
        annualVacationDays,
        isActive,
        emailVerifiedAt,
        createdAt,
        updatedAt,
        isConnectedToGoogleCalendar,
        isRequested,
        pivot,
        projects,
        userProjectCalendarDates,
        userProjectAvailableDates,
      ];

  String get userRoleViewString {
    switch (role) {
      case AppConstants.doctorOne:
        return AppConstants.doctor1;
      case AppConstants.doctorTwo:
        return AppConstants.doctor2;
      case AppConstants.firstYear:
        return AppConstants.firstYearResident;
      case AppConstants.secondYear:
        return AppConstants.secondYearResident;
      case AppConstants.thirdYear:
        return AppConstants.thirdYearResident;
      case AppConstants.lastYear:
        return AppConstants.lastYearResident;
      case AppConstants.student:
        return AppConstants.students;
      default:
        return 'Admin';
    }
  }

  String get dashboardUserRoleViewString {
    switch (role) {
      case AppConstants.doctorOne:
        return AppConstants.doctor1;
      case AppConstants.doctorTwo:
        return AppConstants.doctor2;
      case AppConstants.firstYear:
        return 'First yr';
      case AppConstants.secondYear:
        return 'Second yr';
      case AppConstants.thirdYear:
        return 'Third yr';
      case AppConstants.lastYear:
        return 'Last yr';
      case AppConstants.student:
        return 'Student';
      default:
        return 'Admin';
    }
  }

  String get firstLetterForRole {
    switch (role) {
      case AppConstants.doctorOne:
        return 'D1';
      case AppConstants.doctorTwo:
        return 'D2';
      case AppConstants.firstYear:
        return 'F';
      case AppConstants.secondYear:
        return 'S';
      case AppConstants.thirdYear:
        return 'T';
      case AppConstants.lastYear:
        return 'L';
      case AppConstants.student:
        return 'St';
      default:
        return 'Admin';
    }
  }

  Color get getColor {
    switch (role) {
      case AppConstants.doctorOne:
        return AppColors.deepSkyBlue;
      case AppConstants.doctorTwo:
        return AppColors.deepSkyBlue;
      case AppConstants.firstYear:
        return AppColors.sunShade;
      case AppConstants.secondYear:
        return AppColors.davyGray;
      case AppConstants.thirdYear:
        return AppColors.mediumGreen;
      case AppConstants.lastYear:
        return AppColors.tyrianPurple;
      case AppConstants.student:
        return AppColors.davyGray;
      default:
        return AppColors.deepSkyBlue;
    }
  }

  @override
  bool get stringify => true;
}
