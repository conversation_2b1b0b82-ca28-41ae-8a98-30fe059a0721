import 'dart:convert';
import 'dart:developer';
// ignore: avoid_web_libraries_in_flutter
import 'dart:html' as html;
import 'dart:typed_data';

import 'package:excel_app/constants/app_assets.dart';
import 'package:excel_app/constants/app_colors.dart';
import 'package:excel_app/constants/app_strings.dart';
import 'package:excel_app/injector/injector.dart';
import 'package:excel_app/users/repository/i_user_repository.dart';
import 'package:excel_app/users/widget/failed_user_dailog_widget.dart';
import 'package:excel_app/users/widget/successfully_user_add_dailog_widget.dart';
import 'package:excel_app/users/widget/user_data_missmatch_dailog.dart';
import 'package:excel_app/utility/helpers/utility.dart';
import 'package:excel_app/utility/model/app_file.dart';
import 'package:excel_app/widget/app_asset_image.dart';
import 'package:excel_app/widget/app_text_form_field.dart';
import 'package:excel_app/widget/custome_button_gradiant_widget.dart';
import 'package:excel_app/widget/dialogs.dart';
import 'package:excel_app/widget/popup_wrapper.dart';
import 'package:file_picker/file_picker.dart';
import 'package:file_saver/file_saver.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:mime/mime.dart';
import 'package:path/path.dart' as p;

class ImportUserToAddProjectDialog extends StatefulWidget {
  const ImportUserToAddProjectDialog({super.key});

  @override
  State<ImportUserToAddProjectDialog> createState() => _ImportUserToAddProjectDialogState();
}

class _ImportUserToAddProjectDialogState extends State<ImportUserToAddProjectDialog> {
  final excleFile = ValueNotifier<AppFile?>(null);
  final isButtonLoading = ValueNotifier<bool>(false);
  final isExcleDownloadLoading = ValueNotifier<bool>(false);

  final uploadFileController = TextEditingController();

  Future<void> downloadCSV(String file) async {
    final bytes = Uint8List.fromList(utf8.encode(file));
    final ext = p.extension(file).replaceFirst('.', '');
    final fileName = p.basename(file);
    await FileSaver.instance.saveFile(
      name: fileName.split('.').first,
      bytes: bytes,
      ext: ext.isNotEmpty ? ext : 'txt',
    );
  }

  @override
  Widget build(BuildContext context) {
    return PopUpWrapper(
      constraints: const BoxConstraints(maxWidth: 382, maxHeight: 354),
      childrenPadding: const EdgeInsets.fromLTRB(20, 20, 20, 30),
      showCancelButton: false,
      children: [
        ValueListenableBuilder(
          valueListenable: isButtonLoading,
          builder: (context, loading, _) {
            return IgnorePointer(
              ignoring: loading,
              child: ValueListenableBuilder(
                valueListenable: excleFile,
                builder: (context, value, _) {
                  return Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        'Import Users',
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                              fontSize: 18,
                              fontWeight: FontWeight.w600,
                            ),
                      ),
                      const Gap(20),
                      AppTextFormField(
                        controller: uploadFileController,
                        title: 'Upload File',
                        readOnly: true,
                        hintText: 'Select File',
                        onTap: () async {
                          final pickedFile = await FilePicker.platform.pickFiles(
                            type: FileType.custom,
                            allowedExtensions: ['xlsx', 'csv'],
                            withData: true,
                          );
                          if (pickedFile != null && pickedFile.files.isNotEmpty) {
                            final file = pickedFile.files.single;

                            final mimeType = lookupMimeType(file.name) ??
                                lookupMimeType('', headerBytes: file.bytes) ??
                                'application/octet-stream'; // Default fallback

                            excleFile.value = AppFile(
                              bytes: file.bytes!, // Always use bytes on the web
                              name: file.name,
                              mimeType: mimeType,
                            );
                            uploadFileController.text = file.name;
                          } else {
                            log('No file selected');
                          }
                        },
                        suffixIcon: const CustomeButtonGradiantWidget(
                          buttonText: 'Browse',
                          isGradient: true,
                          margin: EdgeInsets.only(right: 4),
                          width: 100,
                          height: 38,
                        ),
                      ),
                      const Gap(12),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          ValueListenableBuilder(
                            valueListenable: isExcleDownloadLoading,
                            builder: (context, downloadExcleLoading, _) {
                              return CustomeButtonGradiantWidget(
                                width: 190,
                                height: 32,
                                textColor: AppColors.primary,
                                isLoading: downloadExcleLoading,
                                preIcon: const Padding(
                                  padding: EdgeInsets.only(right: 6),
                                  child: AppAssetImage(AppAssets.downloadIcon),
                                ),
                                buttonText: 'Download Sample File',
                                isUseContainerBorder: true,
                                onTap: downloadExcleFile,
                              );
                            },
                          ),
                          RichText(
                            text: TextSpan(
                              children: [
                                TextSpan(
                                  text: 'File type: ',
                                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(color: AppColors.subText),
                                ),
                                TextSpan(
                                  text: '.xlsx',
                                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w500),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                      const Gap(30),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          CustomeButtonGradiantWidget(
                            buttonText: 'Cancel',
                            isUseContainerBorder: true,
                            width: 100,
                            height: 38,
                            onTap: () {
                              context.pop();
                            },
                          ),
                          const Gap(15),
                          CustomeButtonGradiantWidget(
                            buttonText: 'Upload',
                            width: 100,
                            height: 38,
                            isGradient: true,
                            isLoading: loading,
                            onTap: () {
                              if (value != null) {
                                importUser();
                                // successFullyImportUser();
                              } else {
                                Utility.toast(message: 'First Upload File');
                              }
                            },
                          ),
                        ],
                      ),
                    ],
                  );
                },
              ),
            );
          },
        ),
      ],
    );
  }

  Future<void> importUser() async {
    isButtonLoading.value = true;
    log('${excleFile.value}excle file');
    final response = await getIt<IUserRepository>().importUser(
      excleUserFile: excleFile.value,
    );
    await response.fold(
      (l) {
        isButtonLoading.value = false;
        context.pop();
        DailogBox.showBluredBgDailog(context, const UserImportFailedAlertDailog());
      },
      (r) async {
        isButtonLoading.value = false;
        context.pop();
        if (r.status == '1') {
          await DailogBox.showBluredBgDailog(context, const UserAddSucceffullyAlertDailog());
        } else {
          await DailogBox.showBluredBgDailog(context, const UserDataMissMatchedAlertDailog());
        }
      },
    );
  }

  Future<void> downloadExcleFile() async {
    isExcleDownloadLoading.value = true;
    final response = await getIt<IUserRepository>().downloadExcleFile();
    response.fold(
      (l) {
        isExcleDownloadLoading.value = false;
        context.pop();
        Utility.toast(message: l.message);
      },
      (r) {
        isExcleDownloadLoading.value = false;
        html.window.open('${AppStrings.storageBaseUrl}${r.path}', 'new tab');
        // downloadCSV('${AppStrings.storageBaseUrl}${r.path}');
        context.pop();
      },
    );
  }
}
