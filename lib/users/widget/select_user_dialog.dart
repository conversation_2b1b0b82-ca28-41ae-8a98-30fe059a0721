import 'package:excel_app/constants/app_assets.dart';
import 'package:excel_app/constants/app_colors.dart';
import 'package:excel_app/widget/app_asset_image.dart';
import 'package:excel_app/widget/app_text_form_field.dart';
import 'package:excel_app/widget/custome_button_gradiant_widget.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';

class SelectUserDialog extends StatefulWidget {
  const SelectUserDialog({super.key});

  @override
  State<SelectUserDialog> createState() => _SelectUserDialogState();
}

class _SelectUserDialogState extends State<SelectUserDialog> {
  final usersList = ValueNotifier<List<SampleUserModel>>([
    SampleUserModel(id: 1, name: 'User 1', userType: 'DOCTOR'),
    SampleUserModel(id: 2, name: 'User 2', userType: 'FIRSTYEAR'),
    SampleUserModel(id: 3, name: 'User 3', userType: 'SECONDYEAR'),
    SampleUserModel(id: 4, name: 'User 4', userType: 'THIRDYEAR'),
    SampleUserModel(id: 5, name: 'User 5', userType: 'FORTHYEAR'),
    SampleUserModel(id: 6, name: 'User 6', userType: 'SECONDYEAR'),
  ]);

  final selectedUsers = ValueNotifier<List<SampleUserModel>>([]);

  @override
  Widget build(BuildContext context) {
    return StatefulBuilder(
      builder: (context, setState) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          child: SizedBox(
            width: 390,
            child: Padding(
              padding: const EdgeInsets.fromLTRB(20, 20, 20, 30),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    'Select Users',
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                        ),
                  ),
                  const Gap(20),
                  AppTextFormField(
                    controller: TextEditingController(),
                    prefixIcon: AppAssets.searchIcon,
                    hintText: 'Search',
                  ),
                  const Gap(8),
                  ValueListenableBuilder<List<SampleUserModel>>(
                    valueListenable: selectedUsers,
                    builder: (context, selected, _) {
                      return ValueListenableBuilder<List<SampleUserModel>>(
                        valueListenable: usersList,
                        builder: (context, users, _) {
                          return Flexible(
                            child: ConstrainedBox(
                              constraints: const BoxConstraints(
                                maxHeight: 290,
                              ),
                              child: ListView.builder(
                                shrinkWrap: true,
                                itemBuilder: (context, index) {
                                  return InkWell(
                                    onTap: () {
                                      setState(() {
                                        if (selected.any((element) => element.id == users[index].id)) {
                                          selected.removeWhere((element) => element.id == users[index].id);
                                        } else {
                                          selected.add(users[index]);
                                        }
                                      });
                                    },
                                    child: Padding(
                                      padding: const EdgeInsets.symmetric(vertical: 12),
                                      child: Row(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          AppAssetImage(
                                            selected.any((element) => element.id == users[index].id)
                                                ? AppAssets.selectedCheckboxIcon
                                                : AppAssets.unselectedCheckboxIcon,
                                          ),
                                          const Gap(14),
                                          Text(
                                            users[index].name ?? '',
                                            style: Theme.of(context).textTheme.bodyMedium,
                                          ),
                                          const Gap(6),
                                          Container(
                                            height: 18,
                                            width: 16,
                                            alignment: Alignment.center,
                                            decoration: BoxDecoration(
                                              borderRadius: BorderRadius.circular(4),
                                              color: (users[index].userType == 'FIRSTYEAR'
                                                      ? AppColors.sunShade
                                                      : users[index].userType == 'SECONDYEAR'
                                                          ? AppColors.davyGray
                                                          : users[index].userType == 'THIRDYEAR'
                                                              ? AppColors.mediumGreen
                                                              : users[index].userType == 'FORTHYEAR'
                                                                  ? AppColors.tyrianPurple
                                                                  : AppColors.deepSkyBlue)
                                                  .withOpacity(0.1),
                                            ),
                                            child: Text(
                                              users[index].userType == 'FIRSTYEAR'
                                                  ? 'F'
                                                  : users[index].userType == 'SECONDYEAR'
                                                      ? 'S'
                                                      : users[index].userType == 'THIRDYEAR'
                                                          ? 'T'
                                                          : users[index].userType == 'FORTHYEAR'
                                                              ? 'F'
                                                              : 'D',
                                              style: Theme.of(context).textTheme.labelSmall?.copyWith(
                                                    color: users[index].userType == 'FIRSTYEAR'
                                                        ? AppColors.sunShade
                                                        : users[index].userType == 'SECONDYEAR'
                                                            ? AppColors.davyGray
                                                            : users[index].userType == 'THIRDYEAR'
                                                                ? AppColors.mediumGreen
                                                                : users[index].userType == 'FORTHYEAR'
                                                                    ? AppColors.tyrianPurple
                                                                    : AppColors.deepSkyBlue,
                                                    fontSize: 11,
                                                    fontWeight: FontWeight.w500,
                                                  ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  );
                                },
                                itemCount: users.length,
                              ),
                            ),
                          );
                        },
                      );
                    },
                  ),
                  const Gap(18),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      CustomeButtonGradiantWidget(
                        buttonText: 'Cancel',
                        isUseContainerBorder: true,
                        width: 100,
                        height: 38,
                        onTap: () {
                          Navigator.pop(context);
                        },
                      ),
                      const Gap(15),
                      CustomeButtonGradiantWidget(
                        buttonText: 'Add',
                        isGradient: true,
                        width: 100,
                        height: 38,
                        onTap: () {},
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}

class SampleUserModel {
  SampleUserModel({this.userType, this.id, this.name});
  final int? id;
  final String? name;
  final String? userType;
}
