import 'package:excel_app/app/cubit/refresh_cubit.dart';
import 'package:excel_app/constants/app_assets.dart';
import 'package:excel_app/users/widget/user_alert_dialog_box.dart';
import 'package:excel_app/utility/enums/enum_file.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';

class UserAddSucceffullyAlertDailog extends StatelessWidget {
  const UserAddSucceffullyAlertDailog({super.key});

  @override
  Widget build(BuildContext context) {
    return UserAlertDialogBox(
      imageIcon: AppAssets.checkCircleIcon,
      title: 'User Added Successful',
      message: 'Users added.',
      secondButtonTitle: 'Ok',
      secondButtonOnTap: () {
        context.read<RefreshCubit>().modifyUser(null, UserAction.edit);
        context.pop();
      },
    );
  }
}
