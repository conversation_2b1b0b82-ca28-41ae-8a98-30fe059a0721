// ignore_for_file: public_member_api_docs, sort_constructors_first

import 'package:excel_app/custom_calendar/cubit/custom_calendar_cubit.dart';
import 'package:excel_app/custom_calendar/models/custom_calender_config.dart';
import 'package:excel_app/custom_calendar/views/cal_day_list_view.dart';
import 'package:excel_app/projects/model/project_model.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';

class CustomCalender extends StatelessWidget {
  const CustomCalender({
    required this.customCalendarCubit,
    super.key,
    this.config,
    this.projectData,
  });
  final CustomCalenderConfig? config;
  final CustomCalendarCubit customCalendarCubit;
  final ProjectModel? projectData;

  @override
  Widget build(BuildContext context) {
    final calConfig = config ?? CustomCalenderConfig.defaultConfig();
    final dates = calConfig.getMonthsBetweenDates();
    return BlocProvider.value(
      value: customCalendarCubit,
      child: Builder(
        builder: (context) {
          return RepaintBoundary(
            child: AlignedGridView.count(
              key: ValueKey(calConfig),
              crossAxisCount: 3,
              mainAxisSpacing: 20,
              shrinkWrap: true,
              crossAxisSpacing: 20,
              cacheExtent: 10000,
              itemCount: dates.length,
              itemBuilder: (context, index) {
                final date = dates[index];
                return CalDayListView(
                  key: ValueKey('${date.year}-${date.month}'),
                  month: date.month,
                  year: date.year,
                  firstDayOfWeek: calConfig.firstDayOfWeek,
                  projectDetailData: projectData,
                );
              },
            ),
          );
        },
      ),
    );
  }
}
