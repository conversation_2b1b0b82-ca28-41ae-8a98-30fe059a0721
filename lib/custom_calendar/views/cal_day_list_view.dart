// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:developer';

import 'package:excel_app/constants/app_colors.dart';
import 'package:excel_app/constants/app_constants.dart';
import 'package:excel_app/custom_calendar/cubit/custom_calendar_cubit.dart';
import 'package:excel_app/custom_calendar/widgets/cal_day_view.dart';
import 'package:excel_app/projects/model/project_model.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';

class CalDayListView extends StatelessWidget {
  const CalDayListView({
    required this.year,
    required this.month,
    super.key,
    this.firstDayOfWeek = 0,
    this.projectDetailData,
  });
  final int year;
  final int month;
  final int firstDayOfWeek;
  final ProjectModel? projectDetailData;

  static final Map<String, List<String>> _weekDaysCache = {};
  static final Map<String, List<_DayData>> _daysDataCache = {};

  List<String> _getWeekDays(int firstDayOfWeek) {
    final cacheKey = firstDayOfWeek.toString();
    return _weekDaysCache[cacheKey] ??= _calculateWeekDays(firstDayOfWeek);
  }

  List<String> _calculateWeekDays(int firstDayOfWeek) {
    final baseWeekDays = <String>['Su', 'Mo', 'Tu', 'We', 'Th', 'Fr', 'Sa'];
    return [
      ...baseWeekDays.sublist(firstDayOfWeek),
      ...baseWeekDays.sublist(0, firstDayOfWeek),
    ];
  }

  List<_DayData> _getDaysData() {
    final cacheKey = '$year-$month-$firstDayOfWeek';
    return _daysDataCache[cacheKey] ??= _calculateDaysData();
  }

  List<_DayData> _calculateDaysData() {
    final firstDayOfMonth = DateTime(year, month);
    final daysInMonth = DateTime(year, month + 1).subtract(const Duration(days: 1)).day;
    final firstWeekday = (firstDayOfMonth.weekday + 7 - firstDayOfWeek) % 7;
    final daysInPreviousMonth = DateTime(year, month).subtract(const Duration(days: 1)).day;
    const totalDays = 42;

    return List.generate(totalDays, (index) {
      late final DateTime currentDate;
      late final int displayDay;
      var isCurrentMonth = true;

      if (index < firstWeekday) {
        displayDay = daysInPreviousMonth - (firstWeekday - index - 1);
        currentDate = DateTime(year, month - 1, displayDay);
        isCurrentMonth = false;
      } else if (index < firstWeekday + daysInMonth) {
        displayDay = index - firstWeekday + 1;
        currentDate = DateTime(year, month, displayDay);
      } else {
        displayDay = index - (firstWeekday + daysInMonth) + 1;
        currentDate = DateTime(year, month + 1, displayDay);
        isCurrentMonth = false;
      }

      return _DayData(
        date: currentDate,
        isCurrentMonth: isCurrentMonth,
      );
    });
  }

  @override
  Widget build(BuildContext context) {
    final weekDays = _getWeekDays(firstDayOfWeek);
    final daysData = _getDaysData();

    return RepaintBoundary(
      child: Container(
        decoration: BoxDecoration(
          color: AppColors.white,
          border: Border.all(color: AppColors.gray),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          children: [
            _MonthHeader(year: year, month: month),
            _WeekDaysHeader(weekDays: weekDays),
            _DaysGrid(
              daysData: daysData,
              projectDetailData: projectDetailData,
            ),
          ],
        ),
      ),
    );
  }
}

class _MonthHeader extends StatelessWidget {
  const _MonthHeader({required this.year, required this.month});

  final int year;
  final int month;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: const BoxDecoration(
        color: AppColors.gray,
        borderRadius: BorderRadius.vertical(top: Radius.circular(12)),
      ),
      child: Center(
        child: Text(
          DateFormat('MMMM yyyy').format(DateTime(year, month)),
          style: Theme.of(context).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
        ),
      ),
    );
  }
}

class _WeekDaysHeader extends StatelessWidget {
  const _WeekDaysHeader({required this.weekDays});

  final List<String> weekDays;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: weekDays.map((day) => _WeekDayLabel(day: day)).toList(),
      ),
    );
  }
}

class _WeekDayLabel extends StatelessWidget {
  const _WeekDayLabel({required this.day});

  final String day;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: 24,
      child: Center(
        child: Text(
          day,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
                color: AppColors.dark,
              ),
        ),
      ),
    );
  }
}

class _DaysGrid extends StatelessWidget {
  const _DaysGrid({
    required this.daysData,
    this.projectDetailData,
  });

  final List<_DayData> daysData;
  final ProjectModel? projectDetailData;

  // Only VACATION leave types can select HOLIDAY dates
  bool isVacationLeaveType(String? holidayKey, ProjectModel? projectData) {
    if (holidayKey == null || projectData == null) {
      return false;
    }

    // Check if it's specifically the predefined vacation type constant
    if (holidayKey == AppConstants.vactions) {
      log('Found match with AppConstants.vactions');
      return true;
    }

    // Find the category in project rules
    for (final rule in projectData.projectRules) {
      for (final category in rule.projectRuleCategories) {
        log('Checking category: ${category.name} (ID: ${category.leaveCategoryId}) against holidayKey: $holidayKey');

        if (category.leaveCategoryId.toString() == holidayKey) {
          // Get the category name
          final name = category.name?.toLowerCase() ?? '';

          // ONLY consider it a vacation type if it contains "vacation" or "vaction"
          // and does NOT contain "personal" or "holiday"
          if ((name == 'vacation' || name == 'vactions' || name == 'vacations') ||
              (name.contains('vac') && !name.contains('personal') && !name.contains('holiday'))) {
            log('Category IS a vacation type: $name');
            return true;
          }

          log('Category is NOT a vacation type: $name');
          return false;
        }
      }
    }

    log('No matching category found for holidayKey: $holidayKey');
    return false;
  }

  @override
  Widget build(BuildContext context) {
    return GridView.builder(
      physics: const NeverScrollableScrollPhysics(),
      shrinkWrap: true,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 7,
        crossAxisSpacing: 4,
        mainAxisSpacing: 4,
        childAspectRatio: 3.5 / 3,
      ),
      itemCount: daysData.length,
      itemBuilder: (context, index) {
        final dayData = daysData[index];
        return BlocBuilder<CustomCalendarCubit, CustomCalendarState>(
          buildWhen: (previous, current) => previous.currentHolidayKey != current.currentHolidayKey,
          builder: (context, state) {
            // Log when the outer builder rebuilds due to holiday key change
            log('Outer builder rebuilding with holiday key: ${state.currentHolidayKey}');

            return BlocBuilder<CustomCalendarCubit, CustomCalendarState>(
              buildWhen: (previous, current) =>
                  previous.isDateSelected(dayData.date) != current.isDateSelected(dayData.date) ||
                  previous.isDateProhibited(dayData.date) != current.isDateProhibited(dayData.date) ||
                  previous.currentHolidayKey != current.currentHolidayKey ||
                  previous.selectedDates != current.selectedDates,
              builder: (context, state) {
                final isPastDate = dayData.date.isBefore(DateTime.now().subtract(const Duration(days: 1)));
                final isWeekEnd = dayData.date.weekday == DateTime.saturday || dayData.date.weekday == DateTime.sunday;

                final cellColor = dayData.isCurrentMonth
                    ? state.getEffectiveHolidayForDisplay(dayData.date)?.bgColor ?? Colors.white
                    : Colors.white;

                // final adminSelectProjectHolliday = projectDetailData?.projectCalendarDates
                //     .map<DateTime>((element) => element.date ?? DateTime.now())
                //     .toList();

                // // Check if the date is a project holiday (any type)
                // final isProjectHoliday = adminSelectProjectHolliday?.any(
                //       (date) =>
                //           date.year == dayData.date.year &&
                //           date.month == dayData.date.month &&
                //           date.day == dayData.date.day,
                //     ) ??
                //     false;

                // Check if the date is specifically a HOLIDAY type (not other holiday types)
                final isHolidayType = projectDetailData?.projectCalendarDates.any(
                      (element) =>
                          element.dayType == AppConstants.prohibitedDay &&
                          element.date?.year == dayData.date.year &&
                          element.date?.month == dayData.date.month &&
                          element.date?.day == dayData.date.day,
                    ) ??
                    false;

                // Log the current holiday key from state
                if (isHolidayType && dayData.isCurrentMonth) {
                  log('Found HOLIDAY date: ${dayData.date}');
                  log('Current holiday key from state: ${state.currentHolidayKey}');
                }

                // Set text color - don't gray out HOLIDAY dates when VACATION type is selected
                final textColor = !dayData.isCurrentMonth
                    ? Colors.grey.withOpacity2(0.5)
                    : isPastDate || (isHolidayType && projectDetailData?.canUseProhibitedDays == 0) || isWeekEnd
                        ? Colors.grey.withOpacity2(0.5) // Gray text for past dates and non-vacation project holidays
                        : cellColor.getContrastingColor;

                // 1. Past dates
                // 2. Dates outside the current month
                // 3. Dates marked as prohibited by a leader
                final isSelectable = dayData.isCurrentMonth && !isPastDate && !isHolidayType;

                return CalDayView(
                  key: ValueKey('${dayData.date}'),
                  displayDay: dayData.date.day,
                  cellColor: cellColor,
                  onPressed: (isSelectable || (projectDetailData?.canUseProhibitedDays == 1 && isHolidayType))
                      ? () {
                          context.read<CustomCalendarCubit>().setSelectedDate(dayData.date);
                        }
                      : null,
                  textColor: textColor,
                );
              },
            );
          },
        );
      },
    );
  }
}

// Helper class for day data
class _DayData {
  final DateTime date;

  final bool isCurrentMonth;

  const _DayData({
    required this.date,
    required this.isCurrentMonth,
  });
}
