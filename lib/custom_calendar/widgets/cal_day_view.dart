// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:flutter/material.dart';

class CalDayView extends StatelessWidget {
  const CalDayView({
    required this.displayDay,
    super.key,
    this.onPressed,
    this.cellColor = Colors.white,
    this.textColor = Colors.black,
  });
  final VoidCallback? onPressed;
  final Color cellColor;
  final Color textColor;
  final int displayDay;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onPressed,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 250),
        alignment: Alignment.center,
        decoration: BoxDecoration(
          color: cellColor,
          borderRadius: BorderRadius.circular(6),
        ),
        child: Text(
          displayDay.toString(),
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: textColor,
              ),
        ),
      ),
    );
  }
}
