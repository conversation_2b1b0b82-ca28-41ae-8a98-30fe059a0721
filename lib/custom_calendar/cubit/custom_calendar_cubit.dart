import 'package:bloc/bloc.dart';
import 'package:collection/collection.dart';
import 'package:equatable/equatable.dart';
import 'package:excel_app/constants/app_constants.dart';
import 'package:excel_app/projects/model/holiday_model.dart';
import 'package:excel_app/projects/model/project_model.dart';
import 'package:excel_app/projects/model/project_rules_category_model.dart';
import 'package:excel_app/utility/extentions/date_extentions.dart';
import 'package:excel_app/utility/helpers/utility.dart';
import 'package:flutter/material.dart';
import 'package:injectable/injectable.dart';

part 'custom_calendar_state.dart';

@injectable
class CustomCalendarCubit extends Cubit<CustomCalendarState> {
  CustomCalendarCubit({
    @factoryParam ProjectModel? projectDataParams,
  }) : super(CustomCalendarState(projectData: projectDataParams));

  // Flag to track whether we're currently processing individual date selections
  // This is used to prevent weekday selections from being affected by individual date selections
  bool isProcessingIndividualDateSelection = false;

  void setHolidayKey({required String holidayKey, required Color color}) {
    // Check if the new holiday type is a vacation type
    final isNewTypeVacation = _isVacationType(holidayKey);

    // Get the current holiday key before changing it
    final oldHolidayKey = state.currentHolidayKey;

    if (!isNewTypeVacation && oldHolidayKey != holidayKey) {
      // If the new type is NOT vacation and we're changing types,
      // we need to clear any dates that are already selected for other holiday types

      // First, find the holiday model for the new holiday type (if it exists)
      final existingHolidayForNewType = state.selectedDates.firstWhereOrNull((h) => h.holidayKey == holidayKey);

      // Get all dates that are currently selected for other holiday types
      final datesFromOtherTypes = <DateTime>[];
      for (final holiday in state.selectedDates) {
        if (holiday.holidayKey != holidayKey) {
          datesFromOtherTypes.addAll(holiday.dates);
        }
      }

      // Now, emit the state with the new holiday key and color
      emit(
        state.copyWith(
          currentHolidayKey: holidayKey,
          currentHolidayColor: color,
        ),
      );

      // If there's an existing holiday model for the new type, we need to remove any dates
      // that are also selected for other holiday types
      if (existingHolidayForNewType != null && datesFromOtherTypes.isNotEmpty) {
        // Create a new list of dates that excludes dates from other holiday types
        final updatedDates = existingHolidayForNewType.dates
            .where((date) => !datesFromOtherTypes.any((d) => d.dateHasCode == date.dateHasCode))
            .toList();

        // Update the holiday model with the filtered dates
        final updatedHolidays = state.selectedDates.map((holiday) {
          if (holiday.holidayKey == holidayKey) {
            return holiday.copyWith(dates: updatedDates);
          }
          return holiday;
        }).toList();

        // Emit the updated state
        emit(
          state.copyWith(
            selectedDates: updatedHolidays,
          ),
        );
      }
    } else {
      // If the new type is vacation or we're not changing types,
      // just update the holiday key and color as before
      emit(
        state.copyWith(
          currentHolidayKey: holidayKey,
          currentHolidayColor: color,
        ),
      );
    }
  }

  void setSelectedDate(DateTime date) {
    // Set the flag to indicate we're processing an individual date selection
    // This will prevent the weekday checkboxes from being updated
    isProcessingIndividualDateSelection = true;

    final holidayKey = state.selectedDates.firstWhereOrNull((element) => element.holidayKey == state.currentHolidayKey);
    final dateAvailable = holidayKey?.dates.firstWhereOrNull((d) => d.dateHasCode == date.dateHasCode) != null;

    final existingHoliday = state.getHolidayFromDate(date);
    final isDateAlredaySelectedFromOtherHoliday =
        existingHoliday != null && existingHoliday.holidayKey != state.currentHolidayKey;

    // Check if current holiday type is prohibited day
    final isProhibitedDay = state.currentHolidayKey == AppConstants.prohibitedDay;
    // Check if current holiday type is vacation
    final isVacationType = _isVacationType(state.currentHolidayKey);

    // Special handling for prohibited days
    if (isProhibitedDay && isDateAlredaySelectedFromOtherHoliday) {
      // For prohibited days, we don't remove the existing holiday, just mark it as prohibited
      // for display priority

      // Add to prohibited dates set for display priority
      final updatedProhibitedDates = Set<int>.from(state.prohibitedDates)..add(date.dateHasCode);

      // Add the prohibited day to the list if not already there
      if (holidayKey != null) {
        emit(
          state.copyWith(
            selectedDates: state.selectedDates
                .map(
                  (e) => e.holidayKey == state.currentHolidayKey ? e.copyWith(dates: [...e.dates, date]) : e,
                )
                .toList(),
            prohibitedDates: updatedProhibitedDates,
          ),
        );
      } else {
        emit(
          state.copyWith(
            selectedDates: [
              ...state.selectedDates,
              HolidaysModel(
                holidayKey: state.currentHolidayKey,
                dates: [date],
                bgColor: state.currentHolidayColor,
              ),
            ],
            prohibitedDates: updatedProhibitedDates,
          ),
        );
      }
      return;
    }
    // Handle vacation type (highest priority)
    else if (isDateAlredaySelectedFromOtherHoliday && isVacationType) {
      // For vacation types, we want to add the date to the vacation type
      // This will ensure vacation type has priority for display

      // Check if the date is already selected for the vacation type
      final isAlreadySelectedForVacation = holidayKey != null &&
          state.selectedDates
              .firstWhere((e) => e.holidayKey == state.currentHolidayKey)
              .dates
              .any((d) => d.dateHasCode == date.dateHasCode);

      if (isAlreadySelectedForVacation) {
        // If it's already selected for vacation, remove it (toggle behavior)
        removeSelectedDate(date);
      } else {
        // If it's not already selected for vacation, add it
        if (holidayKey != null) {
          // Add the date to the existing vacation holiday
          final updatedSelectedDates = state.selectedDates
              .map(
                (e) => e.holidayKey == state.currentHolidayKey ? e.copyWith(dates: [...e.dates, date]) : e,
              )
              .toList();

          // Emit the updated state
          emit(
            state.copyWith(
              selectedDates: updatedSelectedDates,
            ),
          );

          // Force a rebuild by emitting the state again
          // This ensures the UI updates immediately
          Future.microtask(() {
            emit(
              state.copyWith(
                selectedDates: List.from(updatedSelectedDates),
              ),
            );
          });
        } else {
          // Create a new holiday model for the vacation type
          final updatedSelectedDates = [
            ...state.selectedDates,
            HolidaysModel(
              holidayKey: state.currentHolidayKey,
              dates: [date],
              bgColor: state.currentHolidayColor,
            ),
          ];

          // Emit the updated state
          emit(
            state.copyWith(
              selectedDates: updatedSelectedDates,
            ),
          );

          // Force a rebuild by emitting the state again
          // This ensures the UI updates immediately
          Future.microtask(() {
            emit(
              state.copyWith(
                selectedDates: List.from(updatedSelectedDates),
              ),
            );
          });
        }
      }
    }
    // Handle prohibited day (also high priority but different behavior)
    else if (isDateAlredaySelectedFromOtherHoliday && isProhibitedDay) {
      // For prohibited days, we still remove the existing holiday
      removeSelectedDate(date);

      // Add date to the prohibited day type
      if (holidayKey != null) {
        emit(
          state.copyWith(
            selectedDates: state.selectedDates
                .map(
                  (e) => e.holidayKey == state.currentHolidayKey ? e.copyWith(dates: [...e.dates, date]) : e,
                )
                .toList(),
          ),
        );
      } else {
        emit(
          state.copyWith(
            selectedDates: [
              ...state.selectedDates,
              HolidaysModel(
                holidayKey: state.currentHolidayKey,
                dates: [date],
                bgColor: state.currentHolidayColor,
              ),
            ],
          ),
        );
      }
    }
    // Handle date already selected with current holiday type
    else if (dateAvailable) {
      // If removing a prohibited day, also remove from prohibited dates set
      if (state.currentHolidayKey == AppConstants.prohibitedDay) {
        final updatedProhibitedDates = Set<int>.from(state.prohibitedDates)..remove(date.dateHasCode);
        removeSelectedDate(date);
        emit(state.copyWith(prohibitedDates: updatedProhibitedDates));
      } else {
        removeSelectedDate(date);
      }
      return;
    }
    // Handle case where date is already selected for another holiday type and current type is NOT vacation or prohibited
    else if (isDateAlredaySelectedFromOtherHoliday && !isVacationType && !isProhibitedDay) {
      // For non-vacation, non-prohibited types, we don't allow selecting dates that are already selected for other types
      // Just return without making any changes
      return;
    }
    // Handle adding to existing holiday type
    else if (holidayKey != null) {
      // If adding a prohibited day, also add to prohibited dates set
      if (isProhibitedDay) {
        final updatedProhibitedDates = Set<int>.from(state.prohibitedDates)..add(date.dateHasCode);
        emit(
          state.copyWith(
            selectedDates: state.selectedDates
                .map(
                  (e) => e.holidayKey == state.currentHolidayKey ? e.copyWith(dates: [...e.dates, date]) : e,
                )
                .toList(),
            prohibitedDates: updatedProhibitedDates,
          ),
        );
      } else {
        emit(
          state.copyWith(
            selectedDates: state.selectedDates
                .map(
                  (e) => e.holidayKey == state.currentHolidayKey ? e.copyWith(dates: [...e.dates, date]) : e,
                )
                .toList(),
          ),
        );
      }
      return;
    }
    // Handle creating new holiday type
    else {
      // If adding a prohibited day, also add to prohibited dates set
      if (isProhibitedDay) {
        final updatedProhibitedDates = Set<int>.from(state.prohibitedDates)..add(date.dateHasCode);
        emit(
          state.copyWith(
            selectedDates: [
              ...state.selectedDates,
              HolidaysModel(
                holidayKey: state.currentHolidayKey,
                dates: [date],
                bgColor: state.currentHolidayColor,
              ),
            ],
            prohibitedDates: updatedProhibitedDates,
          ),
        );
      } else {
        emit(
          state.copyWith(
            selectedDates: [
              ...state.selectedDates,
              HolidaysModel(
                holidayKey: state.currentHolidayKey,
                dates: [date],
                bgColor: state.currentHolidayColor,
              ),
            ],
          ),
        );
      }
    }

    // Reset the flag after processing is complete
    isProcessingIndividualDateSelection = false;
  }

  // Helper method to check if a holiday type is vacation-related
  bool _isVacationType(String? holidayKey) {
    if (holidayKey == null) return false;

    // Get the category name from the project rule categories
    final projectData = state.projectData;
    if (projectData == null) return false;

    // Check all project rules and their categories
    for (final rule in projectData.projectRules) {
      for (final category in rule.projectRuleCategories) {
        if (category.leaveCategoryId.toString() == holidayKey) {
          // Check if the category name contains 'vacation' or 'vaction'
          final name = category.name?.toLowerCase() ?? '';
          return name.contains('vacation') || name.contains('vaction');
        }
      }
    }

    return false;
  }

  void removeSelectedDate(DateTime date) {
    // Check if this date is in the prohibited dates set
    final isProhibited = state.prohibitedDates.contains(date.dateHasCode);

    // Update the prohibited dates set if needed
    Set<int>? updatedProhibitedDates;
    if (isProhibited) {
      updatedProhibitedDates = Set<int>.from(state.prohibitedDates)..remove(date.dateHasCode);
    }

    emit(
      state.copyWith(
        selectedDates: [...state.selectedDates]
            .map(
              (holiday) {
                final getSelectedDate = holiday.getSelectedDate(date);

                if (holiday.dates.length == 1 && getSelectedDate != null) {
                  return null;
                }
                if (getSelectedDate != null) {
                  return holiday.copyWith(
                    dates: holiday.dates.where((d) => d.dateHasCode != date.dateHasCode).toList(),
                  );
                }
                return holiday;
              },
            )
            .whereType<HolidaysModel>()
            .toList(),
        prohibitedDates: updatedProhibitedDates,
      ),
    );
  }

  void init() {
    if (state.projectData == null) {
      emit(
        state.copyWith(
          selectedDates: <HolidaysModel>[],
          prohibitedDates: <int>{},
        ),
      );
      return;
    }

    // Get holidays from project data
    final holidays = state.projectData!.getHolidays();

    // Initialize prohibited dates set
    final prohibitedDates = <int>{};

    // Find all dates marked as prohibited days
    for (final holiday in holidays) {
      if (holiday.holidayKey == AppConstants.prohibitedDay) {
        for (final date in holiday.dates) {
          prohibitedDates.add(date.dateHasCode);
        }
      }
    }

    emit(
      state.copyWith(
        selectedDates: holidays,
        prohibitedDates: prohibitedDates,
      ),
    );
  }

  void initAvailableDate(List<ProjectRuleCategoryModel> data) {
    if (data.isEmpty) return;

    final holidaysList = <HolidaysModel>[];
    final prohibitedDates = <int>{};

    for (final element in data) {
      final holidayKey = element.leaveCategoryId.toString();
      final holidayIndex = holidaysList.indexWhere((e) => e.holidayKey == holidayKey);

      // Check if this is a prohibited day
      final isProhibitedDay = holidayKey == AppConstants.prohibitedDay;

      // If it's a prohibited day, add to the prohibited dates set
      if (isProhibitedDay && element.date != null) {
        prohibitedDates.add(element.date!.dateHasCode);
      }

      if (holidayIndex != -1) {
        holidaysList[holidayIndex] = holidaysList[holidayIndex].copyWith(
          dates: [...holidaysList[holidayIndex].dates, element.date!],
        );
      } else {
        holidaysList.add(
          HolidaysModel(
            holidayKey: holidayKey,
            dates: [element.date].whereType<DateTime>().toList(),
            bgColor: element.leaveCategory?.color ?? Colors.transparent,
          ),
        );
      }
    }

    emit(
      state.copyWith(
        selectedDates: [...holidaysList],
        prohibitedDates: prohibitedDates,
      ),
    );
  }

  void setNotAvaiableDate(int day) {
    final projectData = state.projectData;
    if (projectData?.startDate == null || projectData?.endDate == null) return;

    final currentKey = state.currentHolidayKey;
    final existingHolidayIndex = state.selectedDates.indexWhere((e) => e.holidayKey == currentKey);

    // Check if this is a prohibited day
    final isProhibitedDay = currentKey == AppConstants.prohibitedDay;

    // Get all dates with the specified weekday
    final allWeekdayDates = Utility.generateWeekdayDates(
      startDate: projectData!.startDate!,
      endDate: projectData.endDate!,
      weekday: day,
    );

    // Filter out dates that should not be selectable:
    // 1. Past dates
    // 2. Project holiday dates (unless it's a vacation type)
    // 3. Weekends (unless it's a vacation type and the date is a holiday)
    final validDates = allWeekdayDates.where((date) {
      // Skip past dates
      if (date.isBefore(DateTime.now().subtract(const Duration(days: 1)))) {
        return false;
      }

      // Check if it's a weekend
      final isWeekend = date.weekday == DateTime.saturday || date.weekday == DateTime.sunday;

      // Check if it's a project holiday
      final isProjectHoliday = projectData.projectCalendarDates.any(
        (element) =>
            element.date?.year == date.year && element.date?.month == date.month && element.date?.day == date.day,
      );

      // Check if it's specifically a HOLIDAY type (not other holiday types)
      final isHolidayType = projectData.projectCalendarDates.any(
        (element) =>
            element.dayType == AppConstants.holiday &&
            element.date?.year == date.year &&
            element.date?.month == date.month &&
            element.date?.day == date.day,
      );

      // Check if current leave type is specifically VACATION
      final isVacationType = _isVacationType(currentKey);

      // Special case: If it's a HOLIDAY type date and current leave type is VACATION,
      // it should be selectable regardless of other conditions
      final isHolidayAndVacation = isHolidayType && isVacationType;

      // Skip weekends unless it's a holiday and vacation type
      if (isWeekend && !isHolidayAndVacation) {
        return false;
      }

      // Skip project holidays (unless it's specifically a HOLIDAY type and current type is vacation)
      if (isProjectHoliday && !isHolidayAndVacation) {
        return false;
      }

      return true;
    }).toList();

    final totalListDatesSet = Set<DateTime>.from(validDates);

    // For prohibited days, we don't remove other holiday dates
    if (state.selectedDates.isNotEmpty && !isProhibitedDay) {
      final otherHolidayDatesSet = Set<DateTime>.from(
        state.selectedDates.where((element) => element.holidayKey != currentKey).expand((e) => e.dates),
      );

      totalListDatesSet.removeAll(otherHolidayDatesSet);
    }

    // Update prohibited dates set if needed
    Set<int>? updatedProhibitedDates;
    if (isProhibitedDay) {
      updatedProhibitedDates = Set<int>.from(state.prohibitedDates);
      for (final date in totalListDatesSet) {
        updatedProhibitedDates.add(date.dateHasCode);
      }
    }

    if (existingHolidayIndex != -1) {
      final updatedDates = List<HolidaysModel>.from(state.selectedDates);
      final existingDates = Set<DateTime>.from(updatedDates[existingHolidayIndex].dates)..addAll(totalListDatesSet);

      updatedDates[existingHolidayIndex] = updatedDates[existingHolidayIndex].copyWith(
        dates: existingDates.toList(),
      );

      emit(
        state.copyWith(
          selectedDates: updatedDates,
          prohibitedDates: updatedProhibitedDates,
        ),
      );
    } else {
      emit(
        state.copyWith(
          selectedDates: [
            ...state.selectedDates,
            HolidaysModel(
              holidayKey: currentKey,
              dates: totalListDatesSet.toList(),
              bgColor: state.currentHolidayColor,
            ),
          ],
          prohibitedDates: updatedProhibitedDates,
        ),
      );
    }
  }

  void removeNotAvaiableDate(int day) {
    final projectData = state.projectData;
    if (projectData?.startDate == null || projectData?.endDate == null) return;

    final currentKey = state.currentHolidayKey;
    final existingHolidayIndex = state.selectedDates.indexWhere((e) => e.holidayKey == currentKey);
    if (existingHolidayIndex == -1) return;

    // Check if this is a prohibited day
    final isProhibitedDay = currentKey == AppConstants.prohibitedDay;

    // Filter to only include dates that are actually in the current selection
    final holidayToUpdate = state.selectedDates[existingHolidayIndex];
    final selectedDatesWithWeekday = holidayToUpdate.dates.where((date) => date.weekday == day).toList();

    final datesToRemove = Set<DateTime>.from(selectedDatesWithWeekday);

    // Update prohibited dates set if needed
    Set<int>? updatedProhibitedDates;
    if (isProhibitedDay) {
      updatedProhibitedDates = Set<int>.from(state.prohibitedDates);
      for (final date in datesToRemove) {
        updatedProhibitedDates.remove(date.dateHasCode);
      }
    }

    final updatedDates = List<HolidaysModel>.from(state.selectedDates);
    final holidayToRemoveFrom = updatedDates[existingHolidayIndex];

    final remainingDates = Set<DateTime>.from(holidayToRemoveFrom.dates)..removeAll(datesToRemove);

    if (remainingDates.isEmpty) {
      updatedDates.removeAt(existingHolidayIndex);
    } else {
      // Update the existing holiday with remaining dates
      updatedDates[existingHolidayIndex] = holidayToRemoveFrom.copyWith(
        dates: remainingDates.toList(),
      );
    }

    emit(
      state.copyWith(
        selectedDates: updatedDates,
        prohibitedDates: updatedProhibitedDates,
      ),
    );
  }
}
