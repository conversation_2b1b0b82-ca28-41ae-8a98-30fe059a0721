part of 'custom_calendar_cubit.dart';

final class CustomCalendarState extends Equatable {
  const CustomCalendarState({
    this.selectedDates = const [],
    this.currentHolidayKey,
    this.currentHolidayColor,
    this.projectData,
    this.prohibitedDates = const {},
  });
  final List<HolidaysModel> selectedDates;
  final String? currentHolidayKey;
  final Color? currentHolidayColor;
  final ProjectModel? projectData;
  // Set of dates that are marked as prohibited (for display priority)
  final Set<int> prohibitedDates;

  @override
  List<Object?> get props => [selectedDates, currentHolidayKey, currentHolidayColor, projectData, prohibitedDates];

  CustomCalendarState copyWith({
    List<HolidaysModel>? selectedDates,
    String? currentHolidayKey,
    Color? currentHolidayColor,
    ProjectModel? projectData,
    Set<int>? prohibitedDates,
  }) {
    return CustomCalendarState(
      selectedDates: selectedDates ?? this.selectedDates,
      currentHolidayKey: currentHolidayKey ?? this.currentHolidayKey,
      currentHolidayColor: currentHolidayColor ?? this.currentHolidayColor,
      projectData: projectData ?? this.projectData,
      prohibitedDates: prohibitedDates ?? this.prohibitedDates,
    );
  }

  HolidaysModel? getHolidayFromDate(DateTime date) {
    return selectedDates.firstWhereOrNull(
      (element) => element.dates.firstWhereOrNull((d) => d.dateHasCode == date.dateHasCode) != null,
    );
  }

  bool isDateSelected(DateTime date) {
    return getHolidayFromDate(date)?.getSelectedDate(date) != null;
  }

  // Check if a date is marked as prohibited
  bool isDateProhibited(DateTime date) {
    return prohibitedDates.contains(date.dateHasCode);
  }

  // Get the effective holiday for display (prioritizing prohibited days and vacation types)
  HolidaysModel? getEffectiveHolidayForDisplay(DateTime date) {
    // First check for prohibited days (highest display priority)
    if (isDateProhibited(date)) {
      // First try to find the prohibited day holiday
      final prohibitedHoliday = selectedDates.firstWhereOrNull(
        (element) =>
            element.holidayKey == AppConstants.prohibitedDay &&
            element.dates.firstWhereOrNull((d) => d.dateHasCode == date.dateHasCode) != null,
      );

      if (prohibitedHoliday != null) {
        return prohibitedHoliday;
      }

      // If no prohibited holiday model exists but the date is in prohibitedDates,
      // create a temporary one for display purposes
      return HolidaysModel(
        holidayKey: AppConstants.prohibitedDay,
        dates: [date],
        bgColor: Colors.pink[100], // Use a default color for prohibited days
      );
    }

    // Check if this date is selected for any holiday type
    final holidaysForDate = selectedDates
        .where(
          (element) => element.dates.any((d) => d.dateHasCode == date.dateHasCode),
        )
        .toList();

    // If this date is selected for any holiday type
    if (holidaysForDate.isNotEmpty) {
      // First check if there's a vacation type in the project rules
      final projectData = this.projectData;
      if (projectData != null) {
        // Find all vacation type categories in the project
        final vacationCategories = <String>[];
        for (final rule in projectData.projectRules) {
          for (final category in rule.projectRuleCategories) {
            final name = category.name?.toLowerCase() ?? '';
            if (name.contains('vacation') || name.contains('vaction')) {
              vacationCategories.add(category.leaveCategoryId.toString());
            }
          }
        }

        // Check if any of the vacation categories have this date selected
        for (final vacationCategory in vacationCategories) {
          final vacationHoliday = selectedDates.firstWhereOrNull(
            (element) =>
                element.holidayKey == vacationCategory && element.dates.any((d) => d.dateHasCode == date.dateHasCode),
          );

          // If a vacation category has this date selected, prioritize it for display
          if (vacationHoliday != null) {
            return vacationHoliday;
          }
        }
      }

      // If no vacation type has this date selected, check for any vacation type
      final vacationHoliday = selectedDates.firstWhereOrNull(
        (element) => _isVacationType(element.holidayKey) && element.dates.any((d) => d.dateHasCode == date.dateHasCode),
      );

      // If there's a vacation type selected for this date, return it
      if (vacationHoliday != null) {
        return vacationHoliday;
      }
    }

    // Otherwise return the regular holiday
    return getHolidayFromDate(date);
  }

  // Helper method to check if a holiday type is vacation-related
  bool _isVacationType(String? holidayKey) {
    if (holidayKey == null) return false;

    // Check if it's specifically the predefined vacation type constant
    if (holidayKey == AppConstants.vactions) {
      return true;
    }

    // Get the project data
    final projectData = this.projectData;
    if (projectData == null) return false;

    // Check all project rules and their categories
    for (final rule in projectData.projectRules) {
      for (final category in rule.projectRuleCategories) {
        if (category.leaveCategoryId.toString() == holidayKey) {
          // Check if the category name contains 'vacation' or 'vaction'
          final name = category.name?.toLowerCase() ?? '';
          return name.contains('vacation') || name.contains('vaction');
        }
      }
    }

    return false;
  }
}
