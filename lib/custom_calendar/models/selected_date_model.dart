// // ignore_for_file: public_member_api_docs, sort_constructors_first
// import 'package:collection/collection.dart';
// import 'package:equatable/equatable.dart';
// import 'package:flutter/material.dart';

// class SelectedDateModel extends Equatable {
//   final DateTime date;
//   final Color cellColor;
//   final String holidayKey;

//   const SelectedDateModel({
//     required this.date,
//     required this.cellColor,
//     required this.holidayKey,
//   });

//   SelectedDateModel copyWith({
//     DateTime? date,
//     Color? cellColor,
//     String? holidayKey,
//   }) {
//     return SelectedDateModel(
//       date: date ?? this.date,
//       cellColor: cellColor ?? this.cellColor,
//       holidayKey: holidayKey ?? this.holidayKey,
//     );
//   }

//   @override
//   List<Object?> get props => [date, cellColor, holidayKey];

//   @override
//   bool get stringify => true;
// }

// extension ListSelecectedDateModelX on List<SelectedDateModel> {
//   SelectedDateModel? getSelectedDate(DateTime date) {
//     return firstWhereOrNull(
//       (element) => element.date.year == date.year && element.date.month == date.month && element.date.day == date.day,
//     );
//   }
// }
