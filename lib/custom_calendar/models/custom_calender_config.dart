// ignore_for_file: public_member_api_docs, sort_constructors_first
class CustomCalenderConfig {
  final int firstDayOfWeek;
  final DateTime startDate;
  final DateTime endDate;
  const CustomCalenderConfig({
    required this.endDate,
    required this.startDate,
    this.firstDayOfWeek = 0,
  });

  CustomCalenderConfig copyWith({
    int? firstDayOfWeek,
    DateTime? startDate,
    DateTime? endDate,
  }) {
    return CustomCalenderConfig(
      firstDayOfWeek: firstDayOfWeek ?? this.firstDayOfWeek,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
    );
  }

  factory CustomCalenderConfig.defaultConfig() {
    final today = DateTime.now();
    return CustomCalenderConfig(
      startDate: today,
      endDate: today.add(const Duration(days: 330)),
    );
  }

  List<DateTime> getMonthsBetweenDates() {
    final months = <DateTime>[];
    var currentDate = DateTime(startDate.year, startDate.month);
    final lastDate = DateTime(endDate.year, endDate.month);

    while (currentDate.isBefore(lastDate) || currentDate.year == lastDate.year && currentDate.month == lastDate.month) {
      months.add(currentDate);
      currentDate = DateTime(
        currentDate.year + (currentDate.month == 12 ? 1 : 0),
        currentDate.month == 12 ? 1 : currentDate.month + 1,
      );
    }

    return months;
  }
}
