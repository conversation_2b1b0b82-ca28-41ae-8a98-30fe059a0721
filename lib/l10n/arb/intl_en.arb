{"@@locale": "en", "languageKey": "en", "spotlink": "Spotlink", "login": "<PERSON><PERSON>", "email": "Email", "password": "Password", "pleaseEnterValidEmail": "Please enter a valid email", "pleaseEnterEmail": "Please enter email", "pleaseEnterPassword": "Please enter password", "logout": "Logout", "areYouSureWantToLogout": "Are you sure want to logout", "no": "No", "dashboard": "Dashboard", "admin": "Admin", "home": "Home", "projects": "Projects", "users": "Users", "rollManagement": "Roll Management", "connectGoogleCal": "Connect Google Cal", "profile": "Profile", "addUser": "Add User", "editUser": "Edit User", "edit": "Edit", "add": "Add", "name": "Name", "type": "Type", "select": "Select", "pleaseEnterName": "Please enter name", "pleaseSelectType": "Please select type", "user": "User", "action": "Action", "leader": "Leader", "deadline": "Deadline", "basicDetails": "Basic Details", "shiftSettings": "Shift Setting", "holidays": "Holidays", "userAndSettings": "User & Settings", "projectName": "Project Name", "pleaseEnterProjectName": "Please enter project name", "year": "Year", "pleaseEnterYear": "Please enter year", "startFromMonth": "Start From Month", "day": "Day", "inteDate": "Inte Date", "finalDate": "Final Date", "days": "Days", "pleaseEnterDays": "Please enter days", "leaderType": "Leader Type", "leaderUser": "Leader + User", "leaderOnly": "Leader Only", "cancle": "Cancel", "skipAndCreateProject": "Skip & Create Project", "next": "Next", "userPerShift": "User per shift", "pleaeseEnterUserPerShift": "Please enter user per shift", "selectUserType": "Select user type", "doctorPerShift": "Doctor per shift", "pleaeseEnterDoctorPerShift": "Please enter doctor per shift", "workingDaysPerMonthPerDoctor": "Working days per month per doctor", "pleaseEnterWorkingDaysPerMonthPerDoctor": "Please enter working days per month per doctor", "selectAvailabilityCategoryForDoctors": "Select availability category for doctors", "firstYearResidentPerShift": "First year resident per shift", "pleaseEnterFirstYearResidentPerShift": "Please enter first year resident per shift", "workingDaysPerMonthPerResident": "Working days per month per resident", "pleaseEnterWorkingDaysPerMonthPerResident": "Please enter working days per month per resident", "selecreAvailabilityCategoryForFirstYearResidents": "Select availability category for first year residents", "doctors": "Doctors", "firstYearResident": "First year resident", "availabilityInputDeadline": "Availability input deadline", "pleaseEnterAvailabilityInputDeadline": "Please enter availability input deadline", "howMenyPersonLeavePerMonth": "How many personal leave days per month can take?", "pleaseEnterHowMenyPersonLeavePerMonth": "Please enter how many personal leave days per month can take", "back": "Back", "holidayCategories": "Holiday Categories", "chat": "Cha<PERSON>", "duration": "Duration", "availabilityStatus": "Availability Status", "availability": "Availability"}