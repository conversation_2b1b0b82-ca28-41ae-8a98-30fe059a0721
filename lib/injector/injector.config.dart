// GENERATED CODE - DO NOT MODIFY BY HAND

// **************************************************************************
// InjectableConfigGenerator
// **************************************************************************

// ignore_for_file: type=lint
// coverage:ignore-file

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'package:excel_app/app/bloc/authentication_bloc.dart' as _i424;
import 'package:excel_app/app/cubit/refresh_cubit.dart' as _i52;
import 'package:excel_app/auth/repository/i_auth_repository.dart' as _i771;
import 'package:excel_app/chat/repository/chat_repository.dart' as _i176;
import 'package:excel_app/chat/repository/i_chat_repository.dart' as _i739;
import 'package:excel_app/custom_calendar/cubit/custom_calendar_cubit.dart'
    as _i361;
import 'package:excel_app/dashboard/repository/i_dashboard_repository.dart'
    as _i576;
import 'package:excel_app/holiday_category/repository/i_holiday_catergory_repository.dart'
    as _i585;
import 'package:excel_app/injector/injector_module.dart' as _i71;
import 'package:excel_app/local_storage/i_local_storage_repository.dart'
    as _i999;
import 'package:excel_app/projects/model/project_model.dart' as _i432;
import 'package:excel_app/projects/repository/i_project_repository.dart'
    as _i675;
import 'package:excel_app/users/repository/i_user_repository.dart' as _i1044;
import 'package:excel_app/utility/network/client.dart' as _i767;
import 'package:excel_app/utility/network/http_client.dart' as _i160;
import 'package:get_it/get_it.dart' as _i174;
import 'package:injectable/injectable.dart' as _i526;
import 'package:shared_preferences/shared_preferences.dart' as _i460;

extension GetItInjectableX on _i174.GetIt {
// initializes the registration of main-scope dependencies inside of GetIt
  Future<_i174.GetIt> init({
    String? environment,
    _i526.EnvironmentFilter? environmentFilter,
  }) async {
    final gh = _i526.GetItHelper(
      this,
      environment,
      environmentFilter,
    );
    final injectableModule = _$InjectableModule();
    gh.factory<_i52.RefreshCubit>(() => _i52.RefreshCubit());
    await gh.factoryAsync<_i460.SharedPreferences>(
      () => injectableModule.prefs,
      preResolve: true,
    );
    gh.lazySingleton<_i424.AuthenticationBloc>(
        () => _i424.AuthenticationBloc());
    gh.factory<_i999.ILocalStorageRepository>(
        () => _i999.LocalStorageRepository(gh<_i460.SharedPreferences>()));
    gh.factoryParam<_i361.CustomCalendarCubit, _i432.ProjectModel?, dynamic>((
      projectDataParams,
      _,
    ) =>
        _i361.CustomCalendarCubit(projectDataParams: projectDataParams));
    gh.factory<_i767.Client>(
        () => _i160.HttpClient(gh<_i999.ILocalStorageRepository>()));
    gh.factory<_i675.IProjectRepository>(
        () => _i675.ProjectRepository(gh<_i767.Client>()));
    gh.factory<_i1044.IUserRepository>(
        () => _i1044.UserRepository(gh<_i767.Client>()));
    gh.factory<_i585.IHolidayCategoryRepository>(
        () => _i585.HolidayCatergoryRepository(gh<_i767.Client>()));
    gh.factory<_i739.IChatRepository>(
        () => _i176.ChatRepository(gh<_i767.Client>()));
    gh.factory<_i576.IDashboardRepository>(
        () => _i576.DashboardRepository(gh<_i767.Client>()));
    gh.factory<_i771.IAuthRepository>(() => _i771.AuthRepository(
          gh<_i767.Client>(),
          gh<_i999.ILocalStorageRepository>(),
        ));
    return this;
  }
}

class _$InjectableModule extends _i71.InjectableModule {}
