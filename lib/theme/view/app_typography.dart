import 'package:excel_app/constants/app_colors.dart';
import 'package:flutter/material.dart';

class LightAppTypography {
  static const _fontFamily = 'SFProDisplay';

  static const _style = TextStyle(fontFamily: _fontFamily, color: AppColors.dark);

  static TextStyle displayLarge = _style.copyWith(
    fontSize: 96,
    fontWeight: FontWeight.w600, // Semibold
    letterSpacing: -1.5,
  );

  static TextStyle displayMedium = _style.copyWith(
    fontSize: 60,
    fontWeight: FontWeight.w600, // Semibold
    letterSpacing: -0.5,
  );

  static TextStyle displaySmall = _style.copyWith(
    fontSize: 48,
    fontWeight: FontWeight.w400, // Regular
  );

  static TextStyle headlineMedium = _style.copyWith(
    fontSize: 34,
    fontWeight: FontWeight.w400, // Regular
    letterSpacing: 0.25,
  );

  static TextStyle headlineSmall = _style.copyWith(
    fontSize: 24,
    fontWeight: FontWeight.w400, // Regular
  );

  static TextStyle titleLarge = _style.copyWith(
    fontSize: 20,
    fontWeight: FontWeight.w500, // Medium
    letterSpacing: 0.15,
  );

  static TextStyle titleMedium = _style.copyWith(
    fontSize: 16,
    fontWeight: FontWeight.w400, // Regular
    letterSpacing: 0.15,
  );

  static TextStyle titleSmall = _style.copyWith(
    fontSize: 14,
    fontWeight: FontWeight.w500, // Medium
    letterSpacing: 0.1,
  );

  static TextStyle bodyLarge = _style.copyWith(
    fontSize: 16,
    fontWeight: FontWeight.w400, // Regular
    letterSpacing: 0.5,
  );

  static TextStyle bodyMedium = _style.copyWith(
    fontSize: 14,
    fontWeight: FontWeight.w400, // Regular
    letterSpacing: 0.25,
  );

  static TextStyle bodySmall = _style.copyWith(
    fontSize: 12,
    fontWeight: FontWeight.w400, // Regular
    letterSpacing: 0.4,
  );

  static TextStyle labelLarge = _style.copyWith(
    fontSize: 14,
    fontWeight: FontWeight.w500, // Medium
    letterSpacing: 1.25,
  );

  static TextStyle labelMedium = _style.copyWith(
    fontSize: 12,
    fontWeight: FontWeight.w400, // Regular
    letterSpacing: 0.5,
  );

  static TextStyle labelSmall = _style.copyWith(
    fontSize: 10,
    fontWeight: FontWeight.w400, // Regular
    letterSpacing: 0.5,
  );
}
