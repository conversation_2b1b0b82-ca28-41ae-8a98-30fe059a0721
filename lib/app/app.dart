// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:html' as html;

import 'package:bot_toast/bot_toast.dart';
import 'package:excel_app/app/bloc/authentication_bloc.dart';
import 'package:excel_app/app/cubit/refresh_cubit.dart';
import 'package:excel_app/app/routes/go_router.dart';
import 'package:excel_app/injector/injector.dart';
import 'package:excel_app/l10n/generated/l10n.dart';
import 'package:excel_app/l10n/l10n_extension.dart';
import 'package:excel_app/theme/app_theme.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_localizations/flutter_localizations.dart';

class App extends StatefulWidget {
  const App({super.key});

  @override
  State<App> createState() => _AppState();
}

class _AppState extends State<App> {
  int width = html.window.innerWidth!;
  int height = html.window.innerHeight!;
  @override
  void initState() {
    super.initState();
    html.window.onResize.listen((event) {
      setState(() {
        width = html.window.innerWidth!;
        height = html.window.innerHeight!;
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return AppWrapper(
      child: MaterialApp.router(
        scrollBehavior: CustomScrollBehavior(),
        title: 'ikPlanner',
        debugShowCheckedModeBanner: false,
        theme: AppTheme.light,
        routeInformationParser: goRouter.routeInformationParser,
        routeInformationProvider: goRouter.routeInformationProvider,
        routerDelegate: goRouter.routerDelegate,
        locale: const Locale.fromSubtags(languageCode: 'en'),
        localizationsDelegates: const [
          I10n.delegate,
          AppLocalizations.delegate,
          GlobalMaterialLocalizations.delegate,
          GlobalWidgetsLocalizations.delegate,
        ],
        builder: BotToastInit(),
      ),
    );
  }
}

class AppWrapper extends StatelessWidget {
  const AppWrapper({
    required this.child,
    super.key,
  });
  final Widget child;

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider<AuthenticationBloc>(
          create: (context) => getIt<AuthenticationBloc>(),
        ),
        BlocProvider(
          create: (context) => getIt<RefreshCubit>(),
        ),
      ],
      child: child,
    );
  }
}

class CustomScrollBehavior extends MaterialScrollBehavior {
  @override
  Set<PointerDeviceKind> get dragDevices => {
        PointerDeviceKind.touch,
        PointerDeviceKind.mouse,
      };
}
