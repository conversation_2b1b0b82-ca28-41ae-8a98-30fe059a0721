part of 'authentication_bloc.dart';

sealed class AuthenticationState extends Equatable {
  const AuthenticationState();

  @override
  List<Object> get props => [];

  R? mapOrNull<R extends Object?>({
    R Function(Authenticated state)? authenticated,
    R Function(Unauthenticated state)? unauthenticated,
    R Function(Unknown state)? unknown,
  }) =>
      switch (this) {
        final Authenticated authenticatedState => authenticated?.call(authenticatedState),
        final Unauthenticated unauthenticatedState => unauthenticated?.call(unauthenticatedState),
        final Unknown unknownState => unknown?.call(unknownState),
      };
}

final class Unknown extends AuthenticationState {}

final class Unauthenticated extends AuthenticationState {}

final class Authenticated extends AuthenticationState {
  const Authenticated({required this.user});
  final UserModel user;
  @override
  List<Object> get props => [user];
}
