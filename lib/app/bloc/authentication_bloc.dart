import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:excel_app/injector/injector.dart';
import 'package:excel_app/local_storage/i_local_storage_repository.dart';
import 'package:excel_app/users/model/user_model.dart';
import 'package:excel_app/users/repository/i_user_repository.dart';
import 'package:injectable/injectable.dart';

part 'authentication_event.dart';
part 'authentication_state.dart';

@lazySingleton
class AuthenticationBloc extends Bloc<AuthenticationEvent, AuthenticationState> {
  AuthenticationBloc() : super(Unknown()) {
    on<Check>(_check);
    on<Logout>(_logout);
    on<UpdateUser>(_updateUser);
  }

  Future<void> _check(Check event, Emitter<AuthenticationState> emit) async {
    final token = getIt<ILocalStorageRepository>().token;
    if (token == null) {
      emit(Unauthenticated());
      return;
    }
    if (event.user != null) {
      emit(Authenticated(user: event.user!));
      return;
    }

    final userOrFail = await getIt<IUserRepository>().detail();

    userOrFail.fold(
      (l) {
        emit(Unauthenticated());
      },
      (r) {
        if (r.user == null) {
          emit(Unauthenticated());
          return;
        }
        emit(Authenticated(user: r.user!));
      },
    );
  }

  void _logout(Logout event, Emitter<AuthenticationState> emit) {
    getIt<ILocalStorageRepository>().clearAuth();
    emit(Unauthenticated());
  }

  void _updateUser(UpdateUser event, Emitter<AuthenticationState> emit) {
    final authenticatedState = state is Authenticated;

    if (authenticatedState) {
      emit(Authenticated(user: event.user));
    }
  }
}
