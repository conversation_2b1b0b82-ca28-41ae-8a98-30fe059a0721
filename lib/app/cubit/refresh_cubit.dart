import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:excel_app/holiday_category/model/holiday_catergory_model.dart';
import 'package:excel_app/projects/model/project_model.dart';
import 'package:excel_app/users/model/user_model.dart';
import 'package:excel_app/utility/enums/enum_file.dart';
import 'package:injectable/injectable.dart';

part 'refresh_state.dart';

@injectable
class RefreshCubit extends Cubit<RefreshState> {
  RefreshCubit() : super(UserRefreshInitial());

  void modifyUser(UserModel? user, UserAction action) {
    emit(ModifyUser(user: user ?? const UserModel(), action: action));
  }

  void modifyProject(ProjectModel? project, ProjectAction action) {
    emit(ModifyProject(project: project ?? const ProjectModel(), action: action));
  }

  void modifyHolidayCategory(HolidayCategoryModel? category, HolidayCategoryAction action) {
    emit(ModifyHolidayCategory(category: category ?? const HolidayCategoryModel(), action: action));
  }
}
