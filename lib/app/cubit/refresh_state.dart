part of 'refresh_cubit.dart';

sealed class RefreshState extends Equatable {
  const RefreshState();

  @override
  List<Object> get props => [];
}

final class UserRefreshInitial extends RefreshState {}

final class ModifyUser extends RefreshState {
  const ModifyUser({required this.user, required this.action});

  final UserModel user;
  final UserAction action;

  @override
  List<Object> get props => [user, action];
}

final class ModifyProject extends RefreshState {
  const ModifyProject({required this.project, required this.action});

  final ProjectModel project;
  final ProjectAction action;

  @override
  List<Object> get props => [project, action];
}

final class ModifyHolidayCategory extends RefreshState {
  const ModifyHolidayCategory({required this.category, required this.action});

  final HolidayCategoryModel category;
  final HolidayCategoryAction action;

  @override
  List<Object> get props => [category, action];
}
