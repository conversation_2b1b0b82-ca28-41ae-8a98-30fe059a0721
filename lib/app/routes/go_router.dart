import 'dart:async';

import 'package:excel_app/app/bloc/authentication_bloc.dart';
import 'package:excel_app/app/routes/app_route.dart';
import 'package:excel_app/auth/view/splash_page.dart';
import 'package:excel_app/chat/view/chat_view.dart';
import 'package:excel_app/dashboard/view/dashboard_page.dart';
import 'package:excel_app/holiday_category/view/holiday_categorie_page.dart';
import 'package:excel_app/home/<USER>/home_page.dart';
import 'package:excel_app/injector/injector.dart';
import 'package:excel_app/leader/add_availability/view/add_availability_wrapper_page.dart';
import 'package:excel_app/login/views/login_page.dart';
import 'package:excel_app/master/master_page.dart';
import 'package:excel_app/profile/view/profile_page_wrapper.dart';
import 'package:excel_app/projects/view/add_project_page.dart';
import 'package:excel_app/projects/view/project_page_wrapper.dart';
import 'package:excel_app/users/view/add_user_page.dart';
import 'package:excel_app/users/view/users_page.dart';
import 'package:excel_app/widget/common_button.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

class GoRouterRefreshStream extends ChangeNotifier {
  GoRouterRefreshStream(Stream<dynamic> stream) {
    _subscription = stream.listen(
      (dynamic _) => notifyListeners(),
    );
  }

  late final StreamSubscription<dynamic> _subscription;

  @override
  void dispose() {
    _subscription.cancel();
    super.dispose();
  }
}

final _rootNavigatorKey = GlobalKey<NavigatorState>();

final _shellNavigatorDashboardKey = GlobalKey<NavigatorState>(debugLabel: 'dashboard');
final _shellNavigatorHomeKey = GlobalKey<NavigatorState>(debugLabel: 'home');
final _shellNavigatorProjectsKey = GlobalKey<NavigatorState>(debugLabel: 'projects');
final _shellNavigatorUsersKey = GlobalKey<NavigatorState>(debugLabel: 'users');

final _shellNavigatorHolidayCategories = GlobalKey<NavigatorState>(debugLabel: 'holiday-categories');
final _shellNavigatorProfileKey = GlobalKey<NavigatorState>(debugLabel: 'profile');
final _shellNavigatorChatKey = GlobalKey<NavigatorState>(debugLabel: 'chat');

final goRouter = GoRouter(
  debugLogDiagnostics: kDebugMode,
  navigatorKey: _rootNavigatorKey,
  initialLocation: AppRoutes.splash.route,
  refreshListenable: GoRouterRefreshStream(
    (getIt<AuthenticationBloc>()..add(const Check())).stream,
  ),
  redirect: (context, state) {
    final authState = getIt<AuthenticationBloc>().state;
    final isLoginRoutes = [
      AppRoutes.login.route,
      AppRoutes.splash.route,
    ].contains(state.uri.toString());

    if (authState is Authenticated && isLoginRoutes) {
      return AppRoutes.home.route;
    }

    if (authState is Unauthenticated) {
      return AppRoutes.login.route;
    }
    return null;
  },
  errorBuilder: (context, state) {
    return Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Text(
              'Page Not Found',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 20),
            const Text('The page you are looking for does not exist.'),
            const SizedBox(height: 30),
            CommonButton(
              maximumSize: const Size(100, 50),
              text: 'Home',
              onTap: () {
                context.go(AppRoutes.home.route); // Go to users page or your home page
              },
            ),
          ],
        ),
      ),
    );
  },
  routes: [
    GoRoute(
      path: AppRoutes.splash.route,
      name: AppRoutes.splash.name,
      pageBuilder: (c, s) => const NoTransitionPage(child: SplashPage()),
    ),
    GoRoute(
      path: AppRoutes.login.route,
      name: AppRoutes.login.name,
      pageBuilder: (c, s) => const NoTransitionPage(child: LoginPage()),
    ),
    StatefulShellRoute.indexedStack(
      branches: [
        StatefulShellBranch(
          navigatorKey: _shellNavigatorDashboardKey,
          routes: [
            GoRoute(
              path: AppRoutes.dashboard.route,
              name: AppRoutes.dashboard.name,
              builder: (c, s) => const DashboardPage(),
            ),
          ],
        ),
        StatefulShellBranch(
          navigatorKey: _shellNavigatorHomeKey,
          routes: [
            GoRoute(
              path: AppRoutes.home.route,
              name: AppRoutes.home.name,
              builder: (c, s) => const HomePage(),
            ),
          ],
        ),
        StatefulShellBranch(
          navigatorKey: _shellNavigatorProjectsKey,
          routes: [
            GoRoute(
              path: AppRoutes.projects.route,
              name: AppRoutes.projects.name,
              builder: (c, s) => const ProjectPageWrapper(),
              routes: [
                GoRoute(
                  path: AppRoutes.addProject.route,
                  name: AppRoutes.addProject.name,
                  builder: (c, s) => const AddProjectPage(),
                ),
                GoRoute(
                  path: AppRoutes.editProject.route,
                  name: AppRoutes.editProject.name,
                  builder: (c, s) {
                    return AddProjectPage(
                      projectId: s.pathParameters['projectId'],
                    );
                  },
                ),
                GoRoute(
                  path: AppRoutes.addAvailability.route,
                  name: AppRoutes.addAvailability.name,
                  builder: (c, s) => AddAvailabilityWrapperPage(
                    projectId: s.pathParameters['projectId'] ?? '',
                  ),
                ),
              ],
            ),
          ],
        ),
        StatefulShellBranch(
          navigatorKey: _shellNavigatorUsersKey,
          routes: [
            GoRoute(
              path: AppRoutes.users.route,
              name: AppRoutes.users.name,
              builder: (c, s) => const UsersPage(),
              routes: [
                GoRoute(
                  path: AppRoutes.addUser.route,
                  name: AppRoutes.addUser.name,
                  builder: (c, s) => const AddUserPage(),
                ),
                GoRoute(
                  path: AppRoutes.editUser.route,
                  name: AppRoutes.editUser.name,
                  builder: (c, s) => AddUserPage(
                    userId: s.pathParameters['userId'],
                  ),
                ),
              ],
            ),
          ],
        ),
        StatefulShellBranch(
          navigatorKey: _shellNavigatorHolidayCategories,
          routes: [
            GoRoute(
              path: AppRoutes.holidayCategories.route,
              name: AppRoutes.holidayCategories.name,
              builder: (c, s) => const HolidayCategoriepage(),
            ),
          ],
        ),
        StatefulShellBranch(
          navigatorKey: _shellNavigatorProfileKey,
          routes: [
            GoRoute(
              path: AppRoutes.profile.route,
              name: AppRoutes.profile.name,
              builder: (c, s) => const ProfilePageWrapper(),
            ),
          ],
        ),
        StatefulShellBranch(
          navigatorKey: _shellNavigatorChatKey,
          routes: [
            GoRoute(
              path: AppRoutes.chat.route,
              name: AppRoutes.chat.name,
              builder: (c, s) => const ChatView(),
            ),
          ],
        ),
      ],
      builder: (context, state, navigationShell) => MasterPage(navigationShell: navigationShell),
    ),
  ],
);
