class _AppRoute {
  const _AppRoute({
    required this.route,
    required this.name,
  });

  final String route;
  final String name;
}

class AppRoutes {
  static const splash = _AppRoute(route: '/splash', name: 'splash');
  static const login = _AppRoute(route: '/login', name: 'login');
  static const dashboard = _AppRoute(route: '/dashboard', name: 'dashboard');
  static const home = _AppRoute(route: '/home', name: 'home');
  static const projects = _AppRoute(route: '/projects', name: 'projects');
  static const users = _AppRoute(route: '/users', name: 'users');

  static const holidayCategories = _AppRoute(route: '/holiday-categories', name: 'holiday-categories');
  static const profile = _AppRoute(route: '/profile', name: 'profile');
  static const addUser = _AppRoute(route: '/add-user', name: 'add-user');
  static const editUser = _AppRoute(route: '/:userId/edit-user', name: 'edit-user');
  static const addProject = _AppRoute(route: '/add-project', name: 'add-project');
  static const editProject = _AppRoute(route: '/:projectId/edit-project', name: 'edit-project');
  static const chat = _AppRoute(route: '/chat', name: 'chat');

  static const addAvailability = _AppRoute(route: '/:projectId/add-availability', name: 'add-availability');
}
