import 'package:excel_app/constants/app_strings.dart';
import 'package:excel_app/utility/helpers/logger_config.dart';
import 'package:injectable/injectable.dart';
import 'package:shared_preferences/shared_preferences.dart';

part 'local_storage_repository.dart';

abstract class ILocalStorageRepository {
  ILocalStorageRepository(this.preferences);
  final SharedPreferences preferences;

  Future<bool> setToken(String? value);

  String? get token;

  Future<bool> clearAuth();
}
