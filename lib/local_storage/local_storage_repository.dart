part of 'i_local_storage_repository.dart';

@Injectable(as: ILocalStorageRepository)
class LocalStorageRepository extends ILocalStorageRepository {
  LocalStorageRepository(super.preferences);

  @override
  Future<bool> setToken(String? value) async {
    if (value == null) return false;
    try {
      await preferences.setString(AppStrings.tokenKey, value);
      return true;
    } on Exception catch (e, s) {
      debugError('Error Set Token: $e \n $s');
      return false;
    }
  }

  @override
  String? get token => preferences.getString(AppStrings.tokenKey);

  @override
  Future<bool> clearAuth() async {
    try {
      return preferences.remove(AppStrings.tokenKey);
    } on Exception catch (e, s) {
      debugError('Error Clear Auth: $e \n $s');
      return false;
    }
  }
}
