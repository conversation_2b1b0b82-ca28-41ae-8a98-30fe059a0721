import 'dart:html' as html;

import 'package:excel_app/app/bloc/authentication_bloc.dart';
import 'package:excel_app/auth/repository/i_auth_repository.dart';
import 'package:excel_app/constants/app_assets.dart';
import 'package:excel_app/constants/app_colors.dart';
import 'package:excel_app/injector/injector.dart';
import 'package:excel_app/utility/helpers/utility.dart';
import 'package:excel_app/widget/app_asset_image.dart';
import 'package:excel_app/widget/custome_button_gradiant_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';

class ConnectCalenderDialog extends StatefulWidget {
  const ConnectCalenderDialog({super.key, this.connectedMail});
  final String? connectedMail;

  @override
  State<ConnectCalenderDialog> createState() => _ConnectCalenderDialogState();
}

class _ConnectCalenderDialogState extends State<ConnectCalenderDialog> {
  final isLoading = ValueNotifier<bool>(false);

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20),
      ),
      child: SizedBox(
        width: 390,
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 30, horizontal: 50),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  AppAssetImage(
                    AppAssets.logo,
                    height: 55,
                    width: 55,
                  ),
                  Gap(25),
                  AppAssetImage(AppAssets.reverseIcon),
                  Gap(25),
                  AppAssetImage(AppAssets.googleCalIcon),
                ],
              ),
              const Gap(24),
              Text(
                widget.connectedMail != null ? 'Google Calendar Connected' : 'Connect Google Calendar',
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                    ),
              ),
              const Gap(6),
              if (widget.connectedMail != null)
                RichText(
                  text: TextSpan(
                    children: [
                      TextSpan(
                        text: 'with ',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(color: AppColors.subText),
                      ),
                      TextSpan(
                        text: widget.connectedMail,
                        style: Theme.of(context).textTheme.bodyMedium,
                      ),
                    ],
                  ),
                )
              else
                Text(
                  'All your shift schedule will be added in your google calendar.',
                  textAlign: TextAlign.center,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(color: AppColors.subText),
                ),
              const Gap(30),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CustomeButtonGradiantWidget(
                    buttonText: 'Cancel',
                    isUseContainerBorder: true,
                    width: 100,
                    height: 38,
                    onTap: () {
                      Navigator.pop(context);
                    },
                  ),
                  const Gap(15),
                  ValueListenableBuilder(
                    valueListenable: isLoading,
                    builder: (__, loading, _) {
                      return CustomeButtonGradiantWidget(
                        buttonText: widget.connectedMail != null ? 'Disconnect' : 'Connect',
                        isGradient: true,
                        width: 100,
                        height: 38,
                        isLoading: loading,
                        onTap: () {
                          if (widget.connectedMail != null) {
                            disconnectGoogleCalendarApi();
                          } else {
                            connectGoogleCalendarApi();
                          }
                        },
                      );
                    },
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> connectGoogleCalendarApi() async {
    isLoading.value = true;
    final response = await getIt<IAuthRepository>().connectGoogleCalendar();
    await response.fold(
      (l) {
        isLoading.value = false;
        Navigator.pop(context);
        Utility.toast(message: l.message);
      },
      (r) async {
        Navigator.pop(context);
        isLoading.value = false;
        try {
          html.window.location.href = '${r.data}';
        } catch (e) {
          // ignore: only_throw_errors
          throw e.toString();
        }
      },
    );
  }

  Future<void> disconnectGoogleCalendarApi() async {
    isLoading.value = true;
    final response = await getIt<IAuthRepository>().disconnectGoogleCalendar();
    await response.fold(
      (l) {
        isLoading.value = false;
        Navigator.pop(context);
        Utility.toast(message: l.message);
      },
      (r) async {
        Navigator.pop(context);
        isLoading.value = false;
        context.read<AuthenticationBloc>().add(Check(user: r.user));
      },
    );
  }
}
