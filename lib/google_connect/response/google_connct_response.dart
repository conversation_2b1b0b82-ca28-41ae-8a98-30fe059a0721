import 'package:equatable/equatable.dart';

class GoogleConnectResponse extends Equatable {
  const GoogleConnectResponse({
    this.data,
    this.status,
    this.message,
  });

  factory GoogleConnectResponse.fromJson(Map<String, dynamic> json) {
    return GoogleConnectResponse(
      data: json['data'] as String?,
      status: json['status'] as String?,
      message: json['message'] as String?,
    );
  }

  final String? data;
  final String? status;
  final String? message;

  Map<String, dynamic> toJson() => {
        'data': data,
        'status': status,
        'message': message,
      };

  @override
  List<Object?> get props => [
        data,
        status,
        message,
      ];
}
