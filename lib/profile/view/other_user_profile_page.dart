import 'package:excel_app/app/bloc/authentication_bloc.dart';
import 'package:excel_app/constants/app_colors.dart';
import 'package:excel_app/injector/injector.dart';
import 'package:excel_app/l10n/l10n_extension.dart';
import 'package:excel_app/profile/widget/change_password_dialog.dart';
import 'package:excel_app/users/repository/i_user_repository.dart';
import 'package:excel_app/users/response/login_response.dart';
import 'package:excel_app/utility/helpers/utility.dart';
import 'package:excel_app/utility/model/app_file.dart';
import 'package:excel_app/widget/app_text_form_field.dart';
import 'package:excel_app/widget/container_widget.dart';
import 'package:excel_app/widget/custom_network_image.dart';
import 'package:excel_app/widget/custome_button_gradiant_widget.dart';
import 'package:excel_app/widget/dialogs.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:image_picker/image_picker.dart';

class OtherUserProfilePage extends StatefulWidget {
  const OtherUserProfilePage({
    super.key,
  });

  @override
  State<OtherUserProfilePage> createState() => _OtherUserProfilePageState();
}

class _OtherUserProfilePageState extends State<OtherUserProfilePage> {
  final nameController = TextEditingController();
  final userTypeController = TextEditingController();
  final emailController = TextEditingController();
  final passwordController = TextEditingController();
  final annualVacationController = TextEditingController();
  final passwordVisbility = ValueNotifier<bool>(true);
  final _formKey = GlobalKey<FormState>();
  final pickedImageFile = ValueNotifier<AppFile?>(null);
  final userImage = ValueNotifier<String?>(null);
  final scrollController = ScrollController();

  final isLoading = ValueNotifier<bool>(false);
  final isButtonLoading = ValueNotifier<bool>(false);

  LoginResponse? userData;
  int? userId;

  @override
  void dispose() {
    nameController.dispose();
    emailController.dispose();
    passwordController.dispose();
    passwordVisbility.dispose();
    _formKey.currentState?.dispose();
    pickedImageFile.dispose();
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
    getUserData();
  }

  Future<void> getUserData() async {
    final authState = context.read<AuthenticationBloc>().state;

    if (authState is Authenticated) {
      nameController.text = authState.user.name ?? '';
      emailController.text = authState.user.email ?? '';
      userTypeController.text = authState.user.userRoleViewString;
      userImage.value = authState.user.imagePath;
      userId = authState.user.id;
    }
    passwordController.text = 'password';
  }

  @override
  Widget build(BuildContext context) {
    return ContainerWidget(
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Profile',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
            ),
            const Gap(30),
            InkWell(
              onTap: () async {
                final pickedImage = await ImagePicker().pickImage(source: ImageSource.gallery);
                if (pickedImage != null) {
                  final fileBytes = await pickedImage.readAsBytes();
                  pickedImageFile.value = AppFile(
                    bytes: fileBytes,
                    name: pickedImage.name,
                    mimeType: pickedImage.mimeType ?? '',
                    path: pickedImage.path,
                  );
                  userImage.value = null;
                  // log('${pickedImageFile.value}pickedImageFile');
                }
              },
              child: ValueListenableBuilder(
                valueListenable: userImage,
                builder: (context, userImage, _) {
                  if (userImage != null) {
                    return Container(
                      clipBehavior: Clip.hardEdge,
                      height: 95,
                      width: 95,
                      decoration: const BoxDecoration(shape: BoxShape.circle),
                      child: CustomNetworkImage(
                        imageUrl: userImage,
                        height: 24,
                        width: 24,
                      ),
                    );
                  }
                  return ValueListenableBuilder<AppFile?>(
                    valueListenable: pickedImageFile,
                    builder: (context, file, _) {
                      if (file?.bytes != null) {
                        return Container(
                          clipBehavior: Clip.hardEdge,
                          height: 95,
                          width: 95,
                          decoration: const BoxDecoration(shape: BoxShape.circle),
                          child: Image.memory(
                            file!.bytes,
                            fit: BoxFit.cover,
                          ),
                        );
                      }
                      return Container(
                        height: 95,
                        width: 95,
                        decoration: const BoxDecoration(
                          shape: BoxShape.circle,
                          color: AppColors.inputFieldBg,
                        ),
                        alignment: Alignment.center,
                        child: Text(
                          '+${context.l10n.add}',
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                fontWeight: FontWeight.w500,
                                color: AppColors.primary,
                              ),
                        ),
                      );
                    },
                  );
                },
              ),
            ),
            const Gap(20),
            AppTextFormField(
              controller: nameController,
              title: context.l10n.name,
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return context.l10n.pleaseEnterName;
                }
                return null;
              },
            ),
            const Gap(20),
            AppTextFormField(
              controller: userTypeController,
              title: 'User Type',
              enabled: false,
            ),
            const Gap(20),
            AppTextFormField(
              controller: emailController,
              title: context.l10n.email,
              readOnly: true,
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return context.l10n.pleaseEnterEmail;
                } else if (!Utility.isValidEmail(value.trim())) {
                  return context.l10n.pleaseEnterValidEmail;
                }
                return null;
              },
            ),
            const Gap(20),
            ConstrainedBox(
              constraints: const BoxConstraints(maxWidth: 400),
              child: Stack(
                children: [
                  AppTextFormField(
                    controller: passwordController,
                    title: 'Password',
                    textInputAction: TextInputAction.done,
                    obscureText: true,
                    obscuringCharacter: '*',
                    enabled: false,
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'Please enter password';
                      }
                      return null;
                    },
                  ),
                  Positioned(
                    right: 0,
                    top: 22,
                    child: InkWell(
                      onTap: () {
                        DailogBox.showBluredBgDailog(context, const ChangePasswordDailog());
                      },
                      child: Padding(
                        padding: const EdgeInsets.all(12),
                        child: Text(
                          'Change',
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(color: AppColors.primary),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            const Gap(20),
            Row(
              children: [
                CustomeButtonGradiantWidget(
                  onTap: getUserData,
                  height: 38,
                  width: 100,
                  isUseContainerBorder: true,
                  buttonText: 'Cancel',
                ),
                const Gap(15),
                ValueListenableBuilder<bool>(
                  valueListenable: isButtonLoading,
                  builder: (context, loading, _) {
                    return CustomeButtonGradiantWidget(
                      onTap: editUser,
                      height: 38,
                      width: 100,
                      isGradient: true,
                      isLoading: loading,
                      buttonText: 'Save',
                    );
                  },
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Future<void> editUser() async {
    if (_formKey.currentState!.validate()) {
      isButtonLoading.value = true;
      final response = await getIt<IUserRepository>().editUser(
        userId: userId?.toString(),
        name: nameController.text,
        image: pickedImageFile.value,
      );
      await response.fold(
        (l) {
          isButtonLoading.value = false;
          Utility.toast(message: l.message);
        },
        (r) async {
          isButtonLoading.value = false;
          if (r.data != null) {
            // context.read<RefreshCubit>().modifyUser(r.data, UserAction.edit);
          }
          Utility.toast(message: r.message);
          context.pop();
        },
      );
    }
  }
}
