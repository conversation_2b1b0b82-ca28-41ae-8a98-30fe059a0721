import 'package:excel_app/constants/app_assets.dart';
import 'package:excel_app/injector/injector.dart';
import 'package:excel_app/users/repository/i_user_repository.dart';
import 'package:excel_app/utility/helpers/utility.dart';
import 'package:excel_app/widget/app_asset_image.dart';
import 'package:excel_app/widget/app_text_form_field.dart';
import 'package:excel_app/widget/custome_button_gradiant_widget.dart';
import 'package:excel_app/widget/popup_wrapper.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';

class ChangePasswordDailog extends StatefulWidget {
  const ChangePasswordDailog({super.key});

  @override
  State<ChangePasswordDailog> createState() => ChangePasswordDailogState();
}

class ChangePasswordDailogState extends State<ChangePasswordDailog> {
  final oldPasswordController = TextEditingController();
  final newPasswordController = TextEditingController();
  final confirmNewPasswordController = TextEditingController();
  final obscureOldPassword = ValueNotifier<bool>(true);
  final obscureNewPassword = ValueNotifier<bool>(true);
  final obscureConfirmNewPassword = ValueNotifier<bool>(true);
  final isLoading = ValueNotifier<bool>(false);
  final _formKey = GlobalKey<FormState>();
  @override
  Widget build(BuildContext context) {
    return Form(
      key: _formKey,
      child: PopUpWrapper(
        showCancelButton: false,
        childrenPadding: const EdgeInsets.all(20),
        children: [
          Text(
            'Change Password',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(fontWeight: FontWeight.w600),
          ),
          const Gap(12),
          ValueListenableBuilder<bool>(
            valueListenable: obscureOldPassword,
            builder: (context, obscure, _) {
              return AppTextFormField(
                title: 'Old Password',
                controller: oldPasswordController,
                obscureText: obscure,
                suffixIcon: IconButton(
                  onPressed: () {
                    obscureOldPassword.value = !obscure;
                  },
                  icon: AppAssetImage(
                    obscure ? AppAssets.eyeOpenIcon : AppAssets.eyeCloseIcon,
                  ),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Please enter old password';
                  } else if (value.trim().length < 6) {
                    return 'Password must be at least 6 characters';
                  }
                  return null;
                },
              );
            },
          ),
          const Gap(20),
          ValueListenableBuilder<bool>(
            valueListenable: obscureNewPassword,
            builder: (context, obscure, _) {
              return AppTextFormField(
                title: 'New Password',
                controller: newPasswordController,
                obscureText: obscure,
                suffixIcon: IconButton(
                  onPressed: () {
                    obscureNewPassword.value = !obscure;
                  },
                  icon: AppAssetImage(
                    obscure ? AppAssets.eyeOpenIcon : AppAssets.eyeCloseIcon,
                  ),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Please enter new password';
                  } else if (value.trim().length < 6) {
                    return 'Password must be at least 6 characters';
                  }
                  return null;
                },
              );
            },
          ),
          const Gap(20),
          ValueListenableBuilder<bool>(
            valueListenable: obscureConfirmNewPassword,
            builder: (context, obscure, _) {
              return AppTextFormField(
                title: 'Confirm New Password',
                controller: confirmNewPasswordController,
                obscureText: obscure,
                suffixIcon: IconButton(
                  onPressed: () {
                    obscureConfirmNewPassword.value = !obscure;
                  },
                  icon: AppAssetImage(
                    obscure ? AppAssets.eyeOpenIcon : AppAssets.eyeCloseIcon,
                  ),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Please enter confirm password';
                  } else if (value.trim() != newPasswordController.text.trim()) {
                    return 'Password does not match';
                  }
                  return null;
                },
              );
            },
          ),
          const Gap(30),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CustomeButtonGradiantWidget(
                buttonText: 'Cancel',
                isUseContainerBorder: true,
                width: 100,
                height: 38,
                onTap: () {
                  Navigator.pop(context);
                },
              ),
              const Gap(15),
              ValueListenableBuilder<bool>(
                valueListenable: isLoading,
                builder: (context, loading, _) {
                  return CustomeButtonGradiantWidget(
                    buttonText: 'Change',
                    isGradient: true,
                    isLoading: loading,
                    width: 116,
                    height: 38,
                    onTap: () {
                      if (_formKey.currentState!.validate()) {
                        changePassword();
                      }
                    },
                  );
                },
              ),
            ],
          ),
        ],
      ),
    );
  }

  Future<void> changePassword() async {
    isLoading.value = true;

    final failOrSucess = await getIt<IUserRepository>().changePassword(
      oldPassword: oldPasswordController.text.trim(),
      newPassword: newPasswordController.text.trim(),
    );
    failOrSucess.fold(
      (l) {
        isLoading.value = false;
        Utility.toast(message: l.message);
      },
      (r) {
        Navigator.pop(context);
        isLoading.value = false;
        Utility.toast(message: r.message);
      },
    );
  }
}
