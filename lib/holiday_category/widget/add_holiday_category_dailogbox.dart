import 'package:excel_app/constants/app_colors.dart';
import 'package:excel_app/l10n/l10n_extension.dart';
import 'package:excel_app/widget/app_text_form_field.dart';
import 'package:excel_app/widget/custome_button_gradiant_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:gap/gap.dart';

class AddHolidayCategoryDialogBox extends StatelessWidget {
  const AddHolidayCategoryDialogBox({
    required this.nameController,
    required this.onAdd,
    required this.onCancel,
    required this.changeColor,
    this.color,
    super.key,
    this.loading = false,
    this.isEdit = false,
  });
  final TextEditingController nameController;
  final VoidCallback onAdd;
  final VoidCallback onCancel;
  final VoidCallback changeColor;
  final Color? color;
  final bool loading;
  final bool isEdit;

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20),
      ),
      child: SizedBox(
        width: 390,
        child: Padding(
          padding: const EdgeInsets.fromLTRB(24, 24, 24, 20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Center(
                child: Text(
                  isEdit ? 'Edit Holiday Category' : 'Add New Holiday Category',
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                        fontSize: 20,
                        fontWeight: FontWeight.w600,
                      ),
                ),
              ),
              const Gap(16),
              AppTextFormField(
                controller: nameController,
                title: '${context.l10n.name}*',
                inputFormatters: [
                  LengthLimitingTextInputFormatter(100),
                ],
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return context.l10n.pleaseEnterName;
                  }
                  return null;
                },
              ),
              const Gap(16),
              Text(
                'Color',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      fontSize: 12,
                      fontWeight: FontWeight.w400,
                      color: AppColors.subText,
                    ),
              ),
              const Gap(6),
              GestureDetector(
                onTap: changeColor,
                child: Container(
                  height: 35,
                  decoration: BoxDecoration(
                    color: color,
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
              const Gap(24),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CustomeButtonGradiantWidget(
                    buttonText: 'Cancel',
                    isUseContainerBorder: true,
                    width: 120,
                    height: 45,
                    onTap: onCancel,
                  ),
                  const Gap(16),
                  CustomeButtonGradiantWidget(
                    buttonText: isEdit ? 'Edit' : 'Add',
                    isGradient: true,
                    width: 120,
                    height: 45,
                    isLoading: loading,
                    onTap: onAdd,
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
