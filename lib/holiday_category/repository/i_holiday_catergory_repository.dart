import 'package:excel_app/constants/app_strings.dart';
import 'package:excel_app/holiday_category/response/holiday_category_detail_response.dart';
import 'package:excel_app/holiday_category/response/holiday_category_list_response.dart';
import 'package:excel_app/utility/extentions/fpdart_extentions.dart';
import 'package:excel_app/utility/network/client.dart';
import 'package:injectable/injectable.dart';

part 'holiday_catergory_repository.dart';

abstract class IHolidayCategoryRepository {
  IHolidayCategoryRepository(this.client);
  final Client client;

  ApiResult<HolidayCategoryListResponse> getHolidayCategory({
    int? page,
    int? perPage,
    String? orderBy,
    String? orderDirection,
    String? search,
    int? isActive,
  });

  ApiResult<HolidayCategoryResponse> createHolidayCategory({
    required String name,
    required String colorHex,
  });

  ApiResult<HolidayCategoryResponse> updateHolidayCategory({
    required String id,
    String? name,
    String? colorHex,
    int? holidayCategoryActive,
  });
}
