part of 'i_holiday_catergory_repository.dart';

@Injectable(as: IHolidayCategoryRepository)
class HolidayCatergoryRepository extends IHolidayCategoryRepository {
  HolidayCatergoryRepository(super.client);

  @override
  ApiResult<HolidayCategoryListResponse> getHolidayCategory({
    int? page,
    int? perPage,
    String? orderBy,
    String? orderDirection,
    String? search,
    int? isActive,
  }) async {
    final response = await client.get(
      url: AppStrings.holidayCategories,
      params: {
        if (page != null) 'page': page.toString(),
        if (perPage != null) 'per_page': perPage.toString(),
        if (orderBy != null) 'orderBy': orderBy,
        if (orderDirection != null) 'orderDirection': orderDirection,
        if (search != null && search.trim().isNotEmpty) 'search': search,
        if (isActive != null) 'is_active': isActive.toString(),
      },
    );

    return response.parseResponse(HolidayCategoryListResponse.fromJson);
  }

  @override
  ApiResult<HolidayCategoryResponse> createHolidayCategory({
    required String name,
    required String colorHex,
  }) async {
    final response = await client.post(
      url: AppStrings.createHolidayCategory,
      requests: {
        'name': name.trim(),
        'color_hex': colorHex.trim(),
      },
    );
    return response.parseResponse(HolidayCategoryResponse.fromJson);
  }

  @override
  ApiResult<HolidayCategoryResponse> updateHolidayCategory({
    required String id,
    String? name,
    String? colorHex,
    int? holidayCategoryActive,
  }) async {
    final response = await client.post(
      url: AppStrings.updateHolidayCategory(id),
      requests: {
        if (name != null) 'name': name.trim(),
        if (colorHex != null) 'color_hex': colorHex.trim(),
        if (holidayCategoryActive != null) 'is_active': holidayCategoryActive.toString(),
      },
    );
    return response.parseResponse(HolidayCategoryResponse.fromJson);
  }
}
