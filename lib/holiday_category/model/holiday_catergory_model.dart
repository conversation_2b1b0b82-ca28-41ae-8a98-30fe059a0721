// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:equatable/equatable.dart';
import 'package:excel_app/constants/app_colors.dart';
import 'package:excel_app/utility/extentions/string_extentions.dart';
import 'package:flutter/material.dart';

class HolidayCategoryModel extends Equatable {
  const HolidayCategoryModel({
    this.isActive,
    this.controller,
    this.id,
    this.name,
    this.colorHex,
    this.createdAt,
    this.updatedAt,
  });

  factory HolidayCategoryModel.fromJson(Map<String, dynamic> json) {
    return HolidayCategoryModel(
      id: json['id'] as int?,
      isActive: json['is_active'] as int?,
      name: json['name'] as String?,
      colorHex: json['color_hex'] as String?,
      createdAt: json['created_at'] as String?,
      updatedAt: json['updated_at'] as String?,
      controller: TextEditingController(),
    );
  }

  final int? id;
  final int? isActive;
  final String? name;
  final String? colorHex;
  final String? createdAt;
  final String? updatedAt;
  final TextEditingController? controller;

  Map<String, dynamic> toJson() => {
        'id': id,
        'is_active': isActive,
        'name': name,
        'color_hex': colorHex,
        'created_at': createdAt,
        'updated_at': updatedAt,
        'controller': controller,
      };

  @override
  List<Object?> get props => [
        id,
        isActive,
        name,
        colorHex,
        createdAt,
        updatedAt,
        controller,
      ];

  Color get color => colorHex.isPureValid ? AppColors.hexToColor(colorHex!) : Colors.black;

  Color get getContrastingColor => color.computeLuminance() >= 0.5 ? Colors.black : Colors.white;

  HolidayCategoryModel copyWith({
    int? id,
    int? isActive,
    String? name,
    String? colorHex,
    String? createdAt,
    String? updatedAt,
    TextEditingController? controller,
  }) {
    return HolidayCategoryModel(
      id: id ?? this.id,
      isActive: isActive ?? this.isActive,
      name: name ?? this.name,
      colorHex: colorHex ?? this.colorHex,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      controller: controller ?? this.controller,
    );
  }

  @override
  bool get stringify => true;
}
