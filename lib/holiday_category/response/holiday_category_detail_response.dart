import 'package:equatable/equatable.dart';
import 'package:excel_app/holiday_category/model/holiday_catergory_model.dart';

class HolidayCategoryResponse extends Equatable {
  const HolidayCategoryResponse({
    this.message,
    this.status,
    this.currentPage,
    this.data,
  });

  factory HolidayCategoryResponse.fromJson(Map<String, dynamic> json) {
    return HolidayCategoryResponse(
      message: json['message'] as String?,
      status: json['status'] as String?,
      currentPage: json['current_page'] as int?,
      data: json['data'] == null ? null : HolidayCategoryModel.fromJson(json['data'] as Map<String, dynamic>),
    );
  }

  final String? message;
  final String? status;
  final int? currentPage;
  final HolidayCategoryModel? data;

  Map<String, dynamic> toJson() => {
        'message': message,
        'status': status,
        'current_page': currentPage,
        'data': data?.toJson(),
      };

  @override
  List<Object?> get props => [
        message,
        status,
        currentPage,
        data,
      ];
}
