import 'package:equatable/equatable.dart';
import 'package:excel_app/holiday_category/model/holiday_catergory_model.dart';

class HolidayCategoryListResponse extends Equatable {
  const HolidayCategoryListResponse({
    this.message,
    this.status,
    this.currentPage,
    this.data = const [],
  });

  factory HolidayCategoryListResponse.fromJson(Map<String, dynamic> json) {
    return HolidayCategoryListResponse(
      message: json['message'] as String?,
      status: json['status'] as String?,
      currentPage: json['current_page'] as int?,
      data: json['data'] == null
          ? []
          : List<HolidayCategoryModel>.from(
              (json['data'] as List<dynamic>).map(
                (e) => HolidayCategoryModel.fromJson(e as Map<String, dynamic>),
              ),
            ),
    );
  }

  final String? message;
  final String? status;
  final int? currentPage;
  final List<HolidayCategoryModel> data;

  Map<String, dynamic> toJson() => {
        'message': message,
        'status': status,
        'current_page': currentPage,
        'data': data.map((x) => x.toJson()).toList(),
      };

  @override
  List<Object?> get props => [
        message,
        status,
        currentPage,
        data,
      ];
}
