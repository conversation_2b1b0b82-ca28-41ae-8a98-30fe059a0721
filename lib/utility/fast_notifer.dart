class FastNotifier<T> {
  FastNotifier(this._value);

  T _value;

  final _listeners = <void Function(T)>{};

  T get value => _value;

  set value(T newValue) {
    if (_value == newValue) return; // Skip if value hasn't changed
    _value = newValue;
    _notifyListeners();
  }

  void addListener(void Function(T) listener) {
    _listeners.add(listener);
  }

  void removeListener(void Function(T) listener) {
    _listeners.remove(listener);
  }

  void _notifyListeners() {
    // Using toList() to allow listeners to modify the list during iteration
    for (final listener in _listeners.toList()) {
      listener(_value);
    }
  }

  void dispose() {
    _listeners.clear();
  }
}
