import 'dart:async';
import 'dart:typed_data';

import 'package:bot_toast/bot_toast.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:excel_app/constants/app_assets.dart';
import 'package:excel_app/constants/app_colors.dart';
import 'package:excel_app/constants/app_constants.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

class Utility {
  static Widget progressIndicator({Color? color, double? size}) {
    return Center(
      child: Image.asset(
        AppAssets.loadingGif,
        fit: BoxFit.fill,
        height: size ?? 50,
        width: size ?? 50,
        color: color ?? AppColors.primary,
      ),
    );
  }

  static Widget noDataWidget({required String text, required BuildContext context}) {
    return Text(
      text,
      style: Theme.of(context).textTheme.headlineSmall?.copyWith(color: AppColors.subText),
    );
  }

  static CancelFunc? _currentToastCancel;
  static Timer? _dismissTimer;

  static void toast({
    required String? message,
  }) {
    if (message == null || message.trim().isEmpty) return;

    // Cancel any existing toast
    _currentToastCancel?.call();
    _dismissTimer?.cancel();

    _currentToastCancel = BotToast.showWidget(
      toastBuilder: (cancelFunc) => Positioned(
        bottom: 50,
        left: 0,
        right: 0,
        child: Center(
          child: MouseRegion(
            onEnter: (_) {
              // Cancel the auto-dismiss timer when hovering
              _dismissTimer?.cancel();
            },
            onExit: (_) {
              // Start a new timer when mouse leaves
              _dismissTimer = Timer(const Duration(seconds: 3), () {
                cancelFunc();
                _currentToastCancel = null;
              });
            },
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              margin: const EdgeInsets.symmetric(horizontal: 20),
              decoration: BoxDecoration(
                color: AppColors.primary,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                message,
                style: const TextStyle(
                  color: AppColors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  decoration: TextDecoration.none,
                  fontFamily: 'SFProDisplay',
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ),
        ),
      ),
    );

    // Start initial timer
    _dismissTimer = Timer(const Duration(seconds: 3), () {
      _currentToastCancel?.call();
      _currentToastCancel = null;
    });
  }

  static bool isValidEmail(String email) {
    return RegExp(
      r'^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$',
    ).hasMatch(email);
  }

  static Widget imageLoader({
    required String url,
    required String placeholder,
    BoxFit? fit,
    BuildContext? context,
    bool isShapeCircular = false,
    BorderRadius? borderRadius,
    Widget? loadingWidget,
    BoxShape? shape,
  }) {
    if (url.trim() == '') {
      return Container(
        decoration: BoxDecoration(
          shape: shape ?? BoxShape.rectangle,
          borderRadius: isShapeCircular ? null : borderRadius ?? BorderRadius.circular(10),
          image: DecorationImage(
            image: AssetImage(placeholder),
            fit: fit ?? BoxFit.cover,
          ),
        ),
      );
    }
    return CachedNetworkImage(
      imageUrl: url,
      fadeInDuration: const Duration(seconds: 5),
      imageBuilder: (context, imageProvider) {
        return Container(
          decoration: BoxDecoration(
            borderRadius: isShapeCircular ? null : borderRadius ?? BorderRadius.circular(10),
            // borderRadius: borderRadius ?? BorderRadius.circular(10),
            shape: shape ?? BoxShape.rectangle,
            image: DecorationImage(
              image: imageProvider,
              fit: fit ?? BoxFit.cover,
            ),
          ),
        );
      },
      errorWidget: (context, error, dynamic a) => Container(
        decoration: BoxDecoration(
          shape: shape ?? BoxShape.rectangle,
          borderRadius: isShapeCircular ? null : borderRadius ?? BorderRadius.circular(10),
          // borderRadius: borderRadius ??  BorderRadius.circular(10),
          image: DecorationImage(
            image: AssetImage(placeholder),
            fit: fit ?? BoxFit.cover,
          ),
        ),
      ),
      placeholder: (context, url) =>
          loadingWidget ??
          Container(
            decoration: BoxDecoration(borderRadius: BorderRadius.circular(10), color: const Color(0xffebebeb)),
          ),
    );
  }

  static Future<DateTime?> datePicker({
    required BuildContext context,
    TextInputType? textInputType,
    DateTime? initialDate,
    DateTime? firstDate,
    DateTime? lastdate,
  }) async {
    return showDatePicker(
      locale: const Locale('en', 'IN'),
      context: context,
      initialDate: initialDate ?? DateTime.now(),
      firstDate: firstDate ??
          DateTime.now().subtract(
            const Duration(days: 99999),
          ),
      lastDate: lastdate ?? DateTime.now().add(const Duration(days: 99999)),
      fieldLabelText: 'DD/MM/YYYY',
      keyboardType: textInputType,
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(colorScheme: const ColorScheme.light(primary: AppColors.primary)),
          child: child ?? Container(),
        );
      },
    );
  }

  String dateFormat({DateTime? date}) {
    if (date == null) return '';
    final formatter = DateFormat('dd/MM/yyyy').format(date);
    return formatter;
  }

  String dateYearFormat({DateTime? date}) {
    if (date == null) return '';
    final formatter = DateFormat('yyyy/MM/dd').format(date);
    return formatter;
  }

  String monthFormat({DateTime? date}) {
    if (date == null) return '';
    final formatter = DateFormat('MM/yyyy').format(date);
    return formatter;
  }

  static int calculateDaysBetweenMonths(DateTime startMonth, DateTime endMonth) {
    final startDate = DateTime(startMonth.year, startMonth.month);
    final endDate = DateTime(endMonth.year, endMonth.month + 1).subtract(const Duration(days: 1));
    return endDate.difference(startDate).inDays + 1;
  }

  static Future<Uint8List?> pickFile() async {
    final result = await FilePicker.platform.pickFiles(type: FileType.custom, allowedExtensions: ['xls', 'xlsx']);

    if (result != null && result.files.single.bytes != null) {
      return result.files.single.bytes;
    } else {
      return null;
    }
  }

  static Future<FilePickerResult?> pickExcelFile() async {
    final pickedFile = await FilePicker.platform.pickFiles(
      type: FileType.custom,
      allowedExtensions: ['xlsx', 'csv'], // Limit to Excel files
      withData: true, // Ensures byte data is available for web
    );
    if (pickedFile != null) {
      return pickedFile;
    } else {
      return null;
    }
  }

  String formatMonthYear(DateTime date) {
    const months = [
      'January',
      'February',
      'March',
      'April',
      'May',
      'June',
      'July',
      'August',
      'September',
      'October',
      'November',
      'December',
    ];
    return '${months[date.month - 1]} ${date.year}';
  }

  static Color getColor(String? title) {
    switch (title) {
      case AppConstants.holidays:
        return AppColors.periwinkle;
      case AppConstants.otherHolidays:
        return AppColors.creamBrulee;
      case AppConstants.localHolidays:
        return AppColors.paleLightGreen;
      case AppConstants.psmarCompensatory:
        return AppColors.pastelMagenta;
      case AppConstants.prohibitedDays:
        return AppColors.sweetPink;
      case AppConstants.accepted:
        return AppColors.deepSkyBlue;
      case AppConstants.pending:
        return AppColors.sunShade;
      case AppConstants.rejected:
        return AppColors.redOrange;
      case AppConstants.leaderAndUser:
        return AppColors.lightMint;
      case AppConstants.leaderOnly:
        return AppColors.paleLavender;
      case AppConstants.personalDays:
        return AppColors.lightWisteria;
      case AppConstants.conferences:
        return AppColors.chardonnay;
      case AppConstants.vactions:
        return AppColors.columbiaBlue;
      case AppConstants.otherShift:
        return AppColors.mediumSpringBud;
      case AppConstants.notAvailable:
        return AppColors.lightSalmonPink;
      default:
        return AppColors.white;
    }
  }

  static String userRoleViewString(String selectedType) {
    switch (selectedType) {
      case AppConstants.doctor1:
        return AppConstants.doctorOne;
      case AppConstants.doctor2:
        return AppConstants.doctorTwo;
      case AppConstants.firstYearResident:
        return AppConstants.firstYear;
      case AppConstants.secondYearResident:
        return AppConstants.secondYear;
      case AppConstants.thirdYearResident:
        return AppConstants.thirdYear;
      case AppConstants.lastYearResident:
        return AppConstants.lastYear;
      default:
        return AppConstants.student;
    }
  }

  static List<DateTime> generateWeekdayDates({
    required DateTime startDate,
    required DateTime endDate,
    required int weekday,
  }) {
    if (weekday < 1 || weekday > 7) {
      throw ArgumentError('Weekday must be between 1 (Monday) and 7 (Sunday)');
    }

    if (endDate.isBefore(startDate)) {
      throw ArgumentError('End date must be after start date');
    }
    final dates = <DateTime>[];
    var currentDate = startDate;

    // Find the first occurrence of the specified weekday
    while (currentDate.weekday != weekday) {
      currentDate = currentDate.add(const Duration(days: 1));
      if (currentDate.isAfter(endDate)) return [];
    }

    // Add all occurrences of the weekday until end date
    while (!currentDate.isAfter(endDate)) {
      dates.add(currentDate);
      currentDate = currentDate.add(const Duration(days: 7));
    }

    return dates;
  }
}
