import 'dart:io' as io;
import 'package:flutter/foundation.dart';

/// A utility class to detect platform and browser information
class PlatformDetector {
  /// Check if the app is running on web
  static bool get isWeb => kIsWeb;

  /// Check if the app is running on macOS
  static bool get isMacOS {
    if (kIsWeb) {
      // For web, we use JS interop in js_interop.dart
      return false;
    } else {
      return io.Platform.isMacOS;
    }
  }

  /// Check if the app is running on Windows
  static bool get isWindows {
    if (kIsWeb) return false;
    return io.Platform.isWindows;
  }

  /// Check if the app is running on Linux
  static bool get isLinux {
    if (kIsWeb) return false;
    return io.Platform.isLinux;
  }

  /// Check if the app is running on iOS
  static bool get isIOS {
    if (kIsWeb) return false;
    return io.Platform.isIOS;
  }

  /// Check if the app is running on Android
  static bool get isAndroid {
    if (kIsWeb) return false;
    return io.Platform.isAndroid;
  }

  /// Get a string representation of the current platform
  static String get platformName {
    if (kIsWeb) return 'Web';
    if (io.Platform.isAndroid) return 'Android';
    if (io.Platform.isIOS) return 'iOS';
    if (io.Platform.isMacOS) return 'macOS';
    if (io.Platform.isWindows) return 'Windows';
    if (io.Platform.isLinux) return 'Linux';
    if (io.Platform.isFuchsia) return 'Fuchsia';
    return 'Unknown';
  }
}
