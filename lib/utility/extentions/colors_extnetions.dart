import 'package:excel_app/constants/app_colors.dart';
import 'package:excel_app/constants/app_constants.dart';
import 'package:flutter/material.dart';

extension ColorsExtnetions on Color {
  Color getHolidayColor(String? holidayName) {
    switch (holidayName) {
      case AppConstants.holidays:
        return AppColors.periwinkle;
      case AppConstants.holiWeekAndChristmas:
        return AppColors.creamBrulee;
      case AppConstants.localHolidays:
        return AppColors.paleLightGreen;
      case AppConstants.psmarCompensatory:
        return AppColors.pastelMagenta;
      case AppConstants.prohibitedDays:
        return AppColors.sweetPink;
      default:
        return AppColors.white;
    }
  }

  Color withOpacity2(double opacity) {
    return withValues(alpha: opacity);
  }

  Color get getContrastingColor => computeLuminance() >= 0.5 ? AppColors.dark : Colors.white;
}
