import 'dart:developer';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:firebase_messaging/firebase_messaging.dart';

import 'js_interop.dart';
import 'platform_detector.dart';

/// A utility class to test notifications
class NotificationTester {
  /// Test notifications on the current platform
  static Future<void> testNotifications(BuildContext context) async {
    // Log platform information
    log('Testing notifications on platform: ${PlatformDetector.platformName}');
    log('Is Web: ${PlatformDetector.isWeb}');
    
    if (kIsWeb) {
      log('Is Safari: ${SafariNotificationsInterop.isSafari}');
      log('Is macOS: ${SafariNotificationsInterop.isMacOS}');
    }

    // Request permission
    final settings = await FirebaseMessaging.instance.requestPermission(
      alert: true,
      announcement: true,
      badge: true,
      carPlay: true,
      criticalAlert: true,
      provisional: false,
      sound: true,
    );

    log('Notification permission status: ${settings.authorizationStatus}');

    // Get FCM token
    final token = await FirebaseMessaging.instance.getToken();
    log('FCM Token: $token');

    // Show a test notification
    if (kIsWeb && SafariNotificationsInterop.isSafari && SafariNotificationsInterop.isMacOS) {
      // For Safari on Mac, use our custom implementation
      log('Showing test notification using Safari notifications');
      SafariNotificationsInterop.showSafariNotification(
        'Test Notification',
        'This is a test notification on Safari/Mac',
      );
    } else {
      // For other platforms, show a snackbar
      log('Showing test notification using SnackBar');
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Test notification would appear here on a real device'),
          duration: Duration(seconds: 5),
        ),
      );
    }
  }
}
