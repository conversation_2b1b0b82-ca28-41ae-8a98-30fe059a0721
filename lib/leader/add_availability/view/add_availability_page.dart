// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:developer';

import 'package:collection/collection.dart';
import 'package:excel_app/custom_calendar/cubit/custom_calendar_cubit.dart';
import 'package:excel_app/custom_calendar/models/custom_calender_config.dart';
import 'package:excel_app/custom_calendar/views/custom_calendar.dart';
import 'package:excel_app/l10n/l10n_extension.dart';
import 'package:excel_app/leader/add_availability/model/week_day_model.dart';
import 'package:excel_app/leader/add_availability/view/availability_list_view.dart';
import 'package:excel_app/leader/add_availability/widget/week_day_widget.dart';
import 'package:excel_app/projects/model/project_model.dart';
import 'package:excel_app/projects/model/project_rules_category_model.dart';
import 'package:excel_app/projects/model/project_rules_model.dart';
import 'package:excel_app/widget/app_drop_down_widget.dart';
import 'package:excel_app/widget/container_widget.dart';
import 'package:excel_app/widget/navigation_path_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';

class AddAvailabilityPage extends StatefulWidget {
  const AddAvailabilityPage({
    super.key,
    this.projectRuleModel,
    this.project,
  });
  final ProjectRuleModel? projectRuleModel;
  final ProjectModel? project;

  @override
  State<AddAvailabilityPage> createState() => _AddAvailabilityPageState();
}

class _AddAvailabilityPageState extends State<AddAvailabilityPage> {
  late final projectRuleCategoryList = widget.projectRuleModel?.projectRuleCategories ?? <ProjectRuleCategoryModel>[];

  final weekDays = <WeekDayModel>[
    const WeekDayModel(day: 'Mon', weekDay: 1),
    const WeekDayModel(day: 'Tue', weekDay: 2),
    const WeekDayModel(day: 'Wed', weekDay: 3),
    const WeekDayModel(day: 'Thu', weekDay: 4),
    const WeekDayModel(day: 'Fri', weekDay: 5),
    const WeekDayModel(day: 'Sat', weekDay: 6),
    const WeekDayModel(day: 'Sun', weekDay: 7),
  ];
  final selectedWeekDays = ValueNotifier<List<WeekDayModel>>([]);

  // Track the current holiday key to detect changes
  String? _currentHolidayKey;

  @override
  void initState() {
    super.initState();
    // Initialize selected weekdays based on existing data if needed
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _updateSelectedWeekDays();
    });
  }

  @override
  void dispose() {
    selectedWeekDays.dispose();
    super.dispose();
  }

  // Check if any dates for a specific weekday exist in the current holiday selection
  bool _hasWeekdayDates(int weekday) {
    final cubit = context.read<CustomCalendarCubit>();
    final state = cubit.state;
    final holidayKey = state.currentHolidayKey;
    final holiday = state.selectedDates.firstWhereOrNull((element) => element.holidayKey == holidayKey);

    if (holiday == null) return false;

    // Check if any date in the holiday has the specified weekday
    return holiday.dates.any((date) => date.weekday == weekday);
  }

  // Update the selectedWeekDays list based on the current state
  // This is only called during initialization or when the holiday key changes
  void _updateSelectedWeekDays() {
    // If we're processing an individual date selection, don't update weekday checkboxes
    if (context.read<CustomCalendarCubit>().isProcessingIndividualDateSelection) {
      return;
    }

    final updatedWeekDays = <WeekDayModel>[];

    // Check each weekday to see if it has any dates in the current selection
    for (final weekDay in weekDays) {
      if (_hasWeekdayDates(weekDay.weekDay ?? 0)) {
        updatedWeekDays.add(weekDay);
      }
    }

    // Update the UI
    selectedWeekDays.value = updatedWeekDays;
  }

  @override
  Widget build(BuildContext context) {
    log('${widget.project?.projectCalendarDates.length}length of add availability');
    return MultiBlocListener(
      listeners: [
        BlocListener<CustomCalendarCubit, CustomCalendarState>(
          listenWhen: (previous, current) {
            // ONLY listen for changes in the holiday key
            return previous.currentHolidayKey != current.currentHolidayKey;
          },
          listener: (context, state) {
            // Store the current holiday key to detect changes
            final oldHolidayKey = _currentHolidayKey;
            _currentHolidayKey = state.currentHolidayKey;

            if (oldHolidayKey != state.currentHolidayKey) {
              // Do NOT reset weekday selections when holiday key changes
              // Keep the weekday checkboxes selected as requested

              // We don't call _updateSelectedWeekDays() here to keep the current selection
            }
          },
        ),
        BlocListener<CustomCalendarCubit, CustomCalendarState>(
          listenWhen: (previous, current) {
            // Listen ONLY for changes in selected dates
            return previous.selectedDates != current.selectedDates;
          },
          listener: (context, state) {
            // Only update weekday checkboxes if we ARE processing an individual date selection
            // This ensures we only update when manually selecting/deselecting dates
            if (!context.read<CustomCalendarCubit>().isProcessingIndividualDateSelection) {
              return;
            }

            // Get the current selected weekdays
            final currentSelectedWeekdays = List<WeekDayModel>.from(selectedWeekDays.value);

            // For each selected weekday, check if it still has dates
            for (var i = currentSelectedWeekdays.length - 1; i >= 0; i--) {
              final weekDay = currentSelectedWeekdays[i];
              final hasWeekdayDates = _hasWeekdayDates(weekDay.weekDay ?? 0);

              // If this weekday no longer has any dates, remove it from the selection
              if (!hasWeekdayDates) {
                currentSelectedWeekdays.removeAt(i);
              }
            }

            // Update the UI with the modified weekday selection
            // This will only remove weekdays that no longer have dates, not add new ones
            selectedWeekDays.value = currentSelectedWeekdays;
          },
        ),
      ],
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          ContainerWidget(
            child: NavigationPathWidget(
              mainTitle: 'Project',
              firstTitle: 'Project',
              secondTitle: widget.project?.name ?? '',
              thirdTitle: 'Add Availability',
            ),
          ),
          Flexible(
            child: Row(
              children: [
                Expanded(
                  child: ContainerWidget(
                    margin: const EdgeInsets.only(left: 25, bottom: 25),
                    padding: const EdgeInsets.only(left: 20, right: 20, bottom: 20),
                    child: Column(
                      children: [
                        Padding(
                          padding: const EdgeInsets.symmetric(vertical: 20, horizontal: 5),
                          child: Column(
                            children: [
                              Row(
                                children: [
                                  Text('Add Holidays', style: Theme.of(context).textTheme.titleSmall),
                                  const Gap(20),
                                  BlocSelector<CustomCalendarCubit, CustomCalendarState, int?>(
                                    selector: (state) => int.tryParse(state.currentHolidayKey ?? '0'),
                                    builder: (context, selectedHoliday) {
                                      return AppDropDown<ProjectRuleCategoryModel>(
                                        maxWidth: 300,
                                        selectedValue: projectRuleCategoryList
                                            .firstWhereOrNull((element) => element.leaveCategoryId == selectedHoliday),
                                        hintText: 'Select',
                                        onSelect: (valueOfCategory) {
                                          context.read<CustomCalendarCubit>().setHolidayKey(
                                                holidayKey: valueOfCategory?.leaveCategoryId.toString() ?? '',
                                                color: valueOfCategory?.leaveCategory?.color ?? Colors.transparent,
                                              );
                                        },
                                        items: projectRuleCategoryList
                                            .map(
                                              (e) => DropdownMenuItem<ProjectRuleCategoryModel>(
                                                value: e,
                                                child: Text(
                                                  e.name ?? '',
                                                ),
                                              ),
                                            )
                                            .toList(),
                                        validator: (p0) {
                                          if (p0 == null) {
                                            return context.l10n.pleaseSelectType;
                                          }
                                          return null;
                                        },
                                      );
                                    },
                                  ),
                                ],
                              ),
                              const Gap(20),
                              Row(
                                children: [
                                  Text('Not Available:', style: Theme.of(context).textTheme.titleSmall),
                                  const Gap(20),
                                  ValueListenableBuilder<List<WeekDayModel>>(
                                    valueListenable: selectedWeekDays,
                                    builder: (context, days, _) {
                                      return SizedBox(
                                        height: 20,
                                        child: ListView.separated(
                                          separatorBuilder: (context, index) {
                                            return const Gap(12);
                                          },
                                          shrinkWrap: true,
                                          scrollDirection: Axis.horizontal,
                                          itemBuilder: (context, index) {
                                            return WeekDayWidget(
                                              title: weekDays[index].day,
                                              isSelected: days.contains(weekDays[index]),
                                              onTap: () {
                                                if (projectRuleCategoryList.isNotEmpty) {
                                                  if (days.contains(weekDays[index])) {
                                                    // Deselect weekday
                                                    final state = context.read<CustomCalendarCubit>().state;
                                                    final holidayKey = state.selectedDates.firstWhereOrNull(
                                                      (element) => element.holidayKey == state.currentHolidayKey,
                                                    );
                                                    if (holidayKey == null) return;

                                                    // Update UI first
                                                    selectedWeekDays.value = [...selectedWeekDays.value]
                                                      ..remove(weekDays[index]);

                                                    // Then update the calendar data
                                                    context
                                                        .read<CustomCalendarCubit>()
                                                        .removeNotAvaiableDate(weekDays[index].weekDay ?? 0);
                                                  } else {
                                                    // Select weekday
                                                    // Update UI first
                                                    selectedWeekDays.value = [
                                                      ...selectedWeekDays.value,
                                                      weekDays[index],
                                                    ];

                                                    // Then update the calendar data
                                                    context
                                                        .read<CustomCalendarCubit>()
                                                        .setNotAvaiableDate(weekDays[index].weekDay ?? 0);
                                                  }
                                                }
                                              },
                                            );
                                          },
                                          itemCount: weekDays.length,
                                        ),
                                      );
                                    },
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                        const Gap(12),
                        Flexible(
                          child: IgnorePointer(
                            ignoring: projectRuleCategoryList.isEmpty,
                            child: CustomCalender(
                              customCalendarCubit: context.read<CustomCalendarCubit>(),
                              projectData: widget.project,
                              config: CustomCalenderConfig(
                                startDate: widget.project?.startDate ?? DateTime.now(),
                                endDate: widget.project?.endDate ?? DateTime.now(),
                                firstDayOfWeek: widget.project?.weekdayOfFirstOfWeek ?? 0,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                AvailabilityListView(projectRuleCategoryList: projectRuleCategoryList, project: widget.project),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
