import 'package:collection/collection.dart';
import 'package:excel_app/constants/app_colors.dart';
import 'package:excel_app/custom_calendar/cubit/custom_calendar_cubit.dart';
import 'package:excel_app/injector/injector.dart';
import 'package:excel_app/l10n/l10n_extension.dart';
import 'package:excel_app/leader/add_availability/widget/availability_listview_widget.dart';
import 'package:excel_app/projects/model/holiday_model.dart';
import 'package:excel_app/projects/model/project_model.dart';
import 'package:excel_app/projects/model/project_rules_category_model.dart';
import 'package:excel_app/projects/repository/i_project_repository.dart';
import 'package:excel_app/utility/helpers/utility.dart';
import 'package:excel_app/widget/custome_button_gradiant_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';

class AvailabilityListView extends StatefulWidget {
  const AvailabilityListView({
    super.key,
    this.projectRuleCategoryList = const <ProjectRuleCategoryModel>[],
    this.project,
  });
  final List<ProjectRuleCategoryModel> projectRuleCategoryList;
  final ProjectModel? project;

  @override
  State<AvailabilityListView> createState() => _AvailabilityListViewState();
}

class _AvailabilityListViewState extends State<AvailabilityListView> {
  final isLoading = ValueNotifier<bool>(false);

  @override
  void dispose() {
    isLoading.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      constraints: const BoxConstraints(maxWidth: 300),
      height: MediaQuery.sizeOf(context).height,
      padding: const EdgeInsets.all(20),
      margin: const EdgeInsets.only(left: 25, right: 25, bottom: 25),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        color: AppColors.white,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Unavailable Days',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                      fontSize: 18,
                    ),
              ),
              // const AvailabilityStatusView(
              //   text: AppConstants.rejected,
              //   color: AppColors.redOrange,
              // ),
            ],
          ),
          const Gap(24),
          Expanded(
            child: BlocSelector<CustomCalendarCubit, CustomCalendarState, List<HolidaysModel>>(
              selector: (state) => state.selectedDates,
              builder: (context, holidays) {
                return ListView.builder(
                  shrinkWrap: true,
                  itemCount: holidays.length,
                  itemBuilder: (context, index) {
                    final holiday = holidays[index];
                    return AvailabilityListViewWidget(
                      bgColor: holiday.bgColor ?? AppColors.white,
                      title: widget.projectRuleCategoryList
                              .firstWhereOrNull(
                                (element) => element.leaveCategoryId == int.tryParse(holiday.holidayKey ?? '0'),
                              )
                              ?.name ??
                          '',
                      dates: holiday.dates,
                      onDeleteTap: (value) {
                        context.read<CustomCalendarCubit>().removeSelectedDate(value);
                      },
                    );
                  },
                );
              },
            ),
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CustomeButtonGradiantWidget(
                width: 100,
                buttonText: context.l10n.back,
                isUseContainerBorder: true,
                onTap: () {
                  Navigator.pop(context);
                },
              ),
              const Gap(15),
              ValueListenableBuilder<bool>(
                valueListenable: isLoading,
                builder: (_, value, __) {
                  return CustomeButtonGradiantWidget(
                    isGradient: true,
                    width: 100,
                    isLoading: value,
                    buttonText: 'Submit',
                    onTap: onSubmit,
                  );
                },
              ),
            ],
          ),
        ],
      ),
    );
  }

  Future<void> onSubmit() async {
    isLoading.value = true;

    final failOrSuccess = await getIt<IProjectRepository>().updateProjectAvailability(
      projectId: widget.project?.id ?? 0,
      holidays: context.read<CustomCalendarCubit>().state.selectedDates,
    );

    failOrSuccess.fold(
      (l) {
        isLoading.value = false;
        Utility.toast(message: l.message);
      },
      (r) {
        isLoading.value = false;
        Utility.toast(message: r.message);

        Navigator.pop(context);
      },
    );
  }
}
