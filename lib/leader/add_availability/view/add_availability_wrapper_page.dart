import 'package:collection/collection.dart';
import 'package:excel_app/app/bloc/authentication_bloc.dart';
import 'package:excel_app/custom_calendar/cubit/custom_calendar_cubit.dart';
import 'package:excel_app/injector/injector.dart';
import 'package:excel_app/leader/add_availability/view/add_availability_page.dart';
import 'package:excel_app/projects/model/project_model.dart';
import 'package:excel_app/projects/model/project_rules_category_model.dart';
import 'package:excel_app/projects/repository/i_project_repository.dart';
import 'package:excel_app/users/model/user_model.dart';
import 'package:excel_app/utility/helpers/logger_config.dart';
import 'package:excel_app/utility/helpers/utility.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class AddAvailabilityWrapperPage extends StatefulWidget {
  const AddAvailabilityWrapperPage({required this.projectId, super.key});
  final String projectId;

  @override
  State<AddAvailabilityWrapperPage> createState() => _AddAvailabilityWrapperPageState();
}

class _AddAvailabilityWrapperPageState extends State<AddAvailabilityWrapperPage> {
  final isLoading = ValueNotifier<bool>(false);

  final project = ValueNotifier<ProjectModel?>(null);

  final projectRuleCategoryiesList = ValueNotifier<List<ProjectRuleCategoryModel>>([]);

  @override
  void initState() {
    super.initState();
    initApiCalls();
  }

  Future<void> initApiCalls() async {
    try {
      isLoading.value = true;

      await Future.wait([
        getProjectDetails(),
        getProjectAvailability(),
      ]);
      isLoading.value = false;
    } on Exception catch (e) {
      isLoading.value = true;
      debugError('initApiCalls $e');
    }
  }

  Future<void> getProjectDetails() async {
    final response = await getIt<IProjectRepository>().detailProject(projectId: widget.projectId);

    response.fold(
      (l) {},
      (r) {
        project.value = r.data;
      },
    );
  }

  Future<void> getProjectAvailability() async {
    final user = context.read<AuthenticationBloc>().state.mapOrNull(authenticated: (state) => state.user);
    if (user == null) return;
    final response = await getIt<IProjectRepository>()
        .getProjectAvailability(projectId: int.tryParse(widget.projectId) ?? 0, userId: user.id ?? 0);

    response.fold((l) {
      Utility.toast(message: l.message);
    }, (r) {
      projectRuleCategoryiesList.value = [...r.data];
    });
  }

  @override
  void dispose() {
    isLoading.dispose();
    project.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<bool>(
      valueListenable: isLoading,
      builder: (_, value, __) {
        if (value) {
          return Utility.progressIndicator();
        }
        return ValueListenableBuilder<ProjectModel?>(
          valueListenable: project,
          builder: (_, data, __) {
            return ValueListenableBuilder<List<ProjectRuleCategoryModel>>(
              valueListenable: projectRuleCategoryiesList,
              builder: (_, projectRuleCategoryListValue, __) {
                return BlocSelector<AuthenticationBloc, AuthenticationState, UserModel?>(
                  selector: (state) => state.mapOrNull(authenticated: (state) => state.user),
                  builder: (context, user) {
                    final projectRule =
                        data?.projectRules.firstWhereOrNull((element) => element.userType == user?.role);
                    return BlocProvider(
                      create: (context) => getIt<CustomCalendarCubit>(param1: data)
                        ..setHolidayKey(
                          holidayKey: (projectRule?.projectRuleCategories.firstOrNull?.leaveCategoryId ?? 0).toString(),
                          color: projectRule?.projectRuleCategories.firstOrNull?.leaveCategory?.color ??
                              Colors.transparent,
                        )
                        ..initAvailableDate(projectRuleCategoryListValue),
                      child: AddAvailabilityPage(projectRuleModel: projectRule, project: data),
                    );
                  },
                );
              },
            );
          },
        );
      },
    );
  }
}
