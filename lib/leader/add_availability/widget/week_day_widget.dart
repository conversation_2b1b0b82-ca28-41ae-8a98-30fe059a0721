import 'package:excel_app/constants/app_assets.dart';
import 'package:excel_app/widget/app_asset_image.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';

class WeekDayWidget extends StatelessWidget {
  const WeekDayWidget({super.key, this.title, this.isSelected = false, this.onTap});
  final String? title;
  final bool isSelected;
  final void Function()? onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Row(
        children: [
          AppAssetImage(
            isSelected ? AppAssets.selectedCheckboxIcon : AppAssets.unselectedCheckboxIcon,
            height: 20,
            width: 20,
          ),
          const Gap(6),
          Text(title ?? '', style: Theme.of(context).textTheme.bodyMedium),
        ],
      ),
    );
  }
}
