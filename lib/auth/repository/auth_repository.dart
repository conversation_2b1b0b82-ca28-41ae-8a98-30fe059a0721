part of 'i_auth_repository.dart';

@Injectable(as: IAuthRepository)
class AuthRepository extends IAuthRepository {
  AuthRepository(super.client, super.localStorageRepository);

  @override
  ApiResult<LoginResponse> login({required String email, required String password}) async {
    final response = await client.post(
      url: AppStrings.login,
      requests: {
        'email': email.trim(),
        'password': password.trim(),
        'firebase_ids': FirebaseMessagingService.token,
      },
    );

    return response.parseResponse(LoginResponse.fromJson);
  }

  @override
  ApiResult<CommonResponse> forgotPassword({required String email}) async {
    final response = await client.post(
      url: AppStrings.forgotPassword,
      requests: {
        'email': email.trim(),
      },
    );

    return response.parseResponse(CommonResponse.fromJson);
  }

  @override
  ApiResult<GoogleConnectResponse> connectGoogleCalendar() async {
    final response = await client.post(
      url: AppStrings.connectGoogleCalendar,
    );

    return response.parseResponse(GoogleConnectResponse.fromJson);
  }

  @override
  ApiResult<LoginResponse> disconnectGoogleCalendar() async {
    final response = await client.post(
      url: AppStrings.disconnectGoogleCalendar,
    );

    return response.parseResponse(LoginResponse.fromJson);
  }

  @override
  ApiResult<CommonResponse> logout() async {
    final response = await client.post(
      url: AppStrings.logout,
      requests: {
        'firebase_ids': FirebaseMessagingService.token,
      },
    );

    return response.parseResponse(CommonResponse.fromJson);
  }

  @override
  ApiResult<LoginResponse> signInWithGoogle() async {
    try {
      log('Initializing Google Sign-In');

      // Initialize Google Sign-In with scopes and proper configuration
      final googleSignIn = GoogleSignIn();
      log('Google Sign-In initialized');

      // Start the sign-in flow
      final googleUser = await googleSignIn.signIn();
      log('Sign-in flow completed');

      // If the user cancels the sign-in process
      if (googleUser == null) {
        log('Google Sign-In was cancelled by the user');
        return left(HttpFailure.userCancelledSocialLogin());
      }

      // Get authentication details
      final googleAuth = await googleUser.authentication;

      // Create Firebase credential
      final credential = GoogleAuthProvider.credential(
        accessToken: googleAuth.accessToken,
        idToken: googleAuth.idToken,
      );

      // Sign in with Firebase
      final userCredential = await FirebaseAuth.instance.signInWithCredential(credential);

      final firebaseUser = userCredential.user;

      if (firebaseUser == null) {
        return left(HttpFailure.badRequest('Failed to sign in with Google'));
      }

      // Call your backend API for user registration/authentication
      final response = await client.post(
        url: AppStrings.googleSignIn,
        requests: {
          'provider': 'google',
          'access_token': googleAuth.accessToken,
          'firebase_ids': FirebaseMessagingService.token ?? '',
          'full_name': firebaseUser.displayName,
        },
      );

      // Use fpdart's map to transform the response if successful
      return response.fold(
        left,
        (json) => right(LoginResponse.fromJson(json)),
      );
    } on Exception catch (e) {
      // Use a more appropriate error factory based on the exception
      return left(HttpFailure.fetchData('Google sign in failed: $e', 500));
    }
  }
}
