import 'dart:developer';

import 'package:excel_app/constants/app_strings.dart';
import 'package:excel_app/google_connect/response/google_connct_response.dart';
import 'package:excel_app/local_storage/i_local_storage_repository.dart';
import 'package:excel_app/users/response/common_response.dart';
import 'package:excel_app/users/response/login_response.dart';
import 'package:excel_app/utility/extentions/fpdart_extentions.dart';
import 'package:excel_app/utility/firebase_messaging_services.dart';
import 'package:excel_app/utility/network/client.dart';
import 'package:excel_app/utility/network/http_failure.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:fpdart/fpdart.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:injectable/injectable.dart';

part 'auth_repository.dart';

abstract class IAuthRepository {
  IAuthRepository(this.client, this.localStorageRepository);
  final Client client;
  final ILocalStorageRepository localStorageRepository;

  ApiResult<LoginResponse> login({
    required String email,
    required String password,
  });

  ApiResult<CommonResponse> forgotPassword({
    required String email,
  });
  ApiResult<GoogleConnectResponse> connectGoogleCalendar();
  ApiResult<LoginResponse> disconnectGoogleCalendar();
  ApiResult<CommonResponse> logout();

  ApiResult<LoginResponse> signInWithGoogle();
}
