import 'package:excel_app/constants/app_colors.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';

class ChatTextMessageView extends StatelessWidget {
  const ChatTextMessageView({
    required this.message,
    required this.time,
    required this.isOtherUser,
    super.key,
    this.anotherUserName,
    this.imageUrl,
  });
  final String message;
  final String time;
  final bool isOtherUser;
  final String? anotherUserName;
  final String? imageUrl;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 22),
      child: Row(
        mainAxisAlignment: isOtherUser ? MainAxisAlignment.start : MainAxisAlignment.end,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
                constraints: BoxConstraints(
                  maxWidth: MediaQuery.of(context).size.width * 0.3,
                ),
                decoration: BoxDecoration(
                  color: isOtherUser ? AppColors.gray : AppColors.primary,
                  borderRadius: BorderRadius.circular(12),
                ),
                margin: const EdgeInsets.only(
                  top: 8,
                  bottom: 8,
                ),
                child: (message.trim().isNotEmpty)
                    ? Text(
                        message,
                        style: Theme.of(context).textTheme.labelLarge?.copyWith(
                              color: isOtherUser ? AppColors.black : AppColors.white,
                              fontWeight: FontWeight.w400,
                            ),
                      )
                    : const SizedBox(),
              ),
              if (time.trim() != '') ...[
                const Gap(4),
                Text(
                  time,
                  style: Theme.of(context).textTheme.labelSmall?.copyWith(
                        color: AppColors.subText,
                        fontWeight: FontWeight.w500,
                      ),
                ),
              ],
            ],
          ),
        ],
      ),
    );
  }
}
