import 'package:excel_app/constants/app_assets.dart';
import 'package:excel_app/constants/app_colors.dart';
import 'package:excel_app/users/model/user_model.dart';
import 'package:excel_app/widget/custom_network_image.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';

class ChatListWidget extends StatefulWidget {
  const ChatListWidget({
    required this.chatModel,
    this.chatBadgeValue,
    super.key,
    this.onTap,
    this.isSelected = false,
  });
  final void Function()? onTap;
  final bool isSelected;
  final UserModel chatModel;
  final int? chatBadgeValue;

  @override
  State<ChatListWidget> createState() => _ChatListWidgetState();
}

class _ChatListWidgetState extends State<ChatListWidget> {
  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: widget.onTap,
      child: Container(
        height: 88,
        color: widget.isSelected ? AppColors.lovelyPurple.withOpacity2(0.07) : AppColors.white,
        child: Padding(
          padding: const EdgeInsets.only(
            left: 20,
            right: 20,
            top: 20,
          ),
          child: Column(
            children: [
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    clipBehavior: Clip.hardEdge,
                    height: 40,
                    width: 40,
                    decoration: const BoxDecoration(shape: BoxShape.circle),
                    child: CustomNetworkImage(
                      imageUrl: widget.chatModel.imagePath ?? AppAssets.greyBackgroundIcon,
                      height: 24,
                      width: 24,
                    ),
                  ),
                  const Gap(16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          widget.chatModel.name ?? '',
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                fontWeight: FontWeight.w500,
                              ),
                        ),
                        const Gap(8),
                        Text(
                          widget.chatModel.pivot?.message ?? '',
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                color: AppColors.subText,
                              ),
                        ),
                      ],
                    ),
                  ),
                  const Gap(10),
                  Column(
                    children: [
                      Text(
                        widget.chatModel.pivot?.timeAgo ?? '',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: AppColors.subText,
                            ),
                      ),
                      if (widget.chatBadgeValue != 0 && widget.chatBadgeValue != null) ...[
                        const Gap(8),
                        Container(
                          height: 20,
                          width: 20,
                          alignment: Alignment.center,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(10),
                            color: AppColors.primary,
                          ),
                          child: Text(
                            widget.chatBadgeValue.toString(),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                  color: AppColors.white,
                                ),
                          ),
                        ),
                      ],
                    ],
                  ),
                ],
              ),
              const Gap(25),
              const Divider(
                height: 1,
                thickness: 1,
                indent: 55,
                endIndent: 0,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
