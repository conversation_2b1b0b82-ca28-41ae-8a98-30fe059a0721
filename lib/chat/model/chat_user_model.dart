// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:intl/intl.dart';

class ChatUserModel {
  String? chatType;
  String? senderId;
  String? groupId;
  String? senderName;
  Timestamp? timestamp;
  Timestamp? swapDate;
  Timestamp? currentDate;
  String? projectId;
  String? userSwapRequestId;
  bool? isSwapRequest;
  String? message;
  String? imageUrl;
  num? userAbadge;
  num? userBbadge;

  ChatUserModel({
    this.chatType,
    this.senderId,
    this.groupId,
    this.senderName,
    this.timestamp,
    this.swapDate,
    this.currentDate,
    this.projectId,
    this.userSwapRequestId,
    this.isSwapRequest,
    this.message,
    this.imageUrl,
    this.userAbadge,
    this.userBbadge,
  });

  ChatUserModel.fromJson(Map<String, dynamic> json) {
    chatType = json['type'] as String?;
    senderId = json['sender_id'] as String?;
    groupId = json['group_id'] as String?;
    senderName = json['sender_name'] as String?;
    timestamp = json['time_stamp'] as Timestamp?;
    swapDate = json['swap_date'] as Timestamp?;
    currentDate = json['current_date'] as Timestamp?;
    projectId = json['project_id'] as String?;
    userSwapRequestId = json['user_swap_request_id'] as String?;
    isSwapRequest = json['is_swap_request'] as bool? ?? false;
    message = json['message'] as String?;
    imageUrl = json['image_url'] as String?;
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['type'] = chatType;
    data['sender_id'] = senderId;
    data['group_id'] = groupId;
    data['sender_name'] = senderName;
    data['time_stamp'] = timestamp;
    data['swap_date'] = swapDate;
    data['current_date'] = currentDate;
    data['project_id'] = projectId;
    data['user_swap_request_id'] = userSwapRequestId;
    data['is_swap_request'] = isSwapRequest;
    data['message'] = message;
    data['image_url'] = imageUrl;
    data['userA_badge'] = userAbadge;
    data['userB_badge'] = userBbadge;
    return data;
  }

  DateTime get timeStampDate => timestamp == null ? DateTime.now() : timestamp!.toDate();

  String get formmetedTime => DateFormat('hh:mm a').format(timeStampDate.toLocal());
}
