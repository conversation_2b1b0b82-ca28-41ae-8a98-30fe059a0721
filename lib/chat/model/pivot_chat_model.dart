import 'package:equatable/equatable.dart';

class PivotChatModel extends Equatable {
  const PivotChatModel({
    this.fromUserId,
    this.toUserId,
    this.message,
    this.updatedAt,
    this.createdAt,
  });

  factory PivotChatModel.fromJson(Map<String, dynamic> json) {
    return PivotChatModel(
      fromUserId: json['from_user_id'] as int?,
      toUserId: json['to_user_id'] as int?,
      message: json['message'] as String?,
      createdAt: DateTime.tryParse((json['created_at'] as String?) ?? ''),
      updatedAt: DateTime.tryParse((json['updated_at'] as String?) ?? ''),
    );
  }

  final int? fromUserId;
  final int? toUserId;
  final String? message;
  final DateTime? updatedAt;
  final DateTime? createdAt;

  Map<String, dynamic> toJson() => {
        'from_user_id': fromUserId,
        'to_user_id': toUserId,
        'message': message,
        'updated_at': updatedAt?.toIso8601String(),
        'created_at': createdAt?.toIso8601String(),
      };

  @override
  List<Object?> get props => [
        fromUserId,
        toUserId,
        message,
        updatedAt,
        createdAt,
      ];
  String get timeAgo {
    if (updatedAt == null) {
      return '';
    }
    final diff = DateTime.now().difference(updatedAt!);
    if (diff.inDays > 365) {
      return "${(diff.inDays / 365).floor()} ${(diff.inDays / 365).floor() == 1 ? "year" : "years"} ago";
    }
    if (diff.inDays > 30) {
      return "${(diff.inDays / 30).floor()} ${(diff.inDays / 30).floor() == 1 ? "month" : "months"} ago";
    }
    if (diff.inDays > 7) {
      return "${(diff.inDays / 7).floor()} ${(diff.inDays / 7).floor() == 1 ? "week" : "weeks"} ago";
    }
    if (diff.inDays > 0) {
      return "${diff.inDays} ${diff.inDays == 1 ? "day" : "days"} ago";
    }
    if (diff.inHours > 0) {
      return "${diff.inHours} ${diff.inHours == 1 ? "hour" : "hours"} ago";
    }
    if (diff.inMinutes > 0) {
      return "${diff.inMinutes} ${diff.inMinutes == 1 ? "minute" : "minutes"} ago";
    }
    return 'just now';
  }
}
