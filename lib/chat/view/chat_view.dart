import 'dart:developer';

import 'package:excel_app/app/bloc/authentication_bloc.dart';
import 'package:excel_app/chat/repository/i_chat_repository.dart';
import 'package:excel_app/chat/view/chat_detail_view.dart';
import 'package:excel_app/chat/view/chat_list_view.dart';
import 'package:excel_app/injector/injector.dart';
import 'package:excel_app/users/model/user_model.dart';
import 'package:excel_app/utility/helpers/utility.dart';
import 'package:excel_app/widget/container_widget.dart';
import 'package:excel_app/widget/no_data_available_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class ChatView extends StatefulWidget {
  const ChatView({super.key});

  @override
  State<ChatView> createState() => _ChatViewState();
}

class _ChatViewState extends State<ChatView> {
  final userList = ValueNotifier<List<UserModel>>([]);
  final isLoading = ValueNotifier<bool>(false);
  final selectedUserData = ValueNotifier<UserModel?>(null);

  @override
  void initState() {
    super.initState();
    getChatList();
  }

  Future<void> getChatList() async {
    isLoading.value = true;
    final failOrSucess = await getIt<IChatRepository>().getChatList();
    failOrSucess.fold(
      (l) {
        isLoading.value = false;
      },
      (r) {
        userList.value = r.data;
        selectedUserData.value = userList.value.firstOrNull;
        isLoading.value = false;
      },
    );
  }

  _refresh() {
    userList.value = [];
    getChatList();
  }

  @override
  void dispose() {
    userList.dispose();
    isLoading.dispose();
    selectedUserData.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AuthenticationBloc, AuthenticationState>(
      builder: (context, state) {
        if (state is Authenticated) {
          return ContainerWidget(
            padding: EdgeInsets.zero,
            child: ValueListenableBuilder(
              valueListenable: userList,
              builder: (context, list, _) {
                return ValueListenableBuilder(
                  valueListenable: isLoading,
                  builder: (context, loading, _) {
                    if (loading) {
                      return Center(
                        child: Utility.progressIndicator(),
                      );
                    }
                    if (!loading && list.isEmpty) {
                      return const Center(
                        child: NoDataAvailableWidget(
                          message: 'No Chat Available',
                        ),
                      );
                    }
                    return ValueListenableBuilder<UserModel?>(
                      valueListenable: selectedUserData,
                      builder: (context, selectUser, _) {
                        if (list.isNotEmpty) {
                          return Row(
                            children: [
                              ChatListView(
                                onTap: (p0) {
                                  selectedUserData.value = p0;
                                },
                                isSelectedUser: selectUser,
                                chatList: userList.value,
                                userId: state.user.id ?? 0,
                              ),
                              ChatDetailsView(
                                otherUserDetail: selectUser,
                                userId: state.user.id ?? 0,
                                onUpdateTap: ({otherUserId}) {
                                  final currentList = List<UserModel>.from(userList.value);

                                  final userIndex = currentList.indexWhere((u) => u.id == otherUserId);
                                  if (userIndex == -1 || userIndex == 0) return; // user not found or already at top

                                  final user = currentList.removeAt(userIndex);
                                  currentList.insert(0, user);

                                  userList.value = currentList;
                                  selectedUserData.value = user;

                                  log('${user.name} moved to top of list');
                                },
                              ),
                            ],
                          );
                        }
                        return const SizedBox.shrink();
                      },
                    );
                  },
                );
              },
            ),
          );
        }
        return const SizedBox.shrink();
      },
    );
  }
}
