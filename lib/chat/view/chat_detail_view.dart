import 'dart:developer';

import 'package:excel_app/chat/model/chat_user_model.dart';
import 'package:excel_app/chat/repository/i_chat_repository.dart';
import 'package:excel_app/chat/services/firebase_chat_service.dart';
import 'package:excel_app/chat/view/swap_request_view.dart';
import 'package:excel_app/chat/widget/chat_text_message_item_view.dart';
import 'package:excel_app/constants/app_assets.dart';
import 'package:excel_app/constants/app_constants.dart';
import 'package:excel_app/injector/injector.dart';
import 'package:excel_app/users/model/user_model.dart';
import 'package:excel_app/users/widget/user_alert_dialog_box.dart';
import 'package:excel_app/utility/firebase_messaging_services.dart';
import 'package:excel_app/utility/helpers/utility.dart';
import 'package:excel_app/widget/app_text_form_field.dart';
import 'package:excel_app/widget/custom_network_image.dart';
import 'package:excel_app/widget/dialogs.dart';
import 'package:firebase_ui_firestore/firebase_ui_firestore.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';

class ChatDetailsView extends StatefulWidget {
  const ChatDetailsView({required this.userId, required this.onUpdateTap, super.key, this.otherUserDetail});
  final UserModel? otherUserDetail;
  final int userId;
  final void Function({int? otherUserId}) onUpdateTap;

  @override
  State<ChatDetailsView> createState() => _ChatDetailsViewState();
}

class _ChatDetailsViewState extends State<ChatDetailsView> {
  final messageController = TextEditingController();
  ValueNotifier<bool> isLoading = ValueNotifier<bool>(false);
  ValueNotifier<bool> isDeleteLoading = ValueNotifier<bool>(false);
  ValueNotifier<bool> isStackLoading = ValueNotifier<bool>(false);
  final chatController = ScrollController();
  final chatList = ValueNotifier<List<ChatUserModel>>([]);
  final FocusNode messageFocusNode = FocusNode();
  String? swapRequestId;

  @override
  void initState() {
    super.initState();
    log('userId: ${widget.userId}');
    log('chatId: ${widget.otherUserDetail?.id}');
    log('chatId: ${FirebaseMessagingService.token}');
  }

  @override
  Widget build(BuildContext context) {
    return Flexible(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 19),
            child: Row(
              children: [
                Container(
                  clipBehavior: Clip.hardEdge,
                  height: 40,
                  width: 40,
                  decoration: const BoxDecoration(shape: BoxShape.circle),
                  child: CustomNetworkImage(
                    imageUrl: widget.otherUserDetail?.imagePath ?? AppAssets.greyBackgroundIcon,
                    height: 24,
                    width: 24,
                  ),
                ),
                const Gap(14),
                Text(
                  widget.otherUserDetail?.name ?? '',
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w500,
                      ),
                ),
              ],
            ),
          ),
          const Divider(
            height: 1,
            thickness: 1,
            endIndent: 0,
            indent: 0,
          ),
          Expanded(
            child: Stack(
              alignment: Alignment.center,
              children: [
                FirestoreListView<ChatUserModel>(
                  query: FireaseChatService.getMessagesQuery(
                    uniqueKey: FireaseChatService.generateUniqueKey(
                      userId1: widget.otherUserDetail?.id ?? 0,
                      userId2: widget.userId,
                    ),
                    myId: widget.userId.toString(),
                  ),
                  pageSize: 20,
                  emptyBuilder: (context) => Center(
                    child: Utility.noDataWidget(
                      context: context,
                      text: 'No messages',
                    ),
                  ),
                  errorBuilder: (context, error, stackTrace) {
                    log(error.toString());
                    return Text(error.toString());
                  },
                  reverse: true,
                  loadingBuilder: (context) => Center(child: Utility.progressIndicator()),
                  itemBuilder: (context, doc) {
                    final chatData = doc.data();
                    if (chatData.chatType == AppConstants.swapRequest) {
                      return ValueListenableBuilder(
                        valueListenable: isLoading,
                        builder: (context, loading, _) {
                          return SwapRequestView(
                            chatModel: chatData,
                            isAcceptedSuccessful: chatData.isSwapRequest ?? false,
                            isOtherUser: chatData.senderId != widget.userId.toString(),
                            isLoading: (swapRequestId == chatData.userSwapRequestId) && loading,
                            onTap: chatData.isSwapRequest ?? false
                                ? null
                                : () {
                                    log('message');
                                    swapRequestId = chatData.userSwapRequestId;
                                    log('$swapRequestId    ${chatData.userSwapRequestId}check two id');
                                    acceptSwapRequest(chatUserModel: chatData);
                                  },
                            deleteOnTap: () {
                              DailogBox.showBluredBgDailog(
                                context,
                                ValueListenableBuilder(
                                  valueListenable: isDeleteLoading,
                                  builder: (context, loading, _) {
                                    return UserAlertDialogBox(
                                      imageIcon: AppAssets.closeCircleIcon,
                                      title: 'Delete Swap Request',
                                      message: 'Are you sure you want to delete this swap request?',
                                      firstButtonTitle: 'Cancel',
                                      secondButtonTitle: 'Delete',
                                      isLoading: loading,
                                      firstButtonOnTap: () {
                                        context.pop();
                                      },
                                      secondButtonOnTap: () {
                                        deleteSwapRequest(chatUserModel: chatData);
                                      },
                                    );
                                  },
                                ),
                              );
                            },
                          );
                        },
                      );
                    }
                    return ChatTextMessageView(
                      message: chatData.message ?? '',
                      time: chatData.formmetedTime,
                      imageUrl: chatData.imageUrl,
                      isOtherUser: chatData.senderId == widget.otherUserDetail?.id.toString(),
                    );
                  },
                ),
                ValueListenableBuilder<bool>(
                  valueListenable: isStackLoading,
                  builder: (_, value, __) {
                    if (value) {
                      return Center(child: Utility.progressIndicator());
                    }
                    return const SizedBox();
                  },
                ),
              ],
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(20),
            child: AppTextFormField(
              contentPadding: const EdgeInsets.all(20),
              focusNode: messageFocusNode,
              controller: messageController,
              maxWidth: MediaQuery.of(context).size.width,
              hintText: 'Type a message...',
              onFieldSubmitted: (value) {
                if (messageFocusNode.hasFocus && messageController.text.trim().isNotEmpty) {
                  sendMessageRequst();
                }
              },
              suffixIcon: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  InkWell(
                    onTap: () {
                      if (messageController.text.trim().isNotEmpty) sendMessageRequst();
                    },
                    child: Image.asset(
                      AppAssets.sendIcon,
                      height: 36,
                      width: 36,
                    ),
                  ),
                  const Gap(16),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> sendMessageRequst() async {
    isStackLoading.value = true;
    final message = messageController.text.trim();
    messageController.clear();
    String useraId;
    String userbId;
    if (widget.userId < (widget.userId)) {
      useraId = widget.userId.toString();
      userbId = widget.otherUserDetail?.id.toString() ?? '0';
    } else {
      useraId = widget.otherUserDetail?.id.toString() ?? '0';
      userbId = widget.userId.toString();
    }
    await FireaseChatService.enterDetailUsersChatListCollection(
      chatType: AppConstants.message,
      useraId: useraId,
      userbId: userbId,
      groupchatid: FireaseChatService.generateUniqueKey(
        userId1: widget.otherUserDetail?.id ?? 0,
        userId2: widget.userId,
      ),
      message: message,
      myid: widget.userId.toString(),
    );
    await FireaseChatService.enterChatsData(
      myId: widget.userId.toString(),
      otherId: widget.otherUserDetail?.id.toString() ?? '0',
      groupChatId: FireaseChatService.generateUniqueKey(
        userId1: widget.otherUserDetail?.id ?? 0,
        userId2: widget.userId,
      ),
      content: message,
      type: AppConstants.message,
    );
    isStackLoading.value = false;
    await sendMessageApi(message: message);
  }

  Future<void> sendMessageApi({required String message}) async {
    final failOrSuccess = await getIt<IChatRepository>().sendMessage(
      message: message,
      userId: widget.otherUserDetail?.id.toString() ?? '0',
    );
    failOrSuccess.fold(
      (l) {
        Utility.toast(message: l.message);
      },
      (r) {
        messageFocusNode.requestFocus();
        widget.onUpdateTap(otherUserId: widget.otherUserDetail?.id ?? 0);
        setState(() {});
      },
    );
  }

  Future<void> acceptSwapRequest({ChatUserModel? chatUserModel}) async {
    isLoading.value = true;
    final failOrSuccess = await getIt<IChatRepository>().acceptSwapRequest(
      userSwapRequestId: chatUserModel?.userSwapRequestId ?? '',
      projectId: chatUserModel?.projectId ?? '',
    );
    await failOrSuccess.fold(
      (l) {
        isLoading.value = false;
        Utility.toast(message: l.message);
        return Future<void>.value();
      },
      (r) async {
        log('${isLoading.value} update loading value');
        // Update is_swap_request field in Firebase when swap request is accepted successfully
        String useraId;
        String userbId;
        if (widget.userId < (widget.otherUserDetail?.id ?? 0)) {
          useraId = widget.userId.toString();
          userbId = widget.otherUserDetail?.id.toString() ?? '0';
        } else {
          useraId = widget.otherUserDetail?.id.toString() ?? '0';
          userbId = widget.userId.toString();
        }

        final groupChatId = FireaseChatService.generateUniqueKey(
          userId1: widget.otherUserDetail?.id ?? 0,
          userId2: widget.userId,
        );

        // Update the chat list collection
        await FireaseChatService.enterDetailUsersChatListCollection(
          chatType: AppConstants.swapRequest,
          useraId: useraId,
          userbId: userbId,
          groupchatid: groupChatId,
          myid: widget.userId.toString(),
          swapDate: chatUserModel?.swapDate,
          currentDate: chatUserModel?.currentDate,
          projectId: chatUserModel?.projectId,
          isSwapRequest: true, // Set is_swap_request to true
        );

        // Find the existing message document ID
        final existingDocumentId = await FireaseChatService.findSwapRequestDocumentId(
          groupChatId: groupChatId,
          swapDate: chatUserModel?.swapDate,
          currentDate: chatUserModel?.currentDate,
          projectId: chatUserModel?.projectId,
        );

        log('Existing document ID: $existingDocumentId');

        // Update the existing message or create a new one if not found
        await FireaseChatService.enterChatsData(
          myId: widget.userId.toString(),
          otherId: widget.otherUserDetail?.id.toString() ?? '0',
          groupChatId: groupChatId,
          type: AppConstants.swapRequest,
          swapDate: chatUserModel?.swapDate,
          currentDate: chatUserModel?.currentDate,
          projectId: chatUserModel?.projectId,
          isSwapRequest: true, // Set is_swap_request to true
          documentId: existingDocumentId, // Pass the document ID if found
        );
        isLoading.value = false;
        widget.onUpdateTap.call();
        Utility.toast(message: r.message);
      },
    );
  }

  Future<void> deleteSwapRequest({ChatUserModel? chatUserModel}) async {
    isDeleteLoading.value = true;
    log('${chatUserModel?.userSwapRequestId ?? ' '} fedgf');
    final failOrSuccess = await getIt<IChatRepository>().deleteSwapRequest(
      userSwapRequestId: chatUserModel?.userSwapRequestId ?? '',
      projectId: chatUserModel?.projectId ?? '',
    );
    await failOrSuccess.fold(
      (l) {
        isDeleteLoading.value = false;
        Utility.toast(message: l.message);
        return Future<void>.value();
      },
      (r) async {
        // Get the group chat ID
        final groupChatId = FireaseChatService.generateUniqueKey(
          userId1: widget.otherUserDetail?.id ?? 0,
          userId2: widget.userId,
        );

        // Find the existing message document ID
        final existingDocumentId = await FireaseChatService.findSwapRequestDocumentId(
          groupChatId: groupChatId,
          swapDate: chatUserModel?.swapDate,
          currentDate: chatUserModel?.currentDate,
          projectId: chatUserModel?.projectId,
        );

        log('Deleting swap request document ID: $existingDocumentId');

        // If we found the document, delete it from Firebase
        if (existingDocumentId != null) {
          // Delete the document from the messages collection
          await FireaseChatService.usersMessagesCollection.doc(existingDocumentId).delete();

          // Also update the chat list to remove this swap request

          // Find the chat list document
          final chatListQuery =
              await FireaseChatService.userschatlistsCollection.where('group_id', isEqualTo: groupChatId).get();

          if (chatListQuery.docs.isNotEmpty) {
            // Update the chat list to remove this swap request
            // We're not deleting the entire chat, just updating it to remove this specific swap request
            await FireaseChatService.userschatlistsCollection.doc(chatListQuery.docs[0].id).update({
              'type': AppConstants.message, // Change type back to regular message
              'swap_date': null,
              'current_date': null,
              'project_id': null,
              'user_swap_request_id': null,
              'is_swap_request': null,
            });
          }
        }

        isDeleteLoading.value = false;
        widget.onUpdateTap.call();
        context.pop();
        Utility.toast(message: r.message);
      },
    );
  }
}
