import 'package:excel_app/chat/model/chat_user_model.dart';
import 'package:excel_app/constants/app_colors.dart';
import 'package:excel_app/widget/custome_button_gradiant_widget.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:intl/intl.dart';

class SwapRequestView extends StatelessWidget {
  const SwapRequestView({
    super.key,
    this.chatModel,
    this.isOtherUser = false,
    this.isLoading = false,
    this.onTap,
    this.isAcceptedSuccessful = false,
    this.deleteOnTap,
  });
  final ChatUserModel? chatModel;
  final bool isOtherUser;
  final void Function()? onTap;
  final void Function()? deleteOnTap;
  final bool isLoading;
  final bool isAcceptedSuccessful;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 22),
      child: Row(
        mainAxisAlignment: isOtherUser ? MainAxisAlignment.start : MainAxisAlignment.end,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Container(
                padding: const EdgeInsets.all(16),
                width: 242,
                height: 126,
                decoration: BoxDecoration(
                  color: isOtherUser ? AppColors.gray : AppColors.primary,
                  borderRadius: BorderRadius.circular(12),
                ),
                margin: const EdgeInsets.only(
                  top: 8,
                  bottom: 8,
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    CustomeButtonGradiantWidget(
                      height: 34,
                      buttonText: isOtherUser
                          ? isAcceptedSuccessful
                              ? 'Swap request accepted'
                              : 'Accept Swap request'
                          : 'Shift Swap requested',
                      buttonColor: isOtherUser ? AppColors.primary : AppColors.white,
                      buttonTextColor: isOtherUser ? AppColors.white : AppColors.primary,
                      isLoading: isLoading,
                      isGradient: true,
                      onTap: isOtherUser
                          ? () {
                              onTap?.call();
                            }
                          : null,
                    ),
                    const Gap(10),
                    Text(
                      '${DateFormat('dd/MM/yyyy').format(chatModel?.currentDate?.toDate() ?? DateTime.now())} to ${DateFormat('dd/MM/yyyy').format(chatModel?.swapDate?.toDate() ?? DateTime.now())}',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: isOtherUser ? AppColors.dark : AppColors.white,
                          ),
                    ),
                    const Gap(10),
                    InkWell(
                      onTap: deleteOnTap,
                      child: Text(
                        'Delete',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: isOtherUser ? AppColors.dark : AppColors.white,
                              decoration: TextDecoration.underline,
                            ),
                      ),
                    ),
                  ],
                ),
              ),
              if (chatModel?.formmetedTime.trim() != '') ...[
                const Gap(4),
                Text(
                  chatModel?.formmetedTime ?? '',
                  style: Theme.of(context).textTheme.labelSmall?.copyWith(
                        color: AppColors.subText,
                        fontWeight: FontWeight.w500,
                      ),
                ),
              ],
            ],
          ),
        ],
      ),
    );
  }
}
