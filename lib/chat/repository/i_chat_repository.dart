import 'package:excel_app/chat/response/chat_list_response.dart';
import 'package:excel_app/home/<USER>/availability_swap_request_response.dart';
import 'package:excel_app/users/response/common_response.dart';
import 'package:excel_app/users/response/user_list_response.dart';
import 'package:excel_app/utility/network/client.dart';

abstract class IChatRepository {
  IChatRepository(this.client);
  final Client client;
  ApiResult<ChatListResponse> getChatList();
  ApiResult<CommonResponse> sendMessage({required String message, required String userId});
  ApiResult<UserListResponse> projectSwapAvailableUsers({
    required String projectId,
    int page = 1,
    int perPage = 20,
    DateTime? date,
  });

  ApiResult<AvailabilitySwapRequestResponse> sendSwapRequest({
    required DateTime currentDate,
    required DateTime swapWithDate,
    required String userId,
    required String projectId,
  });

  ApiResult<CommonResponse> acceptSwapRequest({
    required String projectId,
    required String userSwapRequestId,
  });
  ApiResult<CommonResponse> deleteSwapRequest({
    required String projectId,
    required String userSwapRequestId,
  });
}
