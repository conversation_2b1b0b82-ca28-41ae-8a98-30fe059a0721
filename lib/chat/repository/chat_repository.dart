import 'package:excel_app/chat/repository/i_chat_repository.dart';
import 'package:excel_app/chat/response/chat_list_response.dart';
import 'package:excel_app/constants/app_strings.dart';
import 'package:excel_app/home/<USER>/availability_swap_request_response.dart';
import 'package:excel_app/users/response/common_response.dart';
import 'package:excel_app/users/response/user_list_response.dart';
import 'package:excel_app/utility/extentions/fpdart_extentions.dart';
import 'package:excel_app/utility/network/client.dart';
import 'package:injectable/injectable.dart';
import 'package:intl/intl.dart';

@Injectable(as: IChatRepository)
class ChatRepository extends IChatRepository {
  ChatRepository(super.client);

  @override
  ApiResult<ChatListResponse> getChatList() async {
    final response = await client.get(
      url: AppStrings.chatList,
    );
    return response.parseResponse(ChatListResponse.fromJson);
  }

  @override
  ApiResult<CommonResponse> sendMessage({required String message, required String userId}) async {
    final response = await client.post(
      url: AppStrings.sendMessage,
      requests: <String, String>{
        'message': message,
        'user_id': userId,
      },
    );
    return response.parseResponse(CommonResponse.fromJson);
  }

  @override
  ApiResult<UserListResponse> projectSwapAvailableUsers({
    required String projectId,
    int page = 1,
    int perPage = 20,
    DateTime? date,
  }) async {
    final response = await client.get(
      url: AppStrings.projectAvailableUsers(projectId),
      params: {
        'page': page.toString(),
        'per_page': perPage.toString(),
        if (date != null) 'date': DateFormat('yyyy-MM-dd').format(date),
      },
    );
    return response.parseResponse(UserListResponse.fromJson);
  }

  @override
  ApiResult<AvailabilitySwapRequestResponse> sendSwapRequest({
    required DateTime currentDate,
    required DateTime swapWithDate,
    required String userId,
    required String projectId,
  }) async {
    final response = await client.post(
      url: AppStrings.sendSwapRequest(projectId),
      requests: {
        'current_date': DateFormat('yyyy-MM-dd').format(currentDate),
        'swap_with_date': DateFormat('yyyy-MM-dd').format(swapWithDate),
        'user_id': userId,
      },
    );
    return response.parseResponse(AvailabilitySwapRequestResponse.fromJson);
  }

  @override
  ApiResult<CommonResponse> acceptSwapRequest({
    required String userSwapRequestId,
    required String projectId,
  }) async {
    final response = await client.post(
      url: AppStrings.acceptSwapRequest(projectId),
      requests: {
        'request_id': userSwapRequestId,
      },
    );
    return response.parseResponse(CommonResponse.fromJson);
  }

  @override
  ApiResult<CommonResponse> deleteSwapRequest({
    required String userSwapRequestId,
    required String projectId,
  }) async {
    final response = await client.delete(
      url: AppStrings.deleteSwapRequest(projectId),
      params: {
        'request_id': userSwapRequestId,
      },
    );
    return response.parseResponse(CommonResponse.fromJson);
  }
}
