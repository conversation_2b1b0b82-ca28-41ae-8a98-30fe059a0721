import 'dart:developer';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:excel_app/chat/model/chat_user_model.dart';
import 'package:excel_app/constants/app_constants.dart';

class FireaseChatService {
  static String chatUserList = 'users_chat_list';
  static String chatMessagesCollections = 'messages';
  static CollectionReference userschatlistsCollection = FirebaseFirestore.instance.collection(chatUserList);
  static CollectionReference usersMessagesCollection = FirebaseFirestore.instance.collection(chatMessagesCollections);

  static Query<ChatUserModel> getMessagesQuery({required String uniqueKey, required String myId}) {
    messagesreadupdatebadgevaluetozero(uniqueKey, myId);
    log('uniqueKey: $uniqueKey, myId: $myId');
    final query = usersMessagesCollection
        .where('group_id', isEqualTo: uniqueKey)
        .orderBy('time_stamp', descending: true)
        .withConverter(
          fromFirestore: (snapshot, options) => ChatUserModel.fromJson(snapshot.data()!),
          toFirestore: (chatUser, options) => chatUser.toJson(),
        );
    return query;
  }

  static String generateUniqueKey({required int userId1, required int userId2}) {
    final list = <int>[userId1, userId2];
    list.sort();
    return '${list[0]}-${list[1]}';
  }

  static Stream<QuerySnapshot> getUsersChatList(String userId) {
    return userschatlistsCollection.where('id_array', arrayContains: userId).snapshots();
  }

  static Future<void> enterDetailUsersChatListCollection({
    required String useraId,
    required String userbId,
    String? chatType,
    String? message,
    String? groupchatid,
    String? myid,
    String? imageUrl,
    Timestamp? swapDate,
    Timestamp? currentDate,
    String? projectId,
    String? userSwapRequestId,
    bool? isSwapRequest,
  }) async {
    final idsarray = <String>[useraId, userbId];

    final qs = await userschatlistsCollection.where('group_id', isEqualTo: groupchatid).get();
    if (qs.docs.isNotEmpty) {
      log('not send 1');
      await userschatlistsCollection.doc(qs.docs[0].id).update({
        if (imageUrl != null) 'image_url': imageUrl,
        'type': chatType,
        'message': message,
        'time_stamp': FieldValue.serverTimestamp(),
        'usera_badge': FieldValue.increment(
          myid == userbId ? (1) : (0),
        ),
        'userb_badge': FieldValue.increment(
          myid == useraId ? (1) : (0),
        ),
        if (swapDate != null) 'swap_date': swapDate,
        if (currentDate != null) 'current_date': currentDate,
        if (projectId != null) 'project_id': projectId,
        if (userSwapRequestId != null) 'user_swap_request_id': userSwapRequestId,
        if (isSwapRequest != null) 'is_swap_request': isSwapRequest,
      });
    } else {
      log('not send 2');
      await userschatlistsCollection.doc().set({
        if (imageUrl != null) 'image_url': imageUrl,
        'usera_id': useraId,
        'userb_id': userbId,
        'type': chatType,
        'group_id': groupchatid,
        'id_array': idsarray,
        'message': message,
        'usera_badge': myid == userbId ? 1 : 0,
        'userb_badge': myid == useraId ? 1 : 0,
        'time_stamp': FieldValue.serverTimestamp(),
        if (swapDate != null) 'swap_date': swapDate,
        if (currentDate != null) 'current_date': currentDate,
        if (projectId != null) 'project_id': projectId,
        if (userSwapRequestId != null) 'user_swap_request_id': userSwapRequestId,
        if (isSwapRequest != null) 'is_swap_request': isSwapRequest,
      });
    }
  }

  static Future<void> enterChatsData({
    required String myId,
    required String otherId,
    required String groupChatId,
    required String type,
    String? content,
    String? imageUrl,
    Timestamp? swapDate,
    Timestamp? currentDate,
    String? projectId,
    String? userSwapRequestId,
    bool? isSwapRequest,
    String? documentId,
  }) async {
    if (documentId != null) {
      // Update existing document
      await usersMessagesCollection.doc(documentId).update({
        if (imageUrl != null) 'image_url': imageUrl,
        'time_stamp': FieldValue.serverTimestamp(),
        if (content != null) 'message': content,
        if (swapDate != null) 'swap_date': swapDate,
        if (currentDate != null) 'current_date': currentDate,
        if (projectId != null) 'project_id': projectId,
        if (userSwapRequestId != null) 'user_swap_request_id': userSwapRequestId,
        if (isSwapRequest != null) 'is_swap_request': isSwapRequest,
      });
    } else {
      // Create new document
      await usersMessagesCollection.doc().set({
        if (imageUrl != null) 'image_url': imageUrl,
        'userb_id': myId,
        'usera_id': otherId,
        'group_id': groupChatId,
        'time_stamp': FieldValue.serverTimestamp(),
        'message': content,
        'sender_id': myId,
        'type': type,
        if (swapDate != null) 'swap_date': swapDate,
        if (currentDate != null) 'current_date': currentDate,
        if (projectId != null) 'project_id': projectId,
        if (userSwapRequestId != null) 'user_swap_request_id': userSwapRequestId,
        if (isSwapRequest != null) 'is_swap_request': isSwapRequest,
      });
    }
  }

  static Future<void> messagesreadupdatebadgevaluetozero(String groupChatId, String myid) async {
    final qs = await userschatlistsCollection.where('group_id', isEqualTo: groupChatId).get();
    if (qs.docs.isNotEmpty) {
      if (qs.docs[0]['usera_id'] == myid) {
        await userschatlistsCollection.doc(qs.docs[0].id).update({'usera_badge': 0});
      } else {
        await userschatlistsCollection.doc(qs.docs[0].id).update({'userb_badge': 0});
      }
    }
  }

  static Stream<QuerySnapshot> getUsersChatListIndividualBadgeValue(int myId, int otherId) {
    return userschatlistsCollection
        .where('group_id', isEqualTo: generateUniqueKey(userId1: myId, userId2: otherId))
        .snapshots();
  }

  /// Get a specific message document by its properties
  static Future<QuerySnapshot> getMessageDocument({
    required String groupChatId,
    required String type,
    Timestamp? swapDate,
    Timestamp? currentDate,
    String? projectId,
  }) async {
    var query = usersMessagesCollection.where('group_id', isEqualTo: groupChatId).where('type', isEqualTo: type);

    if (projectId != null) {
      query = query.where('project_id', isEqualTo: projectId);
    }

    // For all cases, get the documents and filter manually if needed
    final docs = await query.get();

    // If we need to filter by dates
    if (swapDate != null) {
      // Find the document that matches our criteria
      for (final doc in docs.docs) {
        final data = doc.data()! as Map<String, dynamic>;
        final docSwapDate = data['swap_date'] as Timestamp?;
        final docCurrentDate = data['current_date'] as Timestamp?;

        if (docSwapDate == null || docCurrentDate == null) continue;

        // Compare dates (ignoring time)
        final swapDateMatches = docSwapDate.toDate().year == swapDate.toDate().year &&
            docSwapDate.toDate().month == swapDate.toDate().month &&
            docSwapDate.toDate().day == swapDate.toDate().day;

        final currentDateMatches = currentDate != null &&
            docCurrentDate.toDate().year == currentDate.toDate().year &&
            docCurrentDate.toDate().month == currentDate.toDate().month &&
            docCurrentDate.toDate().day == currentDate.toDate().day;

        if (swapDateMatches && currentDateMatches) {
          // We found a matching document, return it
          log('Found matching document: ${doc.id}');
          return docs;
        }
      }
    }
    return query.get();
  }

  /// Find a specific swap request message document ID
  static Future<String?> findSwapRequestDocumentId({
    required String groupChatId,
    required Timestamp? swapDate,
    required Timestamp? currentDate,
    required String? projectId,
  }) async {
    final querySnapshot = await getMessageDocument(
      groupChatId: groupChatId,
      type: AppConstants.swapRequest,
      swapDate: swapDate,
      currentDate: currentDate,
      projectId: projectId,
    );

    // Find the document that matches our criteria
    for (final doc in querySnapshot.docs) {
      final data = doc.data()! as Map<String, dynamic>;
      final docSwapDate = data['swap_date'] as Timestamp?;
      final docCurrentDate = data['current_date'] as Timestamp?;

      if (docSwapDate == null || docCurrentDate == null) continue;

      // Compare dates (ignoring time)
      final swapDateMatches = docSwapDate.toDate().year == swapDate!.toDate().year &&
          docSwapDate.toDate().month == swapDate.toDate().month &&
          docSwapDate.toDate().day == swapDate.toDate().day;

      final currentDateMatches = currentDate != null &&
          docCurrentDate.toDate().year == currentDate.toDate().year &&
          docCurrentDate.toDate().month == currentDate.toDate().month &&
          docCurrentDate.toDate().day == currentDate.toDate().day;

      if (swapDateMatches && currentDateMatches) {
        // We found a matching document, return its ID
        log('Found matching document ID: ${doc.id}');
        return doc.id;
      }
    }

    // No matching document found
    return null;
  }
}
