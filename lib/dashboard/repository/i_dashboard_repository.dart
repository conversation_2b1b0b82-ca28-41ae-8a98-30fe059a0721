import 'dart:typed_data';

import 'package:excel_app/constants/app_constants.dart';
import 'package:excel_app/constants/app_strings.dart';
import 'package:excel_app/dashboard/response/dashboard_reponse.dart';
import 'package:excel_app/projects/model/project_model.dart';
import 'package:excel_app/users/model/user_model.dart';
import 'package:excel_app/utility/extentions/fpdart_extentions.dart';
import 'package:excel_app/utility/network/client.dart';
import 'package:fpdart/fpdart.dart';
import 'package:injectable/injectable.dart';
import 'package:intl/intl.dart';

part 'dashboard_repository.dart';

abstract class IDashboardRepository {
  IDashboardRepository(this.client);
  final Client client;

  ApiResult<DashboardResponse> getDashboard({
    required DateTime startDate,
    required DateTime endDate,
    List<String>? userRoles,
    List<UserModel>? users,
    List<ProjectModel>? projects,
    String? search,
    int page = 1,
    int perPage = 20,
  });

  ApiResult<Uint8List> exportDashboard({
    required DateTime startDate,
    required DateTime endDate,
    List<String>? userRoles,
    List<UserModel>? users,
    List<ProjectModel>? projects,
  });
}
