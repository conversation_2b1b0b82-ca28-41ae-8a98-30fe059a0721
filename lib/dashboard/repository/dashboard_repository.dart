part of 'i_dashboard_repository.dart';

@Injectable(as: IDashboardRepository)
class DashboardRepository extends IDashboardRepository {
  DashboardRepository(super.client);

  @override
  ApiResult<DashboardResponse> getDashboard({
    required DateTime startDate,
    required DateTime endDate,
    List<String>? userRoles,
    List<UserModel>? users,
    List<ProjectModel>? projects,
    String? search,
    int page = 1,
    int perPage = 20,
  }) async {
    final response = await client.get(
      url: AppStrings.dashboard,
      params: {
        if (search != null && search.trim().isNotEmpty) 'search': search,
        'start_date': DateFormat('yyyy-MM-dd').format(startDate),
        'end_date': DateFormat('yyyy-MM-dd').format(endDate),
        'page': page.toString(),
        'per_page': perPage.toString(),
        if (userRoles != null && userRoles.isNotEmpty && !userRoles.contains('All users')) ...{
          for (int i = 0; i < userRoles.length; i++) ...{
            'role[$i]': userRoleViewString(userRoles[i]),
          },
        },
        if (users != null && users.isNotEmpty) ...{
          for (int i = 0; i < users.length; i++) ...{
            'user_id[$i]': users[i].id.toString(),
          },
        },
        if (projects != null && projects.isNotEmpty) ...{
          for (int i = 0; i < projects.length; i++) ...{
            'project_id[$i]': projects[i].id.toString(),
          },
        },
      },
    );

    return response.parseResponse(DashboardResponse.fromJson);
  }

  @override
  ApiResult<Uint8List> exportDashboard({
    required DateTime startDate,
    required DateTime endDate,
    List<String>? userRoles,
    List<UserModel>? users,
    List<ProjectModel>? projects,
  }) async {
    final response = await client.getFile(
      url: AppStrings.exportDashboard,
      params: {
        'start_date': DateFormat('yyyy-MM-dd').format(startDate),
        'end_date': DateFormat('yyyy-MM-dd').format(endDate),
        if (userRoles != null && userRoles.isNotEmpty && !userRoles.contains('All users')) ...{
          for (int i = 0; i < userRoles.length; i++) ...{
            'role[$i]': userRoleViewString(userRoles[i]),
          },
        },
        if (users != null && users.isNotEmpty) ...{
          for (int i = 0; i < users.length; i++) ...{
            'user_id[$i]': users[i].id.toString(),
          },
        },
        if (projects != null && projects.isNotEmpty) ...{
          for (int i = 0; i < projects.length; i++) ...{
            'project_id[$i]': projects[i].id.toString(),
          },
        },
      },
    );

    return response.fold(Left.new, Right.new);
  }
}

String userRoleViewString(String userRoles) {
  switch (userRoles) {
    case AppConstants.doctor1:
      return AppConstants.doctorOne;
    case AppConstants.doctor2:
      return AppConstants.doctorTwo;
    case AppConstants.firstYearResident:
      return AppConstants.firstYear;
    case AppConstants.secondYearResident:
      return AppConstants.secondYear;
    case AppConstants.thirdYearResident:
      return AppConstants.thirdYear;
    case AppConstants.lastYearResident:
      return AppConstants.lastYear;
    default:
      return AppConstants.student;
  }
}
