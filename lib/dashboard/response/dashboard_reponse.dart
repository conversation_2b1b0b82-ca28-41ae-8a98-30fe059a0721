import 'package:equatable/equatable.dart';
import 'package:excel_app/dashboard/model/dashboard_model.dart';

class DashboardResponse extends Equatable {
  const DashboardResponse({
    this.message,
    this.totalAvailableDays,
    this.totalNonAvailableDays,
    this.weekendsPresent,
    this.weekdaysPresent,
    this.fridaysPresent,
    this.data = const [],
    this.status,
  });

  factory DashboardResponse.fromJson(Map<String, dynamic> json) {
    return DashboardResponse(
      message: json['message'] as String?,
      totalAvailableDays: json['total_Available_days'] as int?,
      totalNonAvailableDays: json['total_non_Available_days'] as int?,
      weekendsPresent: json['Weekends_present'] as int?,
      weekdaysPresent: json['Weekdays_present'] as int?,
      fridaysPresent: json['fridays_present'] as int?,
      data: json['data'] == null
          ? []
          : List<DashboardModel>.from(
              (json['data'] as List<dynamic>).map(
                (e) => DashboardModel.fromJson(e as Map<String, dynamic>),
              ),
            ),
      status: json['status'] as String?,
    );
  }

  final String? message;
  final int? totalAvailableDays;
  final int? totalNonAvailableDays;
  final int? weekendsPresent;
  final int? weekdaysPresent;
  final int? fridaysPresent;
  final List<DashboardModel> data;
  final String? status;

  Map<String, dynamic> toJson() => {
        'message': message,
        'total_Available_days': totalAvailableDays,
        'total_non_Available_days': totalNonAvailableDays,
        'Weekends_present': weekendsPresent,
        'Weekdays_present': weekdaysPresent,
        'fridays_present': fridaysPresent,
        'data': data.map((x) => x.toJson()).toList(),
        'status': status,
      };

  @override
  List<Object?> get props => [
        message,
        totalAvailableDays,
        totalNonAvailableDays,
        weekendsPresent,
        weekdaysPresent,
        fridaysPresent,
        data,
        status,
      ];
}
