import 'package:easy_debounce/easy_debounce.dart';
import 'package:excel_app/constants/app_assets.dart';
import 'package:excel_app/constants/app_colors.dart';
import 'package:excel_app/dashboard/model/user_availability_summary_model.dart';
import 'package:excel_app/dashboard/widget/dashboard_data_view.dart';
import 'package:excel_app/dashboard/widget/user_availability_table.dart';
import 'package:excel_app/l10n/l10n_extension.dart';
import 'package:excel_app/projects/model/project_model.dart';
import 'package:excel_app/users/model/user_model.dart';
import 'package:excel_app/widget/app_asset_image.dart';
import 'package:excel_app/widget/app_text_form_field.dart';
import 'package:excel_app/widget/container_widget.dart';
import 'package:excel_app/widget/custome_button_gradiant_widget.dart';
import 'package:excel_app/widget/dialogs.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';

class DashboardPage extends StatefulWidget {
  const DashboardPage({super.key});

  @override
  State<DashboardPage> createState() => _DashboardPageState();
}

class _DashboardPageState extends State<DashboardPage> {
  final selectDuration = ValueNotifier<String?>(null);
  final selectProject = ValueNotifier<ProjectModel>(const ProjectModel());
  final selectUser = ValueNotifier<UserModel?>(const UserModel());
  final searchController = TextEditingController();

  // Demo data for user availability table
  final userAvailabilityData = UserAvailabilitySummaryModel.getDemoData();

  // Helper method to build filter rows
  Widget _buildFilterRow({
    required String label,
    required Widget child,
  }) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          label,
          style: Theme.of(context).textTheme.bodyMedium,
        ),
        const Gap(10),
        child,
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ContainerWidget(
              margin: const EdgeInsets.fromLTRB(25, 25, 25, 0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    context.l10n.dashboard,
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.w600),
                  ),
                  const Gap(20),
                  LayoutBuilder(
                    builder: (context, constraints) {
                      // Check if screen is small (mobile/tablet)
                      final isSmallScreen = constraints.maxWidth < 1000;

                      if (isSmallScreen) {
                        // For small screens, use Column layout
                        return Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Filters in column layout
                            _buildFilterRow(
                              label: 'Show',
                              child: ValueListenableBuilder<String?>(
                                valueListenable: selectDuration,
                                builder: (context, duration, _) {
                                  return AppTextFormField(
                                    controller: TextEditingController(text: duration ?? ''),
                                    maxHeight: 32,
                                    contentPadding: const EdgeInsets.symmetric(vertical: 6, horizontal: 14),
                                    readOnly: true,
                                    suffixIcon: const Padding(
                                      padding: EdgeInsets.all(11),
                                      child: AppAssetImage(
                                        AppAssets.arrowDownIcon,
                                        height: 18,
                                        width: 18,
                                      ),
                                    ),
                                    onTap: () async {
                                      final selectedDuration = await DailogBox.selectDurationDailog(
                                        context,
                                        selectedDuration: selectDuration.value,
                                      );
                                      if (selectedDuration != null) {
                                        selectDuration.value = selectedDuration;
                                      }
                                    },
                                  );
                                },
                              ),
                            ),
                            const Gap(16),
                            _buildFilterRow(
                              label: 'Project',
                              child: ValueListenableBuilder<ProjectModel>(
                                valueListenable: selectProject,
                                builder: (context, selected, _) {
                                  return AppTextFormField(
                                    controller: TextEditingController(text: selected.name ?? ''),
                                    maxHeight: 32,
                                    contentPadding: const EdgeInsets.symmetric(vertical: 6, horizontal: 14),
                                    readOnly: true,
                                    suffixIcon: const Padding(
                                      padding: EdgeInsets.all(11),
                                      child: AppAssetImage(
                                        AppAssets.arrowDownIcon,
                                        height: 18,
                                        width: 18,
                                      ),
                                    ),
                                    onTap: () async {
                                      final selectedProject = await DailogBox.selectProjectDailog(
                                        context,
                                        selectedProject: selected,
                                      );
                                      if (selectedProject != null) {
                                        selectProject.value = selectedProject;
                                      }
                                    },
                                  );
                                },
                              ),
                            ),
                            const Gap(16),
                            _buildFilterRow(
                              label: 'User Role',
                              child: ValueListenableBuilder<UserModel?>(
                                valueListenable: selectUser,
                                builder: (context, user, _) {
                                  return AppTextFormField(
                                    controller: TextEditingController(text: user?.name ?? ''),
                                    maxHeight: 32,
                                    contentPadding: const EdgeInsets.symmetric(vertical: 6, horizontal: 14),
                                    readOnly: true,
                                    suffixIcon: const Padding(
                                      padding: EdgeInsets.all(11),
                                      child: AppAssetImage(
                                        AppAssets.arrowDownIcon,
                                        height: 18,
                                        width: 18,
                                      ),
                                    ),
                                    onTap: () async {
                                      final selectedUser = await DailogBox.selectUserDailog(
                                        context,
                                        selectedUser: selectUser.value,
                                      );
                                      if (selectedUser != null) {
                                        selectUser.value = selectedUser;
                                      }
                                    },
                                  );
                                },
                              ),
                            ),
                            const Gap(16),
                            _buildFilterRow(
                              label: 'User',
                              child: ValueListenableBuilder<UserModel?>(
                                valueListenable: selectUser,
                                builder: (context, user, _) {
                                  return AppTextFormField(
                                    controller: TextEditingController(text: user?.name ?? ''),
                                    maxHeight: 32,
                                    contentPadding: const EdgeInsets.symmetric(vertical: 6, horizontal: 14),
                                    readOnly: true,
                                    suffixIcon: const Padding(
                                      padding: EdgeInsets.all(11),
                                      child: AppAssetImage(
                                        AppAssets.arrowDownIcon,
                                        height: 18,
                                        width: 18,
                                      ),
                                    ),
                                    onTap: () async {
                                      final selectedUser = await DailogBox.selectUserDailog(
                                        context,
                                        selectedUser: selectUser.value,
                                      );
                                      if (selectedUser != null) {
                                        selectUser.value = selectedUser;
                                      }
                                    },
                                  );
                                },
                              ),
                            ),
                            const Gap(20),
                            // Export button
                            Align(
                              alignment: Alignment.centerRight,
                              child: CustomeButtonGradiantWidget(
                                isGradient: true,
                                width: 90,
                                height: 32,
                                child: Text(
                                  'Export',
                                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                                        fontSize: 15,
                                        color: AppColors.white,
                                      ),
                                ),
                                onTap: () {},
                              ),
                            ),
                          ],
                        );
                      } else {
                        // For larger screens, use Row layout with Wrap
                        return Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Flexible(
                              child: Wrap(
                                spacing: 20,
                                runSpacing: 20,
                                children: [
                                  _buildFilterRow(
                                    label: 'Show',
                                    child: ValueListenableBuilder<String?>(
                                      valueListenable: selectDuration,
                                      builder: (context, duration, _) {
                                        return AppTextFormField(
                                          controller: TextEditingController(text: duration ?? ''),
                                          maxWidth: 180,
                                          maxHeight: 32,
                                          contentPadding: const EdgeInsets.symmetric(vertical: 6, horizontal: 14),
                                          readOnly: true,
                                          suffixIcon: const Padding(
                                            padding: EdgeInsets.all(11),
                                            child: AppAssetImage(
                                              AppAssets.arrowDownIcon,
                                              height: 18,
                                              width: 18,
                                            ),
                                          ),
                                          onTap: () async {
                                            final selectedDuration = await DailogBox.selectDurationDailog(
                                              context,
                                              selectedDuration: selectDuration.value,
                                            );
                                            if (selectedDuration != null) {
                                              selectDuration.value = selectedDuration;
                                            }
                                          },
                                        );
                                      },
                                    ),
                                  ),
                                  _buildFilterRow(
                                    label: 'Project',
                                    child: ValueListenableBuilder<ProjectModel>(
                                      valueListenable: selectProject,
                                      builder: (context, selected, _) {
                                        return AppTextFormField(
                                          controller: TextEditingController(text: selected.name ?? ''),
                                          maxWidth: 180,
                                          maxHeight: 32,
                                          contentPadding: const EdgeInsets.symmetric(vertical: 6, horizontal: 14),
                                          readOnly: true,
                                          suffixIcon: const Padding(
                                            padding: EdgeInsets.all(11),
                                            child: AppAssetImage(
                                              AppAssets.arrowDownIcon,
                                              height: 18,
                                              width: 18,
                                            ),
                                          ),
                                          onTap: () async {
                                            final selectedProject = await DailogBox.selectProjectDailog(
                                              context,
                                              selectedProject: selected,
                                            );
                                            if (selectedProject != null) {
                                              selectProject.value = selectedProject;
                                            }
                                          },
                                        );
                                      },
                                    ),
                                  ),
                                  _buildFilterRow(
                                    label: 'User Role',
                                    child: ValueListenableBuilder<ProjectModel>(
                                      valueListenable: selectProject,
                                      builder: (context, selected, _) {
                                        return AppTextFormField(
                                          controller: TextEditingController(text: selected.name ?? ''),
                                          maxWidth: 180,
                                          maxHeight: 32,
                                          contentPadding: const EdgeInsets.symmetric(vertical: 6, horizontal: 14),
                                          readOnly: true,
                                          suffixIcon: const Padding(
                                            padding: EdgeInsets.all(11),
                                            child: AppAssetImage(
                                              AppAssets.arrowDownIcon,
                                              height: 18,
                                              width: 18,
                                            ),
                                          ),
                                          onTap: () async {
                                            final selectedUser = await DailogBox.selectUserDailog(
                                              context,
                                              selectedUser: selectUser.value,
                                            );
                                            if (selectedUser != null) {
                                              selectUser.value = selectedUser;
                                            }
                                          },
                                        );
                                      },
                                    ),
                                  ),
                                  _buildFilterRow(
                                    label: 'User',
                                    child: ValueListenableBuilder<UserModel?>(
                                      valueListenable: selectUser,
                                      builder: (context, user, _) {
                                        return AppTextFormField(
                                          controller: TextEditingController(text: user?.name ?? ''),
                                          maxWidth: 180,
                                          maxHeight: 32,
                                          contentPadding: const EdgeInsets.symmetric(vertical: 6, horizontal: 14),
                                          readOnly: true,
                                          suffixIcon: const Padding(
                                            padding: EdgeInsets.all(11),
                                            child: AppAssetImage(
                                              AppAssets.arrowDownIcon,
                                              height: 18,
                                              width: 18,
                                            ),
                                          ),
                                          onTap: () async {
                                            final selectedUser = await DailogBox.selectUserDailog(
                                              context,
                                              selectedUser: selectUser.value,
                                            );
                                            if (selectedUser != null) {
                                              selectUser.value = selectedUser;
                                            }
                                          },
                                        );
                                      },
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            const Gap(20),
                            CustomeButtonGradiantWidget(
                              isGradient: true,
                              width: 90,
                              height: 32,
                              child: Text(
                                'Export',
                                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                                      fontSize: 15,
                                      color: AppColors.white,
                                    ),
                              ),
                              onTap: () {},
                            ),
                          ],
                        );
                      }
                    },
                  ),
                ],
              ),
            ),
            const Gap(20),
            const Padding(
              padding: EdgeInsets.symmetric(horizontal: 25),
              child: Wrap(
                spacing: 20,
                runSpacing: 20,
                children: [
                  DashboardDataView(count: '60', title: 'Total Available\nDays'),
                  DashboardDataView(count: '30', title: 'Total Non available\nDays'),
                  DashboardDataView(count: '10', title: 'Weekend\nPresent'),
                  DashboardDataView(count: '5', title: 'Friday\nPresent'),
                  DashboardDataView(count: '7', title: 'Weekdays\nPresent'),
                ],
              ),
            ),
            const Gap(20),
            ContainerWidget(
              margin: const EdgeInsets.symmetric(horizontal: 25),
              child: Column(
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          'User Availability',
                          style: Theme.of(context)
                              .textTheme
                              .titleLarge
                              ?.copyWith(fontSize: 18, fontWeight: FontWeight.w600),
                        ),
                      ),
                      AppTextFormField(
                        maxWidth: 200,
                        maxHeight: 32,
                        controller: searchController,
                        prefixIcon: AppAssets.searchIcon,
                        onChanged: (p0) {
                          EasyDebounce.debounce(
                            'search',
                            const Duration(milliseconds: 800),
                            () {},
                          );
                        },
                        hintText: 'Search',
                      ),
                    ],
                  ),
                  const Gap(20),
                  // Add the user availability table here
                  UserAvailabilityTable(data: userAvailabilityData),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
