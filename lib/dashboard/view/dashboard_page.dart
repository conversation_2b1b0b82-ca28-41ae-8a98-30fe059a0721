import 'dart:async';
import 'dart:developer';
import 'dart:html' as html;

import 'package:easy_debounce/easy_debounce.dart';
import 'package:excel_app/constants/app_assets.dart';
import 'package:excel_app/constants/app_colors.dart';
import 'package:excel_app/dashboard/model/dashboard_model.dart';
import 'package:excel_app/dashboard/repository/i_dashboard_repository.dart';
import 'package:excel_app/dashboard/response/dashboard_reponse.dart';
import 'package:excel_app/dashboard/widget/custom_duration_dialog.dart';
import 'package:excel_app/dashboard/widget/dashboard_data_view.dart';
import 'package:excel_app/data_table/data_table.dart';
import 'package:excel_app/injector/injector.dart';
import 'package:excel_app/l10n/l10n_extension.dart';
import 'package:excel_app/projects/model/project_model.dart';
import 'package:excel_app/users/model/user_model.dart';
import 'package:excel_app/utility/extentions/string_extentions.dart';
import 'package:excel_app/utility/helpers/utility.dart';
import 'package:excel_app/utility/pagination_mixin.dart';
import 'package:excel_app/widget/app_asset_image.dart';
import 'package:excel_app/widget/app_text_form_field.dart';
import 'package:excel_app/widget/container_widget.dart';
import 'package:excel_app/widget/custome_button_gradiant_widget.dart';
import 'package:excel_app/widget/dialogs.dart';
import 'package:excel_app/widget/no_data_available_widget.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:visibility_detector/visibility_detector.dart';

class DashboardPage extends StatefulWidget {
  const DashboardPage({super.key});

  @override
  State<DashboardPage> createState() => _DashboardPageState();
}

class _DashboardPageState extends State<DashboardPage> with PaginatisonMixin {
  final selectDuration = ValueNotifier<SelectDurationModel>(SelectDurationModel(selectedDuration: 'Today'));
  final selectProject = ValueNotifier<List<ProjectModel>>(const []);
  final selectUserRole = ValueNotifier<List<String>>([]);
  final selectUser = ValueNotifier<List<UserModel>>([]);
  final searchController = TextEditingController();
  final FocusNode searchFocusNode = FocusNode();

  final isLoading = ValueNotifier<bool>(false);
  final isLoadingMore = ValueNotifier<bool>(false);
  final isLoadingExport = ValueNotifier<bool>(false);

  final dashboard = ValueNotifier<DashboardResponse>(const DashboardResponse());
  final dashboardList = ValueNotifier<List<DashboardModel>>([]);

  int page = 0;
  bool stopPagination = false;

  DateTime? startDate;
  DateTime? endDate;

  @override
  void initState() {
    initiatePagination();
    getDashboardDetails();
    _requestFocus();
    super.initState();
  }

  // Helper method to build filter rows
  Widget _buildFilterRow({
    required String label,
    required Widget child,
  }) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          label,
          style: Theme.of(context).textTheme.bodyMedium,
        ),
        const Gap(10),
        child,
      ],
    );
  }

  // Helper method to get user roles display text
  String _getUserRolesDisplayText(List<String> roles) {
    if (roles.isEmpty) {
      return 'All Roles';
    } else if (roles.length == 1) {
      return roles.first;
    } else if (roles.length <= 3) {
      return roles.join(', ');
    } else {
      return '${roles.take(2).join(', ')} +${roles.length - 2} more';
    }
  }

  void _refresh() {
    log('test ______');
    page = 0;
    dashboard.value = const DashboardResponse();
    dashboardList.value = [];
    log('${page}page ______________________');
    setState(() {});
    getDashboardDetails();
  }

  Future<void> getDashboardDetails() async {
    if (isLoading.value || isLoadingMore.value) return;
    if (page == 0) {
      isLoading.value = true;
    } else {
      isLoadingMore.value = true;
    }
    page += 1;

    final failOrSucess = await getIt<IDashboardRepository>().getDashboard(
      page: page,
      startDate: startDate ?? DateTime.now(),
      endDate: endDate ?? DateTime.now(),
      userRoles: selectUserRole.value.isNotEmpty ? selectUserRole.value : null,
      users: selectUser.value.isNotEmpty ? selectUser.value : null,
      projects: selectProject.value.isNotEmpty ? selectProject.value : null,
      search: searchController.text.trim(),
    );
    failOrSucess.fold(
      (l) {
        isLoading.value = false;
        isLoadingMore.value = false;
      },
      (r) {
        stopPagination = r.data.length < 20;
        dashboard.value = r;
        dashboardList.value = [...dashboardList.value, ...r.data];
        isLoading.value = false;
        isLoadingMore.value = false;
      },
    );
  }

  Future<void> exportDashboard() async {
    isLoadingExport.value = true;
    final failOrSucess = await getIt<IDashboardRepository>().exportDashboard(
      startDate: startDate ?? DateTime.now(),
      endDate: endDate ?? DateTime.now(),
      userRoles: selectUserRole.value.isNotEmpty ? selectUserRole.value : null,
      users: selectUser.value.isNotEmpty ? selectUser.value : null,
      projects: selectProject.value.isNotEmpty ? selectProject.value : null,
    );
    failOrSucess.fold(
      (l) {
        isLoadingExport.value = false;
        Utility.toast(message: l.message);
      },
      (r) {
        final blob = html.Blob([r]);
        final url = html.Url.createObjectUrlFromBlob(blob);
        final anchor = html.AnchorElement(href: url)
          ..setAttribute('download', 'dashboard.xlsx')
          ..click();
        html.Url.revokeObjectUrl(url);
        isLoadingExport.value = false;
        Utility.toast(message: 'Dashboard exported successfully');
      },
    );
  }

  @override
  void dispose() {
    disposePagination();
    super.dispose();
  }

  @override
  void onReachedLast() {
    log('test onreached last');
    if (stopPagination || isLoadingMore.value || isLoading.value) return;
    EasyDebounce.debounce('Select__User_Pagination', const Duration(milliseconds: 500), getDashboardDetails);
  }

  void _requestFocus() {
    // Use a timer to ensure the widget is fully built and visible
    Timer(const Duration(milliseconds: 50), () {
      if (mounted && searchFocusNode.canRequestFocus) {
        searchFocusNode.requestFocus();
        // Add another small delay to ensure focus is established before positioning cursor
        Timer(const Duration(milliseconds: 50), () {
          if (mounted && searchController.text.isNotEmpty) {
            // Position cursor at the end of text (no selection/highlighting)
            searchController.selection = TextSelection.fromPosition(
              TextPosition(offset: searchController.text.length),
            );
          }
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SingleChildScrollView(
        child: ValueListenableBuilder(
          valueListenable: isLoading,
          builder: (context, loading, _) {
            if (loading) {
              return ContainerWidget(child: Utility.progressIndicator());
            }
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                VisibilityDetector(
                  key: const Key('dashboard-page-visibility'),
                  onVisibilityChanged: (info) {
                    // When page becomes visible (tab switch), request focus
                    if (info.visibleFraction > 0.1) {
                      _requestFocus();
                    }
                  },
                  child: ContainerWidget(
                    margin: const EdgeInsets.fromLTRB(25, 25, 25, 0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          context.l10n.dashboard,
                          style: Theme.of(context).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.w600),
                        ),
                        const Gap(20),
                        LayoutBuilder(
                          builder: (context, constraints) {
                            // Check if screen is small (mobile/tablet)
                            final isSmallScreen = constraints.maxWidth < 1000;

                            if (isSmallScreen) {
                              // For small screens, use Column layout
                              return Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  // Filters in column layout
                                  _buildFilterRow(
                                    label: 'Show',
                                    child: ValueListenableBuilder<SelectDurationModel>(
                                      valueListenable: selectDuration,
                                      builder: (context, duration, _) {
                                        return AppTextFormField(
                                          controller: TextEditingController(text: duration.selectedDuration ?? ''),
                                          maxHeight: 32,
                                          contentPadding: const EdgeInsets.symmetric(vertical: 6, horizontal: 14),
                                          readOnly: true,
                                          suffixIcon: const Padding(
                                            padding: EdgeInsets.all(11),
                                            child: AppAssetImage(
                                              AppAssets.arrowDownIcon,
                                              height: 18,
                                              width: 18,
                                            ),
                                          ),
                                          onTap: () async {
                                            final selectedDuration = await DailogBox.selectDurationDailog(
                                              context,
                                              selectedDuration: selectDuration.value,
                                            );
                                            if (selectedDuration != null) {
                                              selectDuration.value = selectedDuration;
                                              if (selectedDuration.startDate != null &&
                                                  selectedDuration.endDate != null) {
                                                dashboard.value = const DashboardResponse();
                                                dashboardList.value = [];
                                                startDate = selectedDuration.startDate;
                                                endDate = selectedDuration.endDate;
                                                _refresh();
                                              }
                                            }
                                          },
                                        );
                                      },
                                    ),
                                  ),
                                  const Gap(16),
                                  _buildFilterRow(
                                    label: 'Project',
                                    child: ValueListenableBuilder<List<ProjectModel>>(
                                      valueListenable: selectProject,
                                      builder: (context, selected, _) {
                                        return AppTextFormField(
                                          controller:
                                              TextEditingController(text: selected.map((e) => e.name).join(', ')),
                                          maxHeight: 32,
                                          contentPadding: const EdgeInsets.symmetric(vertical: 6, horizontal: 14),
                                          readOnly: true,
                                          suffixIcon: const Padding(
                                            padding: EdgeInsets.all(11),
                                            child: AppAssetImage(
                                              AppAssets.arrowDownIcon,
                                              height: 18,
                                              width: 18,
                                            ),
                                          ),
                                          onTap: () async {
                                            final selectedProject = await DailogBox.selectMultipleProjectDailog(
                                              context,
                                              selectedProject: selected,
                                            );
                                            if (selectedProject != null) {
                                              selectProject.value = selectedProject;
                                              // ignore: unnecessary_statements
                                              _refresh;
                                            }
                                          },
                                        );
                                      },
                                    ),
                                  ),
                                  const Gap(16),
                                  _buildFilterRow(
                                    label: 'User Role',
                                    child: ValueListenableBuilder<List<String>>(
                                      valueListenable: selectUserRole,
                                      builder: (context, selectedRoles, _) {
                                        return AppTextFormField(
                                          controller: TextEditingController(
                                            text: _getUserRolesDisplayText(selectedRoles),
                                          ),
                                          maxHeight: 32,
                                          contentPadding: const EdgeInsets.symmetric(vertical: 6, horizontal: 14),
                                          readOnly: true,
                                          suffixIcon: const Padding(
                                            padding: EdgeInsets.all(11),
                                            child: AppAssetImage(
                                              AppAssets.arrowDownIcon,
                                              height: 18,
                                              width: 18,
                                            ),
                                          ),
                                          onTap: () async {
                                            final selectedUserRoles = await DailogBox.simpleUserRoleDialog(
                                              context,
                                              selectedRoles: selectUserRole.value,
                                              title: 'Select User Roles',
                                            );
                                            if (selectedUserRoles != null) {
                                              selectUserRole.value = selectedUserRoles;
                                              _refresh();
                                            }
                                          },
                                        );
                                      },
                                    ),
                                  ),
                                  const Gap(16),
                                  _buildFilterRow(
                                    label: 'User',
                                    child: ValueListenableBuilder<List<UserModel>>(
                                      valueListenable: selectUser,
                                      builder: (context, user, _) {
                                        return AppTextFormField(
                                          controller:
                                              TextEditingController(text: user.isNotEmpty ? user.first.name : ''),
                                          maxHeight: 32,
                                          contentPadding: const EdgeInsets.symmetric(vertical: 6, horizontal: 14),
                                          readOnly: true,
                                          suffixIcon: const Padding(
                                            padding: EdgeInsets.all(11),
                                            child: AppAssetImage(
                                              AppAssets.arrowDownIcon,
                                              height: 18,
                                              width: 18,
                                            ),
                                          ),
                                          onTap: () async {
                                            final selectedUser = await DailogBox.multipleSelectUserDailog(
                                              context,
                                              selectedUser: selectUser.value,
                                            );
                                            if (selectedUser != null) {
                                              selectUser.value = selectedUser;
                                              _refresh();
                                            }
                                          },
                                        );
                                      },
                                    ),
                                  ),
                                  const Gap(20),
                                  Align(
                                    alignment: Alignment.centerRight,
                                    child: ValueListenableBuilder<bool>(
                                      valueListenable: isLoadingExport,
                                      builder: (context, loading, _) {
                                        return CustomeButtonGradiantWidget(
                                          isGradient: true,
                                          width: 90,
                                          height: 32,
                                          isLoading: loading,
                                          onTap: () {
                                            EasyDebounce.debounce(
                                              'exportDashboard',
                                              const Duration(milliseconds: 500),
                                              exportDashboard,
                                            );
                                          },
                                          child: Text(
                                            'Export',
                                            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                                                  fontSize: 15,
                                                  color: AppColors.white,
                                                ),
                                          ),
                                        );
                                      },
                                    ),
                                  ),
                                ],
                              );
                            } else {
                              // For larger screens, use Row layout with Wrap
                              return Row(
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                children: [
                                  Flexible(
                                    child: Wrap(
                                      spacing: 20,
                                      runSpacing: 20,
                                      children: [
                                        _buildFilterRow(
                                          label: 'Show',
                                          child: ValueListenableBuilder<SelectDurationModel>(
                                            valueListenable: selectDuration,
                                            builder: (context, duration, _) {
                                              return AppTextFormField(
                                                controller:
                                                    TextEditingController(text: duration.selectedDuration ?? ''),
                                                maxWidth: 180,
                                                maxHeight: 32,
                                                contentPadding: const EdgeInsets.symmetric(vertical: 6, horizontal: 14),
                                                readOnly: true,
                                                suffixIcon: const Padding(
                                                  padding: EdgeInsets.all(11),
                                                  child: AppAssetImage(
                                                    AppAssets.arrowDownIcon,
                                                    height: 18,
                                                    width: 18,
                                                  ),
                                                ),
                                                onTap: () async {
                                                  final selectedDuration = await DailogBox.selectDurationDailog(
                                                    context,
                                                    selectedDuration: selectDuration.value,
                                                  );
                                                  if (selectedDuration != null) {
                                                    selectDuration.value = selectedDuration;
                                                    if (selectedDuration.startDate != null &&
                                                        selectedDuration.endDate != null) {
                                                      startDate = selectedDuration.startDate;
                                                      endDate = selectedDuration.endDate;
                                                      _refresh();
                                                    }
                                                  }
                                                },
                                              );
                                            },
                                          ),
                                        ),
                                        _buildFilterRow(
                                          label: 'Project',
                                          child: ValueListenableBuilder<List<ProjectModel>>(
                                            valueListenable: selectProject,
                                            builder: (context, selected, _) {
                                              return AppTextFormField(
                                                controller:
                                                    TextEditingController(text: selected.map((e) => e.name).join(', ')),
                                                maxWidth: 180,
                                                maxHeight: 32,
                                                contentPadding: const EdgeInsets.symmetric(vertical: 6, horizontal: 14),
                                                readOnly: true,
                                                suffixIcon: const Padding(
                                                  padding: EdgeInsets.all(11),
                                                  child: AppAssetImage(
                                                    AppAssets.arrowDownIcon,
                                                    height: 18,
                                                    width: 18,
                                                  ),
                                                ),
                                                onTap: () async {
                                                  final selectedProject = await DailogBox.selectMultipleProjectDailog(
                                                    context,
                                                    selectedProject: selected,
                                                  );
                                                  if (selectedProject != null) {
                                                    selectProject.value = selectedProject;
                                                    _refresh();
                                                  }
                                                },
                                              );
                                            },
                                          ),
                                        ),
                                        _buildFilterRow(
                                          label: 'User Role',
                                          child: ValueListenableBuilder<List<String>>(
                                            valueListenable: selectUserRole,
                                            builder: (context, selectedRoles, _) {
                                              return AppTextFormField(
                                                controller: TextEditingController(
                                                  text: _getUserRolesDisplayText(selectedRoles),
                                                ),
                                                maxWidth: 180,
                                                maxHeight: 32,
                                                contentPadding: const EdgeInsets.symmetric(vertical: 6, horizontal: 14),
                                                readOnly: true,
                                                suffixIcon: const Padding(
                                                  padding: EdgeInsets.all(11),
                                                  child: AppAssetImage(
                                                    AppAssets.arrowDownIcon,
                                                    height: 18,
                                                    width: 18,
                                                  ),
                                                ),
                                                onTap: () async {
                                                  final selectedUserRoles = await DailogBox.simpleUserRoleDialog(
                                                    context,
                                                    selectedRoles: selectUserRole.value,
                                                    title: 'Select User Roles',
                                                  );
                                                  if (selectedUserRoles != null) {
                                                    selectUserRole.value = selectedUserRoles;
                                                    _refresh();
                                                  }
                                                },
                                              );
                                            },
                                          ),
                                        ),
                                        _buildFilterRow(
                                          label: 'User',
                                          child: ValueListenableBuilder<List<UserModel>>(
                                            valueListenable: selectUser,
                                            builder: (context, user, _) {
                                              return AppTextFormField(
                                                controller: TextEditingController(
                                                  text: user.isNotEmpty ? user.map((e) => e.name).join(', ') : '',
                                                ),
                                                maxWidth: 180,
                                                maxHeight: 32,
                                                contentPadding: const EdgeInsets.symmetric(vertical: 6, horizontal: 14),
                                                readOnly: true,
                                                suffixIcon: const Padding(
                                                  padding: EdgeInsets.all(11),
                                                  child: AppAssetImage(
                                                    AppAssets.arrowDownIcon,
                                                    height: 18,
                                                    width: 18,
                                                  ),
                                                ),
                                                onTap: () async {
                                                  final selectedUser = await DailogBox.multipleSelectUserDailog(
                                                    context,
                                                    selectedUser: selectUser.value,
                                                  );
                                                  if (selectedUser != null) {
                                                    selectUser.value = selectedUser;
                                                    _refresh();
                                                  }
                                                },
                                              );
                                            },
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                  const Gap(20),
                                  ValueListenableBuilder(
                                    valueListenable: isLoadingExport,
                                    builder: (context, loading, _) {
                                      return CustomeButtonGradiantWidget(
                                        isGradient: true,
                                        width: 90,
                                        height: 32,
                                        isLoading: loading,
                                        onTap: () {
                                          EasyDebounce.debounce(
                                            'exportDashboard',
                                            const Duration(milliseconds: 500),
                                            exportDashboard,
                                          );
                                        },
                                        child: Text(
                                          'Export',
                                          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                                                fontSize: 15,
                                                color: AppColors.white,
                                              ),
                                        ),
                                      );
                                    },
                                  ),
                                ],
                              );
                            }
                          },
                        ),
                      ],
                    ),
                  ),
                ),
                const Gap(20),
                ValueListenableBuilder(
                  valueListenable: dashboard,
                  builder: (context, dashboardValue, _) {
                    return Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 25),
                      child: Wrap(
                        spacing: 20,
                        runSpacing: 20,
                        children: [
                          DashboardDataView(
                            count: dashboardValue.totalAvailableDays != null
                                ? dashboardValue.totalAvailableDays.toString()
                                : '',
                            title: 'Total Available\nDays',
                          ),
                          DashboardDataView(
                            count: dashboardValue.totalNonAvailableDays != null
                                ? dashboardValue.totalNonAvailableDays.toString()
                                : '',
                            title: 'Total Non available\nDays',
                          ),
                          DashboardDataView(
                            count:
                                dashboardValue.weekendsPresent != null ? dashboardValue.weekendsPresent.toString() : '',
                            title: 'Weekend\nShift',
                          ),
                          DashboardDataView(
                            count:
                                dashboardValue.fridaysPresent != null ? dashboardValue.fridaysPresent.toString() : '',
                            title: 'Friday\nShift',
                          ),
                          DashboardDataView(
                            count:
                                dashboardValue.weekdaysPresent != null ? dashboardValue.weekdaysPresent.toString() : '',
                            title: 'Weekdays\nShift',
                          ),
                        ],
                      ),
                    );
                  },
                ),
                const Gap(20),
                ContainerWidget(
                  margin: const EdgeInsets.symmetric(horizontal: 25),
                  child: Column(
                    children: [
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              'User Availability',
                              style: Theme.of(context)
                                  .textTheme
                                  .titleLarge
                                  ?.copyWith(fontSize: 18, fontWeight: FontWeight.w600),
                            ),
                          ),
                          ValueListenableBuilder(
                            valueListenable: dashboardList,
                            builder: (context, list, _) {
                              if (list.isEmpty && searchController.text.trim().isEmpty) {
                                return const SizedBox.shrink();
                              }
                              return AppTextFormField(
                                maxWidth: 200,
                                maxHeight: 32,
                                controller: searchController,
                                focusNode: searchFocusNode,
                                prefixIcon: AppAssets.searchIcon,
                                onChanged: (p0) {
                                  EasyDebounce.debounce(
                                    'search',
                                    const Duration(milliseconds: 800),
                                    _refresh,
                                  );
                                },
                                hintText: 'Search',
                              );
                            },
                          ),
                        ],
                      ),
                      const Gap(20),
                      // Add the user availability table here with proper search data
                      ValueListenableBuilder(
                        valueListenable: dashboardList,
                        builder: (context, list, _) {
                          final dataToShow = list;
                          // Show "No data" message when search returns empty results
                          if (!loading && dataToShow.isEmpty && searchController.text.trim().isEmpty) {
                            return const Center(
                              child: NoDataAvailableWidget(
                                message: 'No User Available',
                              ),
                            );
                          }
                          if (searchController.text.trim().isNotEmpty && list.isEmpty) {
                            return const NoDataAvailableWidget(
                              message: 'No User Available',
                            );
                          }
                          return SizedBox(
                            height: MediaQuery.of(context).size.height * 0.58,
                            child: ValueListenableBuilder(
                              valueListenable: isLoadingMore,
                              builder: (context, loadingMore, _) {
                                return VisibilityDetector(
                                  key: const Key('dashboard-page'),
                                  onVisibilityChanged: (info) {
                                    if (info.visibleFraction > 0.5) {
                                      onReachedLast();
                                    }
                                  },
                                  child: CustomDataTable(
                                    scrollController: scrollPaginationController,
                                    isPageLoading: loadingMore,
                                    columns: [
                                      DataColumn(
                                        label: Text(
                                          'User Name',
                                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                                fontWeight: FontWeight.w500,
                                                color: AppColors.subText,
                                              ),
                                        ),
                                      ),
                                      DataColumn(
                                        label: Text(
                                          'Role',
                                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                                fontWeight: FontWeight.w500,
                                                color: AppColors.subText,
                                              ),
                                        ),
                                      ),
                                      DataColumn(
                                        label: Text(
                                          'Project',
                                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                                fontWeight: FontWeight.w500,
                                                color: AppColors.subText,
                                              ),
                                        ),
                                      ),
                                      DataColumn(
                                        label: Text(
                                          'Total Available\nDays',
                                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                                fontWeight: FontWeight.w500,
                                                color: AppColors.subText,
                                              ),
                                        ),
                                      ),
                                      DataColumn(
                                        label: Text(
                                          'Total Non-\nAvailable Days',
                                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                                fontWeight: FontWeight.w500,
                                                color: AppColors.subText,
                                              ),
                                        ),
                                      ),
                                      DataColumn(
                                        label: Text(
                                          'Weekends\nPresent',
                                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                                fontWeight: FontWeight.w500,
                                                color: AppColors.subText,
                                              ),
                                        ),
                                      ),
                                      DataColumn(
                                        label: Text(
                                          'Fridays\nPresent',
                                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                                fontWeight: FontWeight.w500,
                                                color: AppColors.subText,
                                              ),
                                        ),
                                      ),
                                      DataColumn(
                                        label: Text(
                                          'Weekdays\nPresent',
                                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                                fontWeight: FontWeight.w500,
                                                color: AppColors.subText,
                                              ),
                                        ),
                                      ),
                                    ],
                                    rows: List<DataRow>.generate(
                                      dataToShow.length,
                                      (index) => DataRow(
                                        color: WidgetStateProperty.all(AppColors.white),
                                        cells: [
                                          DataCell(
                                            Text(
                                              dataToShow[index].user?.name ?? '',
                                              style: Theme.of(context).textTheme.bodyMedium,
                                            ),
                                          ),
                                          DataCell(
                                            Container(
                                              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                                              decoration: BoxDecoration(
                                                color: dataToShow[index].user?.getColor.withOpacity2(0.2),
                                                borderRadius: BorderRadius.circular(6),
                                                border: Border.all(
                                                  color: dataToShow[index].user?.getColor ?? Colors.transparent,
                                                ),
                                              ),
                                              child: Text(
                                                dataToShow[index].user?.dashboardUserRoleViewString ?? '',
                                                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                                      fontWeight: FontWeight.w500,
                                                      color: dataToShow[index].user?.getColor,
                                                    ),
                                              ),
                                            ),
                                          ),
                                          DataCell(
                                            Text(
                                              dataToShow[index].project?.name?.capitalizeFirstofEach ?? '',
                                              overflow: TextOverflow.ellipsis,
                                              maxLines: 2,
                                              style: Theme.of(context).textTheme.bodyMedium,
                                            ),
                                          ),
                                          DataCell(
                                            Text(
                                              dataToShow[index].totalAvailableDays.toString(),
                                              style: Theme.of(context).textTheme.bodyMedium,
                                              textAlign: TextAlign.center,
                                            ),
                                          ),
                                          DataCell(
                                            Text(
                                              dataToShow[index].totalNonAvailableDays.toString(),
                                              style: Theme.of(context).textTheme.bodyMedium,
                                              textAlign: TextAlign.center,
                                            ),
                                          ),
                                          DataCell(
                                            Text(
                                              dataToShow[index].weekendsPresent.toString(),
                                              style: Theme.of(context).textTheme.bodyMedium,
                                              textAlign: TextAlign.center,
                                            ),
                                          ),
                                          DataCell(
                                            Text(
                                              dataToShow[index].fridaysPresent.toString(),
                                              style: Theme.of(context).textTheme.bodyMedium,
                                              textAlign: TextAlign.center,
                                            ),
                                          ),
                                          DataCell(
                                            Text(
                                              dataToShow[index].weekdaysPresent.toString(),
                                              style: Theme.of(context).textTheme.bodyMedium,
                                              textAlign: TextAlign.center,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                    lastWidget: const SizedBox.shrink(),
                                  ),
                                );
                              },
                            ),
                          );
                        },
                      ),
                    ],
                  ),
                ),
              ],
            );
          },
        ),
      ),
    );
  }
}
