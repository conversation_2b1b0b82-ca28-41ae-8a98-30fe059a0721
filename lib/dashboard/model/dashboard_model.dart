import 'package:equatable/equatable.dart';
import 'package:excel_app/projects/model/project_model.dart';
import 'package:excel_app/users/model/user_model.dart';

class DashboardModel extends Equatable {
  const DashboardModel({
    this.id,
    this.projectId,
    this.userId,
    this.maxShift,
    this.minShift,
    this.maxWeekendShift,
    this.minWeekendShift,
    this.status,
    this.createdAt,
    this.updatedAt,
    this.totalAvailableDays,
    this.totalNonAvailableDays,
    this.weekendsPresent,
    this.weekdaysPresent,
    this.fridaysPresent,
    this.user,
    this.project,
  });

  factory DashboardModel.fromJson(Map<String, dynamic> json) {
    return DashboardModel(
      id: json['id'] as int?,
      projectId: json['project_id'] as int?,
      userId: json['user_id'] as int?,
      maxShift: json['max_shift'] as int?,
      minShift: json['min_shift'] as int?,
      maxWeekendShift: json['max_weekend_shift'] as int?,
      minWeekendShift: json['min_weekend_shift'] as int?,
      status: json['status'] as dynamic,
      createdAt: DateTime.tryParse((json['created_at'] as String?) ?? ''),
      updatedAt: DateTime.tryParse((json['updated_at'] as String?) ?? ''),
      totalAvailableDays: json['total_Available_days'] as int?,
      totalNonAvailableDays: json['total_non_Available_days'] as int?,
      weekendsPresent: json['Weekends_present'] as int?,
      weekdaysPresent: json['Weekdays_present'] as int?,
      fridaysPresent: json['fridays_present'] as int?,
      user: json['user'] != null ? UserModel.fromJson(json['user'] as Map<String, dynamic>) : null,
      project: json['project'] != null ? ProjectModel.fromJson(json['project'] as Map<String, dynamic>) : null,
    );
  }

  final int? id;
  final int? projectId;
  final int? userId;
  final int? maxShift;
  final int? minShift;
  final int? maxWeekendShift;
  final int? minWeekendShift;
  final dynamic status;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final int? totalAvailableDays;
  final int? totalNonAvailableDays;
  final int? weekendsPresent;
  final int? weekdaysPresent;
  final int? fridaysPresent;
  final UserModel? user;
  final ProjectModel? project;

  Map<String, dynamic> toJson() => {
        'id': id,
        'project_id': projectId,
        'user_id': userId,
        'max_shift': maxShift,
        'min_shift': minShift,
        'max_weekend_shift': maxWeekendShift,
        'min_weekend_shift': minWeekendShift,
        'status': status,
        'created_at': createdAt?.toIso8601String(),
        'updated_at': updatedAt?.toIso8601String(),
        'total_Available_days': totalAvailableDays,
        'total_non_Available_days': totalNonAvailableDays,
        'Weekends_present': weekendsPresent,
        'Weekdays_present': weekdaysPresent,
        'fridays_present': fridaysPresent,
        'user': user?.toJson(),
        'project': project?.toJson(),
      };

  @override
  List<Object?> get props => [
        id,
        projectId,
        userId,
        maxShift,
        minShift,
        maxWeekendShift,
        minWeekendShift,
        status,
        createdAt,
        updatedAt,
        totalAvailableDays,
        totalNonAvailableDays,
        weekendsPresent,
        weekdaysPresent,
        fridaysPresent,
        user,
        project,
      ];
}
