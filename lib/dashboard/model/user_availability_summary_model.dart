// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:excel_app/constants/app_colors.dart';
import 'package:flutter/material.dart';

class UserAvailabilitySummaryModel {
  final String userName;
  final String role;
  final String project;
  final int totalAvailableDays;
  final int totalNonAvailableDays;
  final int weekendsPresent;
  final int fridaysPresent;
  final int weekdaysPresent;
  final Color roleColor;

  UserAvailabilitySummaryModel({
    required this.userName,
    required this.role,
    required this.project,
    required this.totalAvailableDays,
    required this.totalNonAvailableDays,
    required this.weekendsPresent,
    required this.fridaysPresent,
    required this.weekdaysPresent,
    required this.roleColor,
  });

  // Static data for demo purposes
  static List<UserAvailabilitySummaryModel> getDemoData() {
    return [
      UserAvailabilitySummaryModel(
        userName: 'Dr. <PERSON>',
        role: 'Doctor',
        project: 'Respiratory Dept',
        totalAvailableDays: 22,
        totalNonAvailableDays: 8,
        weekendsPresent: 4,
        fridaysPresent: 3,
        weekdaysPresent: 15,
        roleColor: AppColors.blue,
      ),
      UserAvailabilitySummaryModel(
        userName: 'Erik Key',
        role: 'First yr',
        project: 'ER',
        totalAvailableDays: 18,
        totalNonAvailableDays: 12,
        weekendsPresent: 3,
        fridaysPresent: 2,
        weekdaysPresent: 13,
        roleColor: AppColors.deepSkyBlue,
      ),
      UserAvailabilitySummaryModel(
        userName: 'Erik Key',
        role: 'Second yr',
        project: 'ER',
        totalAvailableDays: 18,
        totalNonAvailableDays: 12,
        weekendsPresent: 3,
        fridaysPresent: 2,
        weekdaysPresent: 13,
        roleColor: AppColors.greenDark,
      ),
      UserAvailabilitySummaryModel(
        userName: 'Dr. Jenny',
        role: 'Doctor',
        project: 'Respiratory Dept',
        totalAvailableDays: 22,
        totalNonAvailableDays: 8,
        weekendsPresent: 4,
        fridaysPresent: 3,
        weekdaysPresent: 15,
        roleColor: AppColors.lovelyPurple,
      ),
      UserAvailabilitySummaryModel(
        userName: 'Dr. Jenny',
        role: 'Doctor',
        project: 'Respiratory Dept',
        totalAvailableDays: 22,
        totalNonAvailableDays: 8,
        weekendsPresent: 4,
        fridaysPresent: 3,
        weekdaysPresent: 15,
        roleColor: AppColors.orange,
      ),
    ];
  }
}
