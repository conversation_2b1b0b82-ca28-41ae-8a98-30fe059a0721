import 'package:data_table_2/data_table_2.dart';
import 'package:excel_app/constants/app_colors.dart';
import 'package:excel_app/dashboard/model/user_availability_summary_model.dart';
import 'package:flutter/material.dart';

class UserAvailabilityTable extends StatelessWidget {
  const UserAvailabilityTable({
    required this.data,
    super.key,
  });

  final List<UserAvailabilitySummaryModel> data;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 400,
      child: DataTable2(
        columns: [
          DataColumn2(
            label: Text(
              'User Name',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                    color: AppColors.subText,
                  ),
            ),
          ),
          DataColumn2(
            label: Text(
              'Role',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                    color: AppColors.subText,
                  ),
            ),
          ),
          DataColumn2(
            label: Text(
              'Project',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                    color: AppColors.subText,
                  ),
            ),
          ),
          DataColumn2(
            label: Text(
              'Total Available\nDays',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                    color: AppColors.subText,
                  ),
            ),
          ),
          DataColumn2(
            label: Text(
              'Total Non-\nAvailable Days',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                    color: AppColors.subText,
                  ),
            ),
          ),
          DataColumn2(
            label: Text(
              'Weekends\nPresent',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                    color: AppColors.subText,
                  ),
            ),
          ),
          DataColumn2(
            label: Text(
              'Fridays\nPresent',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                    color: AppColors.subText,
                  ),
            ),
          ),
          DataColumn2(
            label: Text(
              'Weekdays\nPresent',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                    color: AppColors.subText,
                  ),
            ),
          ),
        ],
        rows: List<DataRow>.generate(
          data.length,
          (index) => DataRow(
            color: WidgetStateProperty.all(AppColors.white),
            cells: [
              DataCell(
                Text(
                  data[index].userName,
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
              ),
              DataCell(
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                  decoration: BoxDecoration(
                    color: data[index].roleColor.withOpacity2(0.2),
                    borderRadius: BorderRadius.circular(6),
                    border: Border.all(color: data[index].roleColor),
                  ),
                  child: Text(
                    data[index].role,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          fontWeight: FontWeight.w500,
                          color: data[index].roleColor,
                        ),
                  ),
                ),
              ),
              DataCell(
                Text(
                  data[index].project,
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
              ),
              DataCell(
                Text(
                  data[index].totalAvailableDays.toString(),
                  style: Theme.of(context).textTheme.bodyMedium,
                  textAlign: TextAlign.center,
                ),
              ),
              DataCell(
                Text(
                  data[index].totalNonAvailableDays.toString(),
                  style: Theme.of(context).textTheme.bodyMedium,
                  textAlign: TextAlign.center,
                ),
              ),
              DataCell(
                Text(
                  data[index].weekendsPresent.toString(),
                  style: Theme.of(context).textTheme.bodyMedium,
                  textAlign: TextAlign.center,
                ),
              ),
              DataCell(
                Text(
                  data[index].fridaysPresent.toString(),
                  style: Theme.of(context).textTheme.bodyMedium,
                  textAlign: TextAlign.center,
                ),
              ),
              DataCell(
                Text(
                  data[index].weekdaysPresent.toString(),
                  style: Theme.of(context).textTheme.bodyMedium,
                  textAlign: TextAlign.center,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
