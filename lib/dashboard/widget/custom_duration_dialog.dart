import 'package:excel_app/constants/app_colors.dart';
import 'package:excel_app/widget/custom_date_picker.dart';
import 'package:excel_app/widget/popup_wrapper.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';

class SelectDurationModel {
  SelectDurationModel({this.startDate, this.endDate, this.selectedDuration});

  final DateTime? startDate;
  final DateTime? endDate;
  final String? selectedDuration;
}

class SelectDurationDialog extends StatefulWidget {
  const SelectDurationDialog({
    super.key,
    this.selectedDuration,
  });
  final SelectDurationModel? selectedDuration;

  @override
  State<SelectDurationDialog> createState() => _SelectDurationDialogState();
}

class _SelectDurationDialogState extends State<SelectDurationDialog> {
  final selectedDuration = ValueNotifier<SelectDurationModel>(SelectDurationModel());
  List<String> durations = [
    'Today',
    'This Week',
    'This Month',
    'This Year',
    'Custom Dates',
  ];

  @override
  void initState() {
    super.initState();
    selectedDuration.value = widget.selectedDuration ?? SelectDurationModel();
  }

  @override
  Widget build(BuildContext context) {
    return StatefulBuilder(
      builder: (context, stateUpdate) {
        return PopUpWrapper(
          constraints: const BoxConstraints(maxWidth: 420, maxHeight: 380),
          children: [
            Text(
              'Select Duration',
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                  ),
            ),
            const Gap(24),
            // Duration options in a modern grid layout
            ValueListenableBuilder<SelectDurationModel>(
              valueListenable: selectedDuration,
              builder: (context, selected, _) {
                return GridView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 2,
                    childAspectRatio: 2.2,
                    crossAxisSpacing: 16,
                    mainAxisSpacing: 16,
                  ),
                  itemCount: durations.length,
                  itemBuilder: (context, index) {
                    final duration = durations[index];
                    final isSelected = selected.selectedDuration == duration;

                    return InkWell(
                      onTap: () {
                        if (duration == 'Custom Dates') {
                          _showCustomDatePicker(context);
                        } else {
                          if (duration == 'This Week') {
                            final today = DateTime.now();
                            final startOfWeek = today.subtract(Duration(days: today.weekday));
                            final endOfWeek = startOfWeek.add(const Duration(days: 6));
                            stateUpdate(() {
                              selectedDuration.value = SelectDurationModel(
                                selectedDuration: duration,
                                startDate: startOfWeek,
                                endDate: endOfWeek,
                              );
                            });
                          } else if (duration == 'This Month') {
                            final today = DateTime.now();
                            final startOfMonth = DateTime(today.year, today.month);
                            final endOfMonth = DateTime(today.year, today.month + 1, 0);
                            stateUpdate(() {
                              selectedDuration.value = SelectDurationModel(
                                selectedDuration: duration,
                                startDate: startOfMonth,
                                endDate: endOfMonth,
                              );
                            });
                          } else if (duration == 'This Year') {
                            final today = DateTime.now();
                            final startOfYear = DateTime(today.year);
                            final endOfYear = DateTime(today.year + 1, 1, 0);
                            stateUpdate(() {
                              selectedDuration.value = SelectDurationModel(
                                selectedDuration: duration,
                                startDate: startOfYear,
                                endDate: endOfYear,
                              );
                            });
                          } else {
                            stateUpdate(() {
                              selectedDuration.value = SelectDurationModel(
                                selectedDuration: duration,
                                startDate: DateTime.now(),
                                endDate: DateTime.now(),
                              );
                            });
                          }
                          Navigator.pop(context, selectedDuration.value);
                        }
                      },
                      borderRadius: BorderRadius.circular(12),
                      child: AnimatedContainer(
                        duration: const Duration(milliseconds: 200),
                        decoration: BoxDecoration(
                          color: isSelected ? AppColors.primary : Colors.white,
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color: isSelected ? AppColors.primary : Colors.grey[300]!,
                            width: isSelected ? 2 : 1,
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: isSelected
                                  ? AppColors.primary.withValues(alpha: 0.25)
                                  : Colors.grey.withValues(alpha: 0.1),
                              blurRadius: isSelected ? 8 : 4,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              _getIconForDuration(duration),
                              color: isSelected ? Colors.white : AppColors.primary,
                              size: 24,
                            ),
                            const Gap(8),
                            Text(
                              duration,
                              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                    color: isSelected ? Colors.white : Colors.black87,
                                    fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                                    fontSize: 14,
                                  ),
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      ),
                    );
                  },
                );
              },
            ),
            const Gap(24),
          ],
        );
      },
    );
  }

  // Helper method to get appropriate icon for each duration
  IconData _getIconForDuration(String duration) {
    switch (duration) {
      case 'Today':
        return Icons.today;
      case 'This Week':
        return Icons.view_week;
      case 'This Month':
        return Icons.calendar_month;
      case 'This Year':
        return Icons.event;
      case 'Custom Dates':
        return Icons.date_range;
      default:
        return Icons.schedule;
    }
  }

  Future<void> _showCustomDatePicker(BuildContext context) async {
    final today = DateTime.now();

    final selectedRange = await CustomDatePicker.showRange(
      context,
      initialStartDate: today,
      initialEndDate: today,
      firstDate: DateTime(2020), // Allow dates from 2020
      lastDate: DateTime(2050), // Don't allow future dates
    );

    if (selectedRange != null && mounted) {
      selectedDuration.value = SelectDurationModel(
        selectedDuration: 'Custom Dates',
        startDate: selectedRange.start,
        endDate: selectedRange.end,
      );
    }
    // ignore: use_build_context_synchronously
    if (mounted) Navigator.pop(context, selectedDuration.value);
  }
}
