import 'package:excel_app/constants/app_colors.dart';
import 'package:excel_app/widget/custom_date_picker.dart';
import 'package:excel_app/widget/popup_wrapper.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:intl/intl.dart';

class SelectDurationDialog extends StatefulWidget {
  const SelectDurationDialog({
    super.key,
    this.selectedDuration,
    this.onDurationSelected,
  });
  final String? selectedDuration;
  final void Function(String)? onDurationSelected;

  @override
  State<SelectDurationDialog> createState() => _SelectDurationDialogState();
}

class _SelectDurationDialogState extends State<SelectDurationDialog> {
  final selectedDuration = ValueNotifier<String>('');
  List<String> durations = [
    'Today',
    'This Week',
    'This Month',
    'This Year',
    'Custom Dates',
  ];

  @override
  void initState() {
    super.initState();
    selectedDuration.value = widget.selectedDuration ?? '';
  }

  @override
  Widget build(BuildContext context) {
    return StatefulBuilder(
      builder: (context, stateUpdate) {
        return PopUpWrapper(
          children: [
            Text(
              'Select Duration',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const Gap(10),
            SizedBox(
              // width: 300,
              child: durations.firstOrNull == null
                  ? const SizedBox()
                  : Wrap(
                      spacing: 10,
                      runSpacing: 10,
                      children: durations.map((duration) {
                        return InkWell(
                          onTap: () {
                            if (duration == 'Custom Dates') {
                              // Close the current dialog
                              Navigator.pop(context);

                              // Show custom date picker
                              _showCustomDatePicker(context);
                            } else {
                              stateUpdate(() {
                                selectedDuration.value = duration;
                              });
                              Navigator.pop(context, duration);
                            }
                          },
                          child: Container(
                            padding: const EdgeInsets.all(10),
                            margin: const EdgeInsets.symmetric(vertical: 4),
                            decoration: BoxDecoration(
                              color: selectedDuration.value == duration
                                  ? AppColors.primary.withAlpha(20)
                                  : AppColors.transparent,
                              borderRadius: BorderRadius.circular(8),
                              border: Border.all(
                                color: AppColors.gray,
                              ),
                            ),
                            child: Text(
                              duration,
                              style: Theme.of(context).textTheme.bodyMedium,
                            ),
                          ),
                        );
                      }).toList(),
                    ),
              // ListView.builder(
              //   shrinkWrap: true,
              //   itemCount: durations.length,
              //   itemBuilder: (context, index) {
              //     return ValueListenableBuilder<String>(
              //       valueListenable: selectedDuration,
              //       builder: (context, selected, _) {
              //         return InkWell(
              //           onTap: () {
              //             if (durations[index] == 'Custom Dates') {
              //               // Close the current dialog
              //               Navigator.pop(context);

              //               // Show custom date picker
              //               _showCustomDatePicker(context);
              //             } else {
              //               stateUpdate(() {
              //                 selectedDuration.value = durations[index];
              //               });
              //               Navigator.pop(context, durations[index]);
              //             }
              //           },
              //           child: Container(
              //             padding: const EdgeInsets.all(10),
              //             margin: const EdgeInsets.symmetric(vertical: 4),
              //             decoration: BoxDecoration(
              //               color: AppColors.transparent,
              //               borderRadius: BorderRadius.circular(8),
              //               border: Border.all(
              //                 color: AppColors.gray,
              //               ),
              //             ),
              //             child: Row(
              //               children: [
              //                 Expanded(
              //                   child: Text(
              //                     durations[index],
              //                     style: Theme.of(context).textTheme.bodyMedium,
              //                   ),
              //                 ),
              //                 if (selected == durations[index])
              //                   const Icon(
              //                     Icons.check,
              //                     color: AppColors.primary,
              //                   ),
              //               ],
              //             ),
              //           ),
              //         );
              //       },
              //     );
              //   },
              // ),
            ),
          ],
        );
      },
    );
  }

  Future<void> _showCustomDatePicker(BuildContext context) async {
    final selectedDate = await CustomDatePicker.show(
      context,
      initialDate: DateTime.now(),
    );

    if (selectedDate != null && mounted) {
      final formattedDate = DateFormat('dd MMM yyyy').format(selectedDate);
      widget.onDurationSelected?.call(formattedDate);
    }
  }
}
