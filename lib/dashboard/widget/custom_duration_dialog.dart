import 'package:excel_app/constants/app_colors.dart';
import 'package:excel_app/widget/custom_date_picker.dart';
import 'package:excel_app/widget/popup_wrapper.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:intl/intl.dart';

class SelectDurationDialog extends StatefulWidget {
  const SelectDurationDialog({
    super.key,
    this.selectedDuration,
    this.onDurationSelected,
  });
  final String? selectedDuration;
  final void Function(String)? onDurationSelected;

  @override
  State<SelectDurationDialog> createState() => _SelectDurationDialogState();
}

class _SelectDurationDialogState extends State<SelectDurationDialog> {
  final selectedDuration = ValueNotifier<String>('');
  List<String> durations = [
    'Today',
    'This Week',
    'This Month',
    'This Year',
    'Custom Dates',
  ];

  @override
  void initState() {
    super.initState();
    selectedDuration.value = widget.selectedDuration ?? '';
  }

  @override
  Widget build(BuildContext context) {
    return StatefulBuilder(
      builder: (context, stateUpdate) {
        return PopUpWrapper(
          constraints: const BoxConstraints(maxWidth: 420, maxHeight: 380),
          children: [
            Text(
              'Select Duration',
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                  ),
            ),

            const Gap(24),
            // Duration options in a modern grid layout
            ValueListenableBuilder<String>(
              valueListenable: selectedDuration,
              builder: (context, selected, _) {
                return GridView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 2,
                    childAspectRatio: 2.2,
                    crossAxisSpacing: 16,
                    mainAxisSpacing: 16,
                  ),
                  itemCount: durations.length,
                  itemBuilder: (context, index) {
                    final duration = durations[index];
                    final isSelected = selected == duration;

                    return InkWell(
                      onTap: () {
                        if (duration == 'Custom Dates') {
                          // Close the current dialog
                          Navigator.pop(context);

                          // Show custom date picker
                          _showCustomDatePicker(context);
                        } else {
                          stateUpdate(() {
                            selectedDuration.value = duration;
                          });
                          Navigator.pop(context, duration);
                        }
                      },
                      borderRadius: BorderRadius.circular(12),
                      child: AnimatedContainer(
                        duration: const Duration(milliseconds: 200),
                        decoration: BoxDecoration(
                          color: isSelected ? AppColors.primary : Colors.white,
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color: isSelected ? AppColors.primary : Colors.grey[300]!,
                            width: isSelected ? 2 : 1,
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: isSelected
                                  ? AppColors.primary.withValues(alpha: 0.25)
                                  : Colors.grey.withValues(alpha: 0.1),
                              blurRadius: isSelected ? 8 : 4,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              _getIconForDuration(duration),
                              color: isSelected ? Colors.white : AppColors.primary,
                              size: 24,
                            ),
                            const Gap(8),
                            Text(
                              duration,
                              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                    color: isSelected ? Colors.white : Colors.black87,
                                    fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                                    fontSize: 14,
                                  ),
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      ),
                    );
                  },
                );
              },
            ),
            const Gap(24),
          ],
        );
      },
    );
  }

  // Helper method to get appropriate icon for each duration
  IconData _getIconForDuration(String duration) {
    switch (duration) {
      case 'Today':
        return Icons.today;
      case 'This Week':
        return Icons.view_week;
      case 'This Month':
        return Icons.calendar_month;
      case 'This Year':
        return Icons.event;
      case 'Custom Dates':
        return Icons.date_range;
      default:
        return Icons.schedule;
    }
  }

  Future<void> _showCustomDatePicker(BuildContext context) async {
    final today = DateTime.now();
    final oneWeekAgo = today.subtract(const Duration(days: 7));

    final selectedRange = await CustomDatePicker.showRange(
      context,
      initialStartDate: today,
      initialEndDate: today,
      firstDate: DateTime(2020), // Allow dates from 2020
      lastDate: today, // Don't allow future dates
    );

    if (selectedRange != null && mounted) {
      final startDate = DateFormat('dd MMM yyyy').format(selectedRange.start);
      final endDate = DateFormat('dd MMM yyyy').format(selectedRange.end);
      final formattedRange = '$startDate - $endDate';
      widget.onDurationSelected?.call(formattedRange);
    }
  }
}
