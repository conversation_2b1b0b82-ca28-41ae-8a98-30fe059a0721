import 'package:excel_app/constants/app_colors.dart';
import 'package:excel_app/widget/popup_wrapper.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';

class SelectDurationDialog extends StatefulWidget {
  const SelectDurationDialog({
    super.key,
    this.selectedDuration,
    this.onDurationSelected,
  });
  final String? selectedDuration;
  final void Function(String)? onDurationSelected;

  @override
  State<SelectDurationDialog> createState() => _SelectDurationDialogState();
}

class _SelectDurationDialogState extends State<SelectDurationDialog> {
  final selectedDuration = ValueNotifier<String>('');
  List<String> durations = [
    'Today',
    'This Week',
    'This Month',
    'This Year',
    'Custom Dates',
  ];

  @override
  void initState() {
    super.initState();
    selectedDuration.value = widget.selectedDuration ?? '';
  }

  @override
  Widget build(BuildContext context) {
    return StatefulBuilder(
      builder: (context, stateUpdate) {
        return PopUpWrapper(
          constraints: const BoxConstraints(maxWidth: 400, maxHeight: 500),
          physics: const NeverScrollableScrollPhysics(),
          children: [
            Text(
              'Select Duration',
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                  ),
            ),
            const Gap(20),
            SizedBox(
              height: 290,
              child: ListView.builder(
                shrinkWrap: true,
                itemBuilder: (context, index) {
                  return ValueListenableBuilder<String>(
                    valueListenable: selectedDuration,
                    builder: (context, selected, _) {
                      return InkWell(
                        onTap: () {
                          if (durations[index] == 'Custom Dates') {
                            // Close the current dialog
                            Navigator.pop(context);

                            // Show date range picker
                            showDateRangePicker(
                              context: context,
                              firstDate: DateTime(2020),
                              lastDate: DateTime(2030),
                              initialDateRange: DateTimeRange(
                                start: DateTime.now(),
                                end: DateTime.now().add(const Duration(days: 7)),
                              ),
                              builder: (context, child) {
                                return Theme(
                                  data: Theme.of(context).copyWith(
                                    colorScheme: const ColorScheme.light(
                                      primary: AppColors.primary,
                                    ),
                                  ),
                                  child: child!,
                                );
                              },
                            ).then((dateRange) {
                              if (dateRange != null) {
                                // Format the date range as a string
                                final startDate =
                                    '${dateRange.start.day}/${dateRange.start.month}/${dateRange.start.year}';
                                final endDate = '${dateRange.end.day}/${dateRange.end.month}/${dateRange.end.year}';
                                final customDateRange = '$startDate - $endDate';

                                // Return the custom date range
                                widget.onDurationSelected?.call(customDateRange);
                              }
                            });
                          } else {
                            stateUpdate(() {
                              selectedDuration.value = durations[index];
                            });
                            Navigator.pop(context, durations[index]);
                          }
                        },
                        child: Container(
                          padding: const EdgeInsets.all(10),
                          margin: const EdgeInsets.symmetric(vertical: 4),
                          decoration: BoxDecoration(
                            color: selectedDuration.value == durations[index]
                                ? AppColors.primary.withAlpha(20)
                                : AppColors.transparent,
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(
                              color: selectedDuration.value == durations[index] ? AppColors.primary : AppColors.gray,
                            ),
                          ),
                          child: Text(
                            durations[index],
                            style: Theme.of(context).textTheme.bodyMedium,
                          ),
                        ),
                      );
                    },
                  );
                },
                itemCount: durations.length,
              ),
            ),
          ],
        );
      },
    );
  }
}
