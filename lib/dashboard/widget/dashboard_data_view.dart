import 'package:excel_app/constants/app_colors.dart';
import 'package:excel_app/widget/container_widget.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';

class DashboardDataView extends StatelessWidget {
  const DashboardDataView({required this.count, required this.title, super.key});
  final String count;
  final String title;

  @override
  Widget build(BuildContext context) {
    return ContainerWidget(
      constraints: const BoxConstraints(maxWidth: 205),
      margin: EdgeInsets.zero,
      child: Column(
        children: [
          Text(
            count,
            style: Theme.of(context)
                .textTheme
                .headlineSmall
                ?.copyWith(fontWeight: FontWeight.w600, color: AppColors.primary),
          ),
          const Gap(8),
          Text(
            title,
            style: Theme.of(context).textTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
