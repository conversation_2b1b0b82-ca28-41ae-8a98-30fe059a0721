import 'dart:developer';

import 'package:excel_app/calender/model/project_staff_model.dart';
import 'package:excel_app/constants/app_colors.dart';
import 'package:excel_app/users/model/user_model.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';

class MonthyGridCalenderView extends StatelessWidget {
  const MonthyGridCalenderView({
    required this.initialMonth,
    required this.assignments,
    required this.user,
    required this.leaderId,
    super.key,
  });
  final DateTime initialMonth;
  final List<ProjectStaffMember> assignments;
  final UserModel user;
  final int leaderId;

  /// Build days for the grid (✅ always Monday → Sunday)
  List<DateTime> _getDaysForGrid() {
    final firstDayOfMonth = DateTime(initialMonth.year, initialMonth.month);
    final lastDayOfMonth = DateTime(initialMonth.year, initialMonth.month + 1, 0);

    // ✅ Find the Monday before (or equal to) the first day of the month
    var start = firstDayOfMonth;
    while (start.weekday != DateTime.monday) {
      start = start.subtract(const Duration(days: 1));
    }

    // ✅ Find the Sunday after (or equal to) the last day of the month
    var end = lastDayOfMonth;
    while (end.weekday != DateTime.sunday) {
      end = end.add(const Duration(days: 1));
    }

    // ✅ Build full list of days (no null placeholders anymore)
    final days = <DateTime>[];
    var current = start;
    while (!current.isAfter(end)) {
      days.add(current);
      current = current.add(const Duration(days: 1));
    }

    return days;
  }

  Widget _buildCalendarGrid(BuildContext context) {
    final days = _getDaysForGrid();
    log('${user.id} userId');
    log('$leaderId leaderId');

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Fixed header section
        Container(
          width: double.infinity,
          height: 1,
          color: AppColors.strokeColor,
        ),
        _buildWeekdayHeaders(),
        // Scrollable content section
        Expanded(
          child: SingleChildScrollView(
            child: GridView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 7,
                childAspectRatio: 1.2,
              ),
              itemCount: days.length,
              itemBuilder: (context, index) {
                final date = days[index];
                // ✅ Check if this cell belongs to the current month
                final isCurrentMonth = date.month == initialMonth.month;
                return _buildDateCell(context, date, isCurrentMonth);
              },
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildWeekdayHeaders() {
    const weekdays = [
      'MON',
      'TUE',
      'WED',
      'THU',
      'FRI',
      'SAT',
      'SUN',
    ];

    return Container(
      decoration: const BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: AppColors.strokeColor,
          ),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: weekdays
            .map(
              (day) => Expanded(
                child: Padding(
                  padding: const EdgeInsets.only(left: 13),
                  child: Container(
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    decoration: const BoxDecoration(
                      border: Border(
                        right: BorderSide(color: AppColors.strokeColor),
                      ),
                    ),
                    child: Text(
                      day,
                      style: TextStyle(
                        fontWeight: FontWeight.w500,
                        color: Colors.grey.shade600,
                        fontSize: 12,
                      ),
                      textAlign: TextAlign.start,
                    ),
                  ),
                ),
              ),
            )
            .toList(),
      ),
    );
  }

  /// ✅ New: Pass `isCurrentMonth` to style and control assignments
  Widget _buildDateCell(
    BuildContext context,
    DateTime date,
    bool isCurrentMonth,
  ) {
    final assignments = isCurrentMonth ? _getStaffForDate(date) : <UserModel>[];
    final isToday = _isToday(date);

    return Container(
      decoration: const BoxDecoration(
        border: Border(
          right: BorderSide(color: AppColors.strokeColor),
          bottom: BorderSide(color: AppColors.strokeColor),
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // ✅ Gray out days that are not in the current month
            Text(
              date.day.toString(),
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: isCurrentMonth ? AppColors.primary : Colors.grey.shade400,
                    fontWeight: isToday ? FontWeight.bold : FontWeight.w500,
                  ),
            ),
            const Gap(16),
            if (isCurrentMonth) // ✅ Show assignments only for current month
              Flexible(
                child: ListView.builder(
                  itemBuilder: (context, index) {
                    return _buildAssignmentChip(assignments[index], context);
                  },
                  physics: const ScrollPhysics(),
                  itemCount: assignments.length,
                  shrinkWrap: true,
                ),
              ),
          ],
        ),
      ),
    );
  }

  bool _isToday(DateTime date) {
    final now = DateTime.now();
    return date.year == now.year && date.month == now.month && date.day == now.day;
  }

  Widget _buildAssignmentChip(UserModel staff, BuildContext context) {
    final isCurrentUser = staff.id == user.id;

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 6),
      child: Container(
        padding: isCurrentUser ? const EdgeInsets.symmetric(horizontal: 4, vertical: 2) : null,
        decoration: isCurrentUser
            ? BoxDecoration(
                color: AppColors.primary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(4),
                border: Border.all(
                  color: AppColors.primary.withValues(alpha: 0.3),
                ),
              )
            : null,
        child: Row(
          children: [
            Container(
              height: 18,
              width: 20,
              alignment: Alignment.center,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(4),
                color: staff.getColor.withOpacity2(.2),
              ),
              child: Text(
                staff.firstLetterForRole,
                style: Theme.of(context).textTheme.labelSmall?.copyWith(
                      color: staff.getColor,
                      fontSize: 11,
                      fontWeight: FontWeight.w500,
                    ),
              ),
            ),
            const Gap(4),
            Expanded(
              child: Text(
                staff.name ?? '',
                style: Theme.of(context).textTheme.labelLarge?.copyWith(
                      fontSize: 11,
                      fontWeight: isCurrentUser ? FontWeight.w600 : FontWeight.normal,
                      color: isCurrentUser ? AppColors.primary : null,
                    ),
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ),
    );
  }

  List<UserModel> _getStaffForDate(DateTime date) {
    final userList =
        assignments.where((staff) => staff.userModel?.users != null).expand((staff) => staff.userModel!.users).toList();

    final isLeader = user.id == leaderId;
    final hasManagerAccess = isLeader;

    log('$hasManagerAccess hasManagerAccess');

    return userList
        .where(
          (u) =>
              u.userProjectAvailableDates != null &&
              u.userProjectAvailableDates!.any((calendar) => calendar.date == date),
        )
        .toList();
  }

  @override
  Widget build(BuildContext context) {
    return _buildCalendarGrid(context);
  }
}
