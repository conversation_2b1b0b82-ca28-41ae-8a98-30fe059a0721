import 'dart:developer';

import 'package:collection/collection.dart';
import 'package:easy_debounce/easy_debounce.dart';
import 'package:excel_app/app/bloc/authentication_bloc.dart';
import 'package:excel_app/calender/model/project_staff_model.dart';
import 'package:excel_app/constants/app_assets.dart';
import 'package:excel_app/constants/app_colors.dart';
import 'package:excel_app/projects/model/project_calendardate_model.dart';
import 'package:excel_app/projects/model/swap_availability_model.dart';
import 'package:excel_app/projects/model/user_project_calendar_date_model.dart';
import 'package:excel_app/users/model/user_model.dart';
import 'package:excel_app/widget/app_text_form_field.dart';
import 'package:excel_app/widget/custom_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';

class ProjectMonthlyListCalenderView extends StatefulWidget {
  const ProjectMonthlyListCalenderView({
    required this.currentMonth,
    required this.staffMembers,
    this.leaderId,
    this.projectCalendarDates,
    super.key,
    this.onTap,
    this.projectLeaderId,
    this.isAdmin = false,
    this.isFromProjectPlanning = false,
    this.userProjectConflictDates = const [],
  });
  final DateTime currentMonth;
  final List<ProjectStaffMember> staffMembers;
  final List<ProjectCalendarDateModel>? projectCalendarDates;
  final List<UserProjectCalendarDateModel> userProjectConflictDates;
  final int? projectLeaderId;
  final bool isFromProjectPlanning;
  final bool isAdmin;
  final int? leaderId;
  final void Function(
    UserProjectCalendarDateModel data,
  )? onTap;

  @override
  State<ProjectMonthlyListCalenderView> createState() => _ProjectMonthlyListCalenderViewState();
}

class _ProjectMonthlyListCalenderViewState extends State<ProjectMonthlyListCalenderView> {
  final searchController = TextEditingController();
  final searchNotifier = ValueNotifier<String>('');
  // List<ProjectStaffMember> filteredStaffMembers = [];
  final filteredStaffMembers = ValueNotifier<List<ProjectStaffMember>>([]);

  final bool _isHovered = false;
  final swapValue = ValueNotifier<SwapUserAvailabilityDate?>(null);
  final isButtonLoading = ValueNotifier<bool>(false);

  @override
  void initState() {
    super.initState();
    filteredStaffMembers.value = widget.staffMembers; // Initialize with all staff members
  }

  @override
  void dispose() {
    searchController.removeListener(_onSearchChanged);
    searchNotifier.dispose();
    super.dispose();
  }

  void _onSearchChanged() {
    final query = searchController.text.trim().toLowerCase();

    if (query.isEmpty) {
      filteredStaffMembers.value = widget.staffMembers;
    } else {
      final filtered = <ProjectStaffMember>[];

      for (final staff in widget.staffMembers) {
        final users = staff.userModel?.users ?? [];

        final matchedUsers = users
            .where(
              (user) => (user.name?.trim().toLowerCase() ?? '').contains(query),
            )
            .toList();

        if (matchedUsers.isNotEmpty) {
          final filteredStaff = staff.copyWith(
            userModel: staff.userModel?.copyWith(users: matchedUsers),
          );
          filtered.add(filteredStaff);
        }
      }
      filteredStaffMembers.value = filtered;
    }
  }

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<List<ProjectStaffMember>>(
      valueListenable: filteredStaffMembers,
      builder: (context, staffMember, _) {
        final projectUsers = _groupUserByCategory(staffMember);
        return Column(
          children: [
            // ✅ Fixed header (search + day headers)
            _buildHeader(),

            // ✅ Scrollable part with remaining height
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    ...projectUsers.entries.map(
                      (entry) => Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          _buildCategoryHeader(entry.key, context),
                          ...entry.value.map(_buildStaffRow),
                        ],
                      ),
                    ),
                    if (widget.isFromProjectPlanning) ...[
                      ...projectUsers.entries.map(
                        (entry) => Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            ...entry.value.map(_buildUserCategoryRow),
                          ],
                        ),
                      ),
                      _buildCategoryTotal(widget.staffMembers),
                    ],
                  ],
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildHeader() {
    return Container(
      decoration: BoxDecoration(
        border: Border(
          bottom: widget.staffMembers.isNotEmpty ? const BorderSide(color: AppColors.strokeColor) : BorderSide.none,
          top: const BorderSide(color: AppColors.strokeColor),
        ),
      ),
      child: Row(
        children: [
          AppTextFormField(
            maxWidth: 200,
            controller: searchController,
            hintText: 'Search',
            prefixIcon: AppAssets.searchIcon,
            fillColor: _isHovered ? AppColors.white : AppColors.white,
            onChanged: (value) {
              EasyDebounce.debounce(
                'search',
                const Duration(milliseconds: 100),
                _onSearchChanged,
              );
            },
          ),
          Expanded(
            child: Row(
              children: _buildDayHeaders(),
            ),
          ),
        ],
      ),
    );
  }

  List<Widget> _buildDayHeaders() {
    final headers = <Widget>[];
    final daysInMonth = _getDaysInMonthWithTotal();

    for (var day = 1; day <= daysInMonth.length; day++) {
      if (day == daysInMonth.length) {
        // Add the "Total" header at the end
        headers.add(
          Expanded(
            child: SizedBox(
              child: Center(
                child: Text(
                  widget.isFromProjectPlanning ? 'Total' : '',
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ),
          ),
        );
      } else {
        final date = DateTime(widget.currentMonth.year, widget.currentMonth.month, day);
        final isWeekend = date.weekday == DateTime.saturday || date.weekday == DateTime.sunday;
        final isCheckProjectHolidayDate = widget.projectCalendarDates?.firstWhereOrNull(
          (element) =>
              element.date?.year == date.year && element.date?.month == date.month && element.date?.day == date.day,
        );

        final isCheckProjectConflictDate = widget.userProjectConflictDates.firstWhereOrNull(
          (element) =>
              element.date?.year == date.year && element.date?.month == date.month && element.date?.day == date.day,
        );

        headers.add(
          Expanded(
            child: InkWell(
              onTap: () {
                if (isCheckProjectConflictDate != null) {
                  widget.onTap?.call(isCheckProjectConflictDate);
                }
              },
              child: Tooltip(
                padding: const EdgeInsets.all(8),
                message: isCheckProjectConflictDate != null ? isCheckProjectConflictDate.metaData?.reason ?? '' : '',
                child: Container(
                  padding: const EdgeInsets.symmetric(vertical: 8),
                  decoration: BoxDecoration(
                    color: isCheckProjectConflictDate != null
                        ? AppColors.conflictBackgroundColor
                        : (!isWeekend && isCheckProjectHolidayDate != null)
                            ? AppColors.lightGray
                            : isWeekend
                                ? AppColors.geyser
                                : AppColors.transparent,
                    border: Border(
                      left: const BorderSide(color: AppColors.strokeColor),
                      right: BorderSide(color: day == daysInMonth.length - 1 ? AppColors.black : AppColors.transparent),
                    ),
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        _getDayLetter(date.weekday),
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey[700],
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        day.toString(),
                        style: const TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      }
    }
    return headers;
  }

  Widget _buildCategoryHeader(String category, BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Text(
        category,
        style: Theme.of(context).textTheme.titleSmall?.copyWith(color: AppColors.primary),
      ),
    );
  }

  Widget _buildStaffRow(ProjectStaffMember staff) {
    return Flexible(
      child: ListView.builder(
        shrinkWrap: true,
        itemCount: staff.userModel?.users.length,
        physics: const NeverScrollableScrollPhysics(),
        itemBuilder: (context, index) {
          final user = staff.userModel?.users[index];
          final userProjectHolidaysDates = user?.userProjectCalendarDates;
          final userProjectAvailableDates = user?.userProjectAvailableDates;
          if (staff.userModel?.users != null && staff.userModel!.users.isNotEmpty) {
            final isLastIndex = index == (staff.userModel?.users.length ?? 0) - 1;
            return Container(
              height: 40,
              decoration: BoxDecoration(
                border: Border(
                  bottom: BorderSide(
                    color: isLastIndex ? AppColors.strokeColor : AppColors.strokeColor,
                  ),
                  top: index == 0
                      ? const BorderSide(color: AppColors.strokeColor)
                      : const BorderSide(color: AppColors.transparent),
                ),
              ),
              child: Row(
                children: [
                  Container(
                    width: 200,
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    decoration: const BoxDecoration(
                      border: Border(
                        right: BorderSide(color: AppColors.transparent),
                      ),
                    ),
                    child: Row(
                      children: [
                        ClipRRect(
                          borderRadius: BorderRadius.circular(100),
                          child: CustomNetworkImage(
                            imageUrl: user?.imagePath ?? '',
                            height: 24,
                            width: 24,
                            isSmallImage: true,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Flexible(
                          child: BlocBuilder<AuthenticationBloc, AuthenticationState>(
                            builder: (context, authState) {
                              final isCurrentUser = authState is Authenticated && authState.user.id == user?.id;
                              return Container(
                                padding: isCurrentUser ? const EdgeInsets.symmetric(horizontal: 6, vertical: 2) : null,
                                decoration: isCurrentUser
                                    ? BoxDecoration(
                                        color: AppColors.primary.withValues(alpha: 0.1),
                                        borderRadius: BorderRadius.circular(4),
                                        border: Border.all(color: AppColors.primary.withValues(alpha: 0.3)),
                                      )
                                    : null,
                                child: Text(
                                  isCurrentUser ? user?.name ?? '' : user?.name ?? '',
                                  style: TextStyle(
                                    fontWeight: isCurrentUser ? FontWeight.w600 : FontWeight.w500,
                                    fontSize: 12,
                                    color: isCurrentUser ? AppColors.primary : null,
                                  ),
                                  overflow: TextOverflow.ellipsis,
                                ),
                              );
                            },
                          ),
                        ),
                        if (widget.projectLeaderId == user?.id) ...[
                          const Gap(8),
                          Container(
                            width: 40,
                            height: 18,
                            alignment: Alignment.center,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(5),
                              color: AppColors.lightMint,
                            ),
                            child: Text(
                              'L + U',
                              maxLines: 1,
                              style: Theme.of(context).textTheme.labelSmall?.copyWith(fontWeight: FontWeight.w500),
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                  Expanded(
                    child: Row(
                      children: _buildStaffScheduleCells(
                        staff,
                        userProjectHolidaysDates ?? [],
                        userProjectAvailableDates ?? [],
                      ),
                    ),
                  ),
                ],
              ),
            );
          }
          return const SizedBox();
        },
      ),
    );
  }

  List<Widget> _buildStaffScheduleCells(
    ProjectStaffMember staff,
    List<UserProjectCalendarDateModel> userProjectHolidaysDates,
    List<UserProjectCalendarDateModel> userProjectAvailableDates,
  ) {
    final cells = <Widget>[];
    final daysInMonth = _getDaysInMonthWithTotal();

    for (var day = 1; day <= daysInMonth.length; day++) {
      final totalSchedules = _calculateUserAvailabilityTotal(userProjectAvailableDates);
      // Calculate the sum of values
      final totalSum = totalSchedules.values.fold(0, (sum, value) => sum + value);

      if (day == daysInMonth.length) {
        // Add a blank or special cell for "Total" at the end
        cells.add(
          Expanded(
            child: Container(
              decoration: const BoxDecoration(
                color: AppColors.transparent,
              ),
              child: Center(
                child: Text(
                  widget.isFromProjectPlanning
                      ? totalSum != 0
                          ? totalSum.toString()
                          : ''
                      : '',
                  style: Theme.of(context).textTheme.titleSmall,
                ),
              ),
            ),
          ),
        );
      } else {
        final date = DateTime(widget.currentMonth.year, widget.currentMonth.month, day);
        final isWeekend = date.weekday == DateTime.saturday || date.weekday == DateTime.sunday;
        final isCheckProjectHolidayDate = widget.projectCalendarDates?.firstWhereOrNull(
          (element) =>
              element.date?.year == date.year && element.date?.month == date.month && element.date?.day == date.day,
        );
        final isCheckProjectAvailableDate = userProjectAvailableDates.firstWhereOrNull(
          (element) =>
              element.date?.year == date.year && element.date?.month == date.month && element.date?.day == date.day,
        );

        final isCheckProjectConflictDate = widget.userProjectConflictDates.firstWhereOrNull(
          (element) =>
              element.date?.year == date.year && element.date?.month == date.month && element.date?.day == date.day,
        );

        UserProjectCalendarDateModel? addAvailabilityDate;
        final scheduledDate = userProjectHolidaysDates.firstWhereOrNull(
          (scheduledDate) =>
              scheduledDate.date?.year == date.year &&
              scheduledDate.date?.month == date.month &&
              scheduledDate.date?.day == date.day,
        );
        if (scheduledDate != null && scheduledDate.leaveCategory != null) {
          addAvailabilityDate = scheduledDate;
        }

        final color = (isCheckProjectConflictDate != null && addAvailabilityDate == null)
            ? AppColors.conflictBackgroundColor
            : addAvailabilityDate != null
                ? AppColors.hexToColor(addAvailabilityDate.leaveCategory?.colorHex ?? '')
                : (isCheckProjectHolidayDate != null && !isWeekend)
                    ? AppColors.lightGray
                    : (isWeekend && addAvailabilityDate != null)
                        ? AppColors.hexToColor(addAvailabilityDate.leaveCategory?.colorHex ?? '')
                        : isWeekend
                            ? AppColors.geyser
                            : Colors.transparent;
        cells.add(
          Expanded(
            child: Builder(
              builder: (context) {
                return InkWell(
                  onTap:
                      (isCheckProjectConflictDate != null && addAvailabilityDate == null) || addAvailabilityDate != null
                          ? () {
                              if (isCheckProjectConflictDate != null && addAvailabilityDate == null) {
                                widget.onTap?.call(isCheckProjectConflictDate);
                                return;
                              } else if (addAvailabilityDate != null) {
                                widget.onTap?.call(addAvailabilityDate);
                                return;
                              }
                            }
                          : null,
                  child: Tooltip(
                    message: isCheckProjectConflictDate != null ? isCheckProjectConflictDate.toString() : '',
                    child: Container(
                      decoration: BoxDecoration(
                        color: color,
                        border: Border(
                          left: day == 1
                              ? const BorderSide(color: AppColors.strokeColor)
                              : const BorderSide(color: AppColors.strokeColor),
                          right: day == (daysInMonth.length) - 1
                              ? const BorderSide()
                              : const BorderSide(color: AppColors.transparent),
                        ),
                      ),
                      child: (isCheckProjectAvailableDate != null)
                          ? ValueListenableBuilder(
                              valueListenable: swapValue,
                              builder: (context, value, _) {
                                return Center(
                                  child: SvgPicture.asset(
                                    AppAssets.checkIcon,
                                  ),
                                );
                              },
                            )
                          : null,
                    ),
                  ),
                );
              },
            ),
          ),
        );
      }
    }
    return cells;
  }

  Widget _buildUserCategoryRow(ProjectStaffMember staff) {
    return Column(
      children: [
        Container(
          height: 40,
          decoration: const BoxDecoration(
            border: Border(
              bottom: BorderSide(color: AppColors.strokeColor),
            ),
          ),
          child: Row(
            children: [
              Container(
                width: 200,
                padding: const EdgeInsets.symmetric(horizontal: 16),
                decoration: const BoxDecoration(),
                child: Row(
                  children: [
                    Expanded(
                      child: Text(
                        staff.userModel?.userRoleViewString ?? '',
                        style: const TextStyle(
                          fontWeight: FontWeight.w500,
                          fontSize: 12,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
              ),
              Expanded(
                child: Row(
                  children: _buildUserCategoryCount(
                    staff,
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  List<Widget> _buildUserCategoryCount(
    ProjectStaffMember staff,
  ) {
    final cells = <Widget>[];
    final daysInMonth = _getDaysInMonthWithTotal();
    // final user =
    //     staff.userModel?.users.expand<UserProjectCalendarDateModel>((e) => e.userProjectCalendarDates ?? []).toList();
    final assignUserDate =
        staff.userModel?.users.expand<UserProjectCalendarDateModel>((e) => e.userProjectAvailableDates ?? []).toList();

    for (var day = 1; day <= daysInMonth.length; day++) {
      if (day == daysInMonth.length) {
        // Add the "Total" cell at the end with the cumulative count
        cells.add(
          Expanded(
            child: Container(
              decoration: const BoxDecoration(),
              child: SizedBox(
                height: 40,
                child: Center(
                  child: Text(
                    (assignUserDate != null && assignUserDate.isNotEmpty) ? assignUserDate.length.toString() : '',
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                ),
              ),
            ),
          ),
        );
      } else {
        final date = DateTime(widget.currentMonth.year, widget.currentMonth.month, day);
        final isCheckProjectConflictDate = widget.userProjectConflictDates.firstWhereOrNull(
          (element) =>
              element.date?.year == date.year && element.date?.month == date.month && element.date?.day == date.day,
        );
        // Count how many times this date appears in the user's schedule
        final count = assignUserDate
                ?.where(
                  (scheduledDate) =>
                      scheduledDate.date?.year == date.year &&
                      scheduledDate.date?.month == date.month &&
                      scheduledDate.date?.day == date.day,
                )
                .length ??
            0;

        // totalCount += count; // Accumulate the count for total
        cells.add(
          Expanded(
            child: InkWell(
              onTap: () {
                if (isCheckProjectConflictDate != null) {
                  widget.onTap?.call(isCheckProjectConflictDate);
                }
              },
              child: Tooltip(
                message: isCheckProjectConflictDate != null ? isCheckProjectConflictDate.metaData?.reason ?? '' : '',
                child: Container(
                  decoration: BoxDecoration(
                    color:
                        isCheckProjectConflictDate != null ? AppColors.conflictBackgroundColor : AppColors.transparent,
                    border: Border(
                      left: const BorderSide(color: AppColors.strokeColor),
                      right: day == (daysInMonth.length) - 1
                          ? const BorderSide()
                          : const BorderSide(color: AppColors.transparent),
                    ),
                  ),
                  child: SizedBox(
                    height: 40,
                    child: Center(
                      child: Text(
                        count > 0 ? '$count' : '',
                        style: Theme.of(context).textTheme.titleSmall,
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
        );
      }
    }
    return cells;
  }

  Widget _buildCategoryTotal(List<ProjectStaffMember> staffList) {
    final totalSchedules = _calculateCategoryTotalSchedules(staffList);

    return SizedBox(
      height: 40,
      child: Row(
        children: [
          Container(
            width: 200,
            alignment: Alignment.centerLeft,
            padding: const EdgeInsets.symmetric(horizontal: 16),
            decoration: const BoxDecoration(
              border: Border(
                bottom: BorderSide(color: AppColors.strokeColor),
              ),
            ),
            child: const Text(
              'Total',
              style: TextStyle(
                fontWeight: FontWeight.w600,
                fontSize: 12,
              ),
            ),
          ),
          Expanded(
            child: Row(
              children: _buildCategoryTotalCounts(totalSchedules),
            ),
          ),
        ],
      ),
    );
  }

  // New method to build total cells
  List<Widget> _buildCategoryTotalCounts(Map<int, int> totalsByDay) {
    final cells = <Widget>[];
    final daysInMonth = _getDaysInMonthWithTotal();

    for (var day = 1; day <= daysInMonth.length; day++) {
      final total = totalsByDay[day] ?? 0;
      if (day == daysInMonth.length) {
        final grandTotal = totalsByDay.values.fold(0, (sum, value) => sum + value);

        cells.add(
          Expanded(
            child: Container(
              height: 40,
              decoration: const BoxDecoration(
                border: Border(
                  bottom: BorderSide(color: AppColors.strokeColor),
                ),
              ),
              child: Center(
                child: Text(
                  grandTotal > 0 ? grandTotal.toString() : '',
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
              ),
            ),
          ),
        );
      } else {
        final date = DateTime(widget.currentMonth.year, widget.currentMonth.month, day);
        final isCheckProjectConflictDate = widget.userProjectConflictDates.firstWhereOrNull(
          (element) =>
              element.date?.year == date.year && element.date?.month == date.month && element.date?.day == date.day,
        );
        cells.add(
          Expanded(
            child: InkWell(
              onTap: () {
                if (isCheckProjectConflictDate != null) {
                  widget.onTap?.call(isCheckProjectConflictDate);
                }
              },
              child: Tooltip(
                message: isCheckProjectConflictDate != null ? isCheckProjectConflictDate.metaData?.reason ?? '' : '',
                child: Container(
                  decoration: BoxDecoration(
                    color:
                        isCheckProjectConflictDate != null ? AppColors.conflictBackgroundColor : AppColors.transparent,
                    border: Border(
                      left: const BorderSide(color: AppColors.strokeColor),
                      bottom: const BorderSide(color: AppColors.strokeColor),
                      right: day == (daysInMonth.length) - 1
                          ? const BorderSide()
                          : const BorderSide(color: AppColors.transparent),
                    ),
                  ),
                  child: Center(
                    child: Text(
                      total != 0 ? total.toString() : '',
                      style: const TextStyle(
                        fontWeight: FontWeight.w600,
                        fontSize: 12,
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
        );
      }
    }
    return cells;
  }

  // New method to calculate category total schedules
  Map<int, int> _calculateCategoryTotalSchedules(List<ProjectStaffMember> staffList) {
    final totalsByDay = <int, int>{};
    for (final staff in staffList) {
      for (final user in staff.userModel?.users ?? <UserModel>[]) {
        for (final date in user.userProjectAvailableDates ?? <UserProjectCalendarDateModel>[]) {
          if (date.date != null) {
            final day = date.date!.day;
            totalsByDay[day] = (totalsByDay[day] ?? 0) + 1;
          }
        }
      }
    }
    log('${totalsByDay}total ways');
    return totalsByDay;
  }

  Map<int, int> _calculateUserAvailabilityTotal(List<UserProjectCalendarDateModel> staff) {
    final totalsByDay = <int, int>{};
    for (final date in staff) {
      if (date.date != null) {
        final day = date.date!.day;
        totalsByDay[day] = (totalsByDay[day] ?? 0) + 1;
      }
    }

    return totalsByDay;
  }

  Map<String, List<ProjectStaffMember>> _groupUserByCategory(List<ProjectStaffMember> staffList) {
    final map = <String, List<ProjectStaffMember>>{};
    for (final staff in staffList) {
      map.putIfAbsent(staff.userModel?.userRoleViewString ?? '', () => []).add(staff);
    }
    return map;
  }

  List<String> _getDaysInMonthWithTotal() {
    return List<String>.generate(
      _getDaysInMonth() + 1,
      (index) => index < _getDaysInMonth() ? '${index + 1}' : 'Total',
    );
  }

  int _getDaysInMonth() {
    return DateTime(widget.currentMonth.year, widget.currentMonth.month + 1, 0).day;
  }

  String _getDayLetter(int weekday) {
    const days = ['M', 'T', 'W', 'T', 'F', 'S', 'S'];
    return days[(weekday - 1) % 7];
  }

  double max(double a, double b) => a > b ? a : b;
}
