import 'package:excel_app/constants/app_assets.dart';
import 'package:excel_app/constants/app_colors.dart';
import 'package:excel_app/l10n/l10n_extension.dart';
import 'package:excel_app/master/app_drawer.dart';
import 'package:excel_app/utility/extentions/context_extnetions.dart';
import 'package:excel_app/utility/firebase_messaging_services.dart';
import 'package:excel_app/widget/app_asset_image.dart';
import 'package:excel_app/widget/dashboard_scroll_item_view.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

class MasterPage extends StatefulWidget {
  const MasterPage({
    required this.navigationShell,
    super.key,
  });
  final StatefulNavigationShell navigationShell;

  @override
  State<MasterPage> createState() => _MasterPageState();
}

class _MasterPageState extends State<MasterPage> {
  final _scaffoldKey = GlobalKey<ScaffoldState>();

  void _goBranch(int index) {
    widget.navigationShell.goBranch(
      index,
      initialLocation: index == widget.navigationShell.currentIndex,
    );
  }

  @override
  void initState() {
    super.initState();
    FirebaseMessagingService().initialize();
  }

  @override
  Widget build(BuildContext context) {
    return DashBoardScrollItemView(
      child: Scaffold(
        key: _scaffoldKey,
        appBar: !context.isDesktop
            ? AppBar(
                elevation: 0,
                // title: getTitle,
                centerTitle: false,
                backgroundColor: AppColors.white,
                leading: InkWell(
                  onTap: () => _scaffoldKey.currentState?.openDrawer(),
                  child: const Padding(
                    padding: EdgeInsets.all(15),
                    child: AppAssetImage(
                      AppAssets.drawerIcon,
                      height: 18,
                      width: 24,
                    ),
                  ),
                ),
              )
            : null,
        drawer: !context.isDesktop
            ? AppDrawer(currentIndex: widget.navigationShell.currentIndex, onChanged: _goBranch)
            : null,
        body: Row(
          children: [
            if (context.isDesktop) ...[
              AppDrawer(currentIndex: widget.navigationShell.currentIndex, onChanged: _goBranch),
              Expanded(
                child: widget.navigationShell,
              ),
            ] else
              Expanded(child: widget.navigationShell),
          ],
        ),
      ),
    );
  }

  String get title {
    switch (widget.navigationShell.currentIndex) {
      case 0:
        return context.l10n.home;
      case 1:
        return context.l10n.projects;
      case 2:
        return context.l10n.users;
      case 3:
        return context.l10n.rollManagement;
      case 4:
        return '';
      case 5:
        return context.l10n.profile;

      default:
        return '';
    }
  }

  Text get getTitle => Text(
        title,
        style: Theme.of(context).textTheme.headlineMedium?.copyWith(
              color: AppColors.black,
              fontSize: 18,
              letterSpacing: 1,
            ),
      );
}
