import 'package:excel_app/app/bloc/authentication_bloc.dart';
import 'package:excel_app/constants/app_assets.dart';
import 'package:excel_app/constants/app_colors.dart';
import 'package:excel_app/constants/app_constants.dart';
import 'package:excel_app/constants/app_strings.dart';
import 'package:excel_app/google_connect/view/connect_calender_dialog.dart';
import 'package:excel_app/l10n/l10n_extension.dart';
import 'package:excel_app/utility/extentions/context_extnetions.dart';
import 'package:excel_app/widget/app_asset_image.dart';
import 'package:excel_app/widget/app_drawer_item_view.dart';
import 'package:excel_app/widget/dialogs.dart';
import 'package:excel_app/widget/logout_dailog.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';

class AppDrawer extends StatelessWidget {
  const AppDrawer({
    required this.currentIndex,
    required this.onChanged,
    super.key,
  });
  final int currentIndex;
  final ValueChanged<int> onChanged;

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AuthenticationBloc, AuthenticationState>(
      builder: (context, state) {
        final isAuthState = state is Authenticated;
        if (!isAuthState) return const SizedBox();
        return Container(
          color: AppColors.white,
          constraints: const BoxConstraints(maxWidth: 287),
          height: double.infinity,
          padding: const EdgeInsets.symmetric(horizontal: 8),
          child: Column(
            children: [
              const Gap(20),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 13),
                child: Row(
                  children: [
                    const AppAssetImage(
                      AppAssets.logo,
                      height: 36,
                    ),
                    const Gap(8),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            AppConstants.projectName,
                            style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                                  fontSize: 18,
                                  fontWeight: FontWeight.w900,
                                  color: AppColors.dark,
                                ),
                          ),
                          Text(
                            'Management',
                            style: Theme.of(context).textTheme.bodyMedium,
                          ),
                        ],
                      ),
                    ),
                    InkWell(
                      onTap: () {
                        if (context.isMobile) {
                          context.pop();
                        }
                      },
                      child: const AppAssetImage(
                        AppAssets.drawerIcon,
                        height: 18,
                        width: 24,
                      ),
                    ),
                  ],
                ),
              ),
              const Gap(30),
              AppDrawerItemView(
                title: context.l10n.dashboard,
                icon: currentIndex == 0 ? AppAssets.selectedDashboardIcon : AppAssets.unselectedDashboardIcon,
                isSelecetd: currentIndex == 0,
                onTap: () {
                  if (context.isMobile) {
                    Navigator.pop(context);
                  }
                  onChanged.call(0);
                },
              ),
              AppDrawerItemView(
                title: context.l10n.home,
                icon: currentIndex == 1 ? AppAssets.selectedHomeIcon : AppAssets.unselectedHomeIcon,
                isSelecetd: currentIndex == 1,
                onTap: () {
                  if (context.isMobile) {
                    Navigator.pop(context);
                  }
                  onChanged.call(1);
                },
              ),
              AppDrawerItemView(
                title: context.l10n.projects,
                icon: currentIndex == 2 ? AppAssets.selectedProjectIcon : AppAssets.unselectedProjectIcon,
                isSelecetd: currentIndex == 2,
                onTap: () {
                  if (context.isMobile) {
                    Navigator.pop(context);
                  }
                  onChanged.call(2);
                },
              ),
              const Divider(),
              const Gap(14),
              if (state.user.role == AppStrings.admin)
                AppDrawerItemView(
                  title: context.l10n.users,
                  icon: currentIndex == 3 ? AppAssets.selectedUserIcon : AppAssets.unselectedUserIcon,
                  isSelecetd: currentIndex == 3,
                  onTap: () {
                    if (context.isMobile) {
                      Navigator.pop(context);
                    }
                    onChanged.call(3);
                  },
                ),
              if (state.user.role != AppStrings.admin)
                AppDrawerItemView(
                  title: context.l10n.chat,
                  icon: currentIndex == 6 ? AppAssets.selectedChatIcon : AppAssets.unselectedChatIcon,
                  isSelecetd: currentIndex == 6,
                  onTap: () {
                    if (context.isMobile) {
                      Navigator.pop(context);
                    }
                    onChanged.call(6);
                  },
                ),
              AppDrawerItemView(
                title: '${context.l10n.connectGoogleCal}.',
                icon: AppAssets.unselectedGoogleConnectIcon,
                // isSelecetd: currentIndex == 4,
                onTap: () {
                  if (context.isMobile) {
                    Navigator.pop(context);
                  }
                  DailogBox.showBluredBgDailog(
                    barrierDismissible: false,
                    context,
                    ConnectCalenderDialog(
                      connectedMail: state.user.isConnectedToGoogleCalendar ?? false ? '${state.user.email}' : null,
                    ),
                  );
                },
              ),
              if (state.user.role == AppStrings.admin)
                AppDrawerItemView(
                  title: context.l10n.holidayCategories,
                  icon: currentIndex == 4 ? AppAssets.selectedHolidayIcon : AppAssets.unselectedHolidayIcon,
                  isSelecetd: currentIndex == 4,
                  onTap: () {
                    if (context.isMobile) {
                      Navigator.pop(context);
                    }
                    onChanged.call(4);
                  },
                ),
              AppDrawerItemView(
                title: context.l10n.profile,
                icon: currentIndex == 5 ? AppAssets.selectedProfileIcon : AppAssets.unselectedProfileIcon,
                isSelecetd: currentIndex == 5,
                onTap: () {
                  if (context.isMobile) {
                    Navigator.pop(context);
                  }
                  onChanged.call(5);
                },
              ),
              AppDrawerItemView(
                title: context.l10n.logout,
                icon: AppAssets.unselectedLogoutIcon,
                onTap: () {
                  if (context.isMobile) {
                    Navigator.pop(context);
                  }
                  DailogBox.showBluredBgDailog(context, const LogoutDailog());
                },
              ),
            ],
          ),
        );
      },
    );
  }
}
