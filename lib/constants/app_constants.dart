class AppConstants {
  // Project name
  static const String projectName = 'ikPlanner';

  // Project Leader Type
  static const String leaderUser = 'LEADER_USER';
  static const String onlyLeader = 'ONLY_LEADER';
  static const String doctorOne = 'DOCTOR1';
  static const String doctorTwo = 'DOCTOR2';
  static const String firstYear = 'FIRST_YEAR';
  static const String secondYear = 'SECOND_YEAR';
  static const String thirdYear = 'THIRD_YEAR';
  static const String lastYear = 'LAST_YEAR';
  static const String student = 'STUDENT';
  static const String admin = 'ADMIN';

  static const String message = 'message';
  static const String swapRequest = 'swap_request';

  static const String sunday = 'SUNDAY';
  static const String monday = 'MONDAY';
  static const String tuesday = 'TUESDAY';
  static const String wednesday = 'WEDNESDAY';
  static const String thursday = 'THURSDAY';
  static const String friday = 'FRIDAY';
  static const String saturday = 'SATURDAY';

  // ________Assignment Status_________
  static const String completed = 'COMPLETED';
  static const String pendingAssignment = 'PENDING';
  static const String inProgress = 'IN_PROGRESS';

  // ________Holiday Type_________
  static const String holiday = 'HOLIDAY';
  static const String otherHoliday = 'OTHER_HOLIDAY';
  static const String prohibitedDay = 'PROHIBITED_DAY';

  static const String planningDetails = 'Planning';
  static const String basicDetails = 'Basic Details';
  static const String shiftSetting = 'Shift Setting';
  static const String holidays = 'Holidays';
  static const String usersAndSettings = 'Users & Settings';
  static const String leaderAndUser = 'Leader + User';
  static const String leaderOnly = 'Leader Only';
  static const String doctor1 = 'Doctor 1';
  static const String doctor2 = 'Doctor 2';
  static const String firstYearResident = 'First year resident';
  static const String secondYearResident = 'Second year resident';
  static const String thirdYearResident = 'Third year resident';
  static const String lastYearResident = 'Last year resident';
  static const String students = 'Student';
  static const String localHolidays = 'Local holidays';
  static const String otherHolidays = 'Other holidays';
  static const String holiWeekAndChristmas = 'Holi week & christmas';
  static const String psmarCompensatory = 'Psmar compensatory';
  static const String prohibitedDays = 'Prohibited days';
  // static const String doctors = 'Doctors';
  static const String availability = 'Availability';
  static const String details = 'Details';
  static const String accepted = 'Accepted';
  static const String pending = 'Pending';
  static const String rejected = 'Rejected';
  static const String personalDays = 'Personal Days ';
  static const String conferences = 'Conferences';
  static const String vactions = 'Vactions';
  static const String otherShift = 'Other Shifts';
  static const String notAvailable = 'Not Available';
  static const String notes =
      'Make sure that you have sufficient users for each Role.\n\nUse this formula:\nD  = MR * (B+1)\n\nMR: minimum user required per shift\nB : Avoid shift days\nD : number of users required for that role';

  static const List<MapEntry<String, String>> daysList = [
    MapEntry('Mon', 'MONDAY'),
    MapEntry('Tue', 'TUESDAY'),
    MapEntry('Wed', 'WEDNESDAY'),
    MapEntry('Thu', 'THURSDAY'),
    MapEntry('Fri', 'FRIDAY'),
    MapEntry('Sat', 'SATURDAY'),
    MapEntry('Sun', 'SUNDAY'),
  ];
  static List<int> monthList = List.generate(12, (index) => index + 1);
  static List<String> typeList = [
    'Doctor 1',
    'Doctor 2',
    'First year resident',
    'Second year resident',
    'Third year resident',
    'Last Year Resident',
    'Student',
  ];
  static List<String> holidaysList = [
    AppConstants.holiday,
    AppConstants.otherHoliday,
    AppConstants.prohibitedDay,
  ];

  static String holidayNameFromKey(String key) {
    switch (key) {
      case AppConstants.holiday:
        return AppConstants.holidays;
      case AppConstants.otherHoliday:
        return AppConstants.otherHolidays;
      case AppConstants.prohibitedDay:
        return AppConstants.prohibitedDays;
      default:
        return '';
    }
  }

  static List<String> typesOfAvailabilityList = [
    AppConstants.personalDays,
    AppConstants.conferences,
    AppConstants.vactions,
    AppConstants.otherShift,
    AppConstants.notAvailable,
  ];
}
