import 'dart:io';
import 'dart:typed_data';

import 'package:encrypt/encrypt.dart' as encrypt;

class EncryptionService {
  static final _key = encrypt.Key.fromBase64('TygXs0L0GZmUDOC/WmTMAwXtAPuz07l01W2DZNRB4eQ=');

  static final _iv = encrypt.IV.fromBase64('OLQGoJjrGGZGdlSVUDXMkQ==');

  static final _encrypter = encrypt.Encrypter(encrypt.AES(_key));

  static String encryptText(String plainText) {
    final encrypted = _encrypter.encrypt(plainText, iv: _iv);
    return encrypted.base64;
  }

  static String decryptText(String encryptedText) {
    final encrypted = encrypt.Encrypted.fromBase64(encryptedText);
    return _encrypter.decrypt(encrypted, iv: _iv);
  }

  static Uint8List encryptFile(File file) {
    final bytes = file.readAsBytesSync();
    final encrypted = _encrypter.encryptBytes(bytes, iv: _iv);
    return encrypted.bytes;
  }

  static Uint8List encryptFileBytes(Uint8List bytes) {
    final encrypted = _encrypter.encryptBytes(bytes, iv: _iv);
    return encrypted.bytes;
  }

  static Uint8List decryptFile(Uint8List bytes) {
    final encrypted = encrypt.Encrypted(bytes);
    return Uint8List.fromList(_encrypter.decryptBytes(encrypted, iv: _iv));
  }
}
