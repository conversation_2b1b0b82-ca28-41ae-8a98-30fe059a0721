import 'dart:convert';
import 'dart:developer';
// ignore: avoid_web_libraries_in_flutter
import 'dart:html' as html;
import 'dart:typed_data';

import 'package:easy_debounce/easy_debounce.dart';
import 'package:excel_app/app/bloc/authentication_bloc.dart';
import 'package:excel_app/calender/gride_view/monthy_grid_calender_view.dart';
import 'package:excel_app/calender/list_view/project_monthly_cal_view.dart';
import 'package:excel_app/calender/model/project_staff_model.dart';
import 'package:excel_app/constants/app_assets.dart';
import 'package:excel_app/constants/app_colors.dart';
import 'package:excel_app/constants/app_constants.dart';
import 'package:excel_app/constants/app_strings.dart';
import 'package:excel_app/holiday_category/model/holiday_catergory_model.dart';
import 'package:excel_app/home/<USER>/user_swap_model.dart';
import 'package:excel_app/injector/injector.dart';
import 'package:excel_app/projects/model/project_calendardate_model.dart';
import 'package:excel_app/projects/model/project_model.dart';
import 'package:excel_app/projects/model/swap_availability_model.dart';
import 'package:excel_app/projects/model/user_project_calendar_date_model.dart';
import 'package:excel_app/projects/repository/i_project_repository.dart';
import 'package:excel_app/projects/response/project_planning_response.dart';
import 'package:excel_app/users/model/user_model.dart';
import 'package:excel_app/utility/helpers/utility.dart';
import 'package:excel_app/widget/app_asset_image.dart';
import 'package:excel_app/widget/app_text_form_field.dart';
import 'package:excel_app/widget/container_widget.dart';
import 'package:excel_app/widget/custome_button_gradiant_widget.dart';
import 'package:excel_app/widget/dialogs.dart';
import 'package:file_saver/file_saver.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:path/path.dart' as p;

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  ValueNotifier<bool> isGrideView = ValueNotifier<bool>(true);
  ValueNotifier<DateTime> currentMonth = ValueNotifier<DateTime>(DateTime.now());
  final selectProject = ValueNotifier<ProjectModel?>(null);
  final isLoading = ValueNotifier<bool>(false);
  final isMonthChangeLoading = ValueNotifier<bool>(false);
  final leaderId = ValueNotifier<int>(0);
  final isButtonLoading = ValueNotifier<bool>(false);

  final projectStartDate = ValueNotifier<DateTime>(DateTime.now());
  final projectEndDate = ValueNotifier<DateTime>(DateTime.now());
  final userData = ValueNotifier<ProjectPlanningResponse?>(null);
  final projectUserList = ValueNotifier<List<ProjectStaffMember>>([]);
  final listOfHolidayCategory = ValueNotifier<List<HolidayCategoryModel>>([]);
  final startDate = ValueNotifier<DateTime>(DateTime.now());
  final endDate = ValueNotifier<DateTime>(DateTime.now());

  final calenderDateList = ValueNotifier<List<ProjectCalendarDateModel>>([]);
  final userProjectConflictDates = ValueNotifier<List<UserProjectCalendarDateModel>>([]);

  final swapValue = ValueNotifier<SwapUserAvailabilityDate?>(null);
  final isSwapLoading = ValueNotifier<bool>(false);
  final exportCalendarLoading = ValueNotifier<bool>(false);
  final exportIcalLoading = ValueNotifier<bool>(false);
  DateTime oneMonthBeforeEndDate = DateTime.now();

  @override
  void initState() {
    super.initState();
    loadProject();
  }

  final hasProjects = ValueNotifier<bool>(true);

  Future<void> loadProject() async {
    isLoading.value = true;
    final failOrSuccess = await getIt<IProjectRepository>().getProject();
    failOrSuccess.fold(
      (l) {
        isLoading.value = false;
        hasProjects.value = false;
      },
      (r) {
        if (r.data.isEmpty) {
          isLoading.value = false;
          hasProjects.value = false;
        } else {
          hasProjects.value = true;
          final approvedProjects = r.data.where((project) => project.isApproved == 1).toList();
          if (approvedProjects.isEmpty) {
            isLoading.value = false;
            hasProjects.value = false;
            return;
          }
          selectProject.value = approvedProjects.first;
          _initializeDates();
          getProjectPlanningDetails();
          getCalendarDateList();
        }
      },
    );
  }

  void _initializeDates() {
    projectStartDate.value = selectProject.value?.startDate ?? DateTime.now();
    projectEndDate.value = selectProject.value?.endDate ?? DateTime.now();
    startDate.value = DateTime(projectStartDate.value.year, projectStartDate.value.month);
    endDate.value = DateTime(projectStartDate.value.year, projectStartDate.value.month + 1, 0);
    oneMonthBeforeEndDate = DateTime(
      selectProject.value?.endDate?.year ?? DateTime.now().year,
      selectProject.value?.endDate?.month ?? DateTime.now().month - 1,
    );
  }

  void _changeMonth(int offset) {
    projectStartDate.value = DateTime(
      projectStartDate.value.year,
      projectStartDate.value.month + offset,
    );
    startDate.value = DateTime(projectStartDate.value.year, projectStartDate.value.month);
    endDate.value = DateTime(projectStartDate.value.year, projectStartDate.value.month + 1, 0);
    listOfHolidayCategory.value = [];
    calenderDateList.value = [];
    getProjectPlanningDetails();
    getCalendarDateList();
  }

  Future<void> getProjectPlanningDetails({bool isChangeProject = false}) async {
    if (isChangeProject) {
      isLoading.value = true;
    } else {
      isMonthChangeLoading.value = true;
    }
    final failOrSucess = await getIt<IProjectRepository>().projectPlanning(
      projectId: selectProject.value?.id.toString() ?? '',
      startDate: startDate.value,
      endDate: endDate.value,
    );
    failOrSucess.fold(
      (l) {
        isMonthChangeLoading.value = false;
        isLoading.value = false;
        Utility.toast(message: l.message);
      },
      (r) {
        userData.value = r;
        leaderId.value = r.leader?.id ?? 0;
        projectDetails();
      },
    );
  }

  Future<void> getCalendarDateList() async {
    final failOrSucess = await getIt<IProjectRepository>().getCalendarDateList(
      projectId: selectProject.value?.id.toString() ?? '',
      startDate: startDate.value,
      endDate: endDate.value,
    );
    failOrSucess.fold(
      (l) {
        Utility.toast(message: l.message);
      },
      (r) {
        calenderDateList.value = r.data;
      },
    );
  }

  Future<void> projectDetails() async {
    final projectUserData = userData.value?.data;

    // Check if projectUserData is not null before mapping
    if (projectUserData != null) {
      projectUserList.value = projectUserData.map((user) {
        userProjectConflictDates.value = user.userProjectConflictDates.map((e) => e).toList();

        final userList = user;
        return ProjectStaffMember(
          userModel: userList,
        );
      }).toList();
    }
    isLoading.value = false;
    isMonthChangeLoading.value = false;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      // backgroundColor: AppColors.background,
      body: ValueListenableBuilder<ProjectModel?>(
        valueListenable: selectProject,
        builder: (context, projectValue, _) {
          return BlocBuilder<AuthenticationBloc, AuthenticationState>(
            builder: (context, state) {
              return ContainerWidget(
                padding: EdgeInsets.zero,
                child: ValueListenableBuilder(
                  valueListenable: isLoading,
                  builder: (context, loading, _) {
                    if (loading) {
                      return Utility.progressIndicator();
                    }
                    return Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Padding(
                          padding: const EdgeInsets.all(20),
                          child: Row(
                            children: [
                              Expanded(
                                child: Text(
                                  'Home',
                                  style:
                                      Theme.of(context).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.w600),
                                ),
                              ),
                              if (projectValue != null)
                                ValueListenableBuilder<ProjectModel?>(
                                  valueListenable: selectProject,
                                  builder: (context, selected, _) {
                                    return AppTextFormField(
                                      controller: TextEditingController(text: selected?.name ?? ''),
                                      maxWidth: 263,
                                      maxHeight: 32,
                                      contentPadding: const EdgeInsets.symmetric(vertical: 6, horizontal: 14),
                                      readOnly: true,
                                      suffixIcon: const Padding(
                                        padding: EdgeInsets.all(11),
                                        child: AppAssetImage(
                                          AppAssets.arrowDownIcon,
                                          height: 18,
                                          width: 18,
                                        ),
                                      ),
                                      onTap: () async {
                                        final selectedProject = await DailogBox.selectProjectDailog(
                                          context,
                                          selectedProject: selected,
                                        );
                                        if (selectedProject != null) {
                                          selectProject.value = selectedProject;
                                          _initializeDates();
                                          await getProjectPlanningDetails(
                                            isChangeProject: true,
                                          );
                                          await getCalendarDateList();
                                        }
                                      },
                                    );
                                  },
                                ),
                            ],
                          ),
                        ),
                        Container(
                          height: 1,
                          color: AppColors.strokeColor,
                        ),
                        Padding(
                          padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 20),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              // Left side - Month selection
                              Row(
                                children: [
                                  InkWell(
                                    onTap: () {
                                      EasyDebounce.debounce('month', const Duration(milliseconds: 500), () {
                                        if (projectStartDate.value
                                            .isAfter(selectProject.value?.startDate ?? DateTime.now())) {
                                          setState(() {
                                            _changeMonth(-1);
                                          });
                                        }
                                      });
                                    },
                                    child: AppAssetImage(
                                      AppAssets.leftArrowIcon,
                                      color: projectStartDate.value
                                              .isAfter(selectProject.value?.startDate ?? DateTime.now())
                                          ? AppColors.primary
                                          : AppColors.subText,
                                    ),
                                  ),
                                  const Gap(18),
                                  Text(
                                    Utility().formatMonthYear(projectStartDate.value),
                                    style:
                                        Theme.of(context).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600),
                                  ),
                                  const Gap(18),
                                  InkWell(
                                    onTap: () {
                                      EasyDebounce.debounce('month', const Duration(milliseconds: 500), () {
                                        if (projectStartDate.value.isBefore(oneMonthBeforeEndDate)) {
                                          setState(() {
                                            _changeMonth(1);
                                          });
                                        }
                                      });
                                    },
                                    child: AppAssetImage(
                                      AppAssets.rightArrowIcon,
                                      color: projectStartDate.value.isBefore(oneMonthBeforeEndDate)
                                          ? AppColors.primary
                                          : AppColors.subText,
                                    ),
                                  ),
                                ],
                              ),
                              // Right side - Action buttons
                              ValueListenableBuilder(
                                valueListenable: userData,
                                builder: (context, userData, _) {
                                  return Row(
                                    children: [
                                      if (state is Authenticated &&
                                          state.user.role != AppConstants.admin &&
                                          userData?.isApproved == 1)
                                        ValueListenableBuilder(
                                          valueListenable: isButtonLoading,
                                          builder: (context, loading, _) {
                                            return CustomeButtonGradiantWidget(
                                              onTap: () {
                                                DailogBox.selectSwapAvailabilityDailog(
                                                  context,
                                                  userSwapModel: UserSwapModel(
                                                    selectedProject: selectProject.value,
                                                    currentMonth: projectStartDate.value,
                                                  ),
                                                );
                                              },
                                              width: 158,
                                              buttonText: 'Swap Availability',
                                              isUseContainerBorder: true,
                                              height: 30,
                                              isLoading: loading,
                                              textColor: AppColors.primary,
                                              // pading: const EdgeInsets.symmetric(vertical: 6, horizontal: 16),
                                            );
                                          },
                                        ),
                                      const Gap(16),
                                      ValueListenableBuilder(
                                        valueListenable: exportIcalLoading,
                                        builder: (context, loading, _) {
                                          return CustomeButtonGradiantWidget(
                                            onTap: (!loading &&
                                                    state is Authenticated &&
                                                    (state.user.isConnectedToGoogleCalendar ?? false))
                                                ? exportIcalCalendar
                                                : null,
                                            buttonText: 'Export to Ical Cal.',
                                            isLoading: loading,
                                            isUseContainerBorder: true,
                                            textColor: state is Authenticated &&
                                                    (state.user.isConnectedToGoogleCalendar ?? false)
                                                ? AppColors.primary
                                                : AppColors.subText,
                                            height: 32,
                                            width: 174,
                                          );
                                        },
                                      ),
                                      const Gap(16),
                                      ValueListenableBuilder(
                                        valueListenable: exportCalendarLoading,
                                        builder: (context, loading, _) {
                                          return CustomeButtonGradiantWidget(
                                            onTap: (!loading &&
                                                    state is Authenticated &&
                                                    (state.user.isConnectedToGoogleCalendar ?? false))
                                                ? exportGoogleCalendar
                                                : null,
                                            buttonText: 'Export to Google Cal.',
                                            isLoading: loading,
                                            isUseContainerBorder: true,
                                            textColor: state is Authenticated &&
                                                    (state.user.isConnectedToGoogleCalendar ?? false)
                                                ? AppColors.primary
                                                : AppColors.subText,
                                            height: 32,
                                            width: 174,
                                          );
                                        },
                                      ),
                                      const SizedBox(width: 16),
                                      ValueListenableBuilder(
                                        valueListenable: isSwapLoading,
                                        builder: (context, loading, _) {
                                          return CustomeButtonGradiantWidget(
                                            onTap: !loading && projectValue != null ? exportPdf : null,
                                            buttonText: 'Export pdf',
                                            isUseContainerBorder: true,
                                            textColor: projectValue != null ? AppColors.primary : AppColors.subText,
                                            height: 32,
                                            width: 103,
                                            isLoading: loading,
                                          );
                                        },
                                      ),
                                      const SizedBox(width: 16),
                                      ValueListenableBuilder<bool>(
                                        valueListenable: isGrideView,
                                        builder: (context, grideView, _) {
                                          return Row(
                                            children: [
                                              InkWell(
                                                onTap: () {
                                                  isGrideView.value = false;
                                                },
                                                child: AppAssetImage(
                                                  !grideView
                                                      ? AppAssets.selectedMenuIcon
                                                      : AppAssets.unselectedMenuIcon,
                                                ),
                                              ),
                                              const Gap(16),
                                              InkWell(
                                                onTap: () {
                                                  isGrideView.value = true;
                                                },
                                                child: AppAssetImage(
                                                  grideView ? AppAssets.selectedGridIcon : AppAssets.unselectedGridIcon,
                                                ),
                                              ),
                                            ],
                                          );
                                        },
                                      ),
                                    ],
                                  );
                                },
                              ),
                            ],
                          ),
                        ),
                        Flexible(
                          child: ValueListenableBuilder(
                            valueListenable: isMonthChangeLoading,
                            builder: (context, loading, _) {
                              if (loading) {
                                return Utility.progressIndicator();
                              }
                              return ValueListenableBuilder(
                                valueListenable: projectUserList,
                                builder: (context, userList, _) {
                                  return ValueListenableBuilder(
                                    valueListenable: projectStartDate,
                                    builder: (context, month, _) {
                                      return ValueListenableBuilder(
                                        valueListenable: leaderId,
                                        builder: (context, leadernewId, _) {
                                          return ValueListenableBuilder<bool>(
                                            valueListenable: isGrideView,
                                            builder: (context, grideView, _) {
                                              if (grideView) {
                                                return BlocBuilder<AuthenticationBloc, AuthenticationState>(
                                                  builder: (context, state) {
                                                    return MonthyGridCalenderView(
                                                      leaderId: leadernewId,
                                                      key: ValueKey(month),
                                                      initialMonth: month,
                                                      user: state is Authenticated ? state.user : const UserModel(),
                                                      assignments: userList,
                                                    );
                                                  },
                                                );
                                              }
                                              return ProjectMonthlyListCalenderView(
                                                projectLeaderId: projectValue?.leaderType == AppConstants.leaderUser
                                                    ? projectValue?.leaderUserId ?? 0
                                                    : null,
                                                projectCalendarDates: calenderDateList.value,
                                                key: ValueKey(month),
                                                currentMonth: month,
                                                staffMembers: userList,
                                                userProjectConflictDates: userProjectConflictDates.value,
                                                leaderId: leadernewId,
                                                isAdmin:
                                                    state is Authenticated && state.user.role == AppConstants.admin,
                                              );
                                            },
                                          );
                                        },
                                      );
                                    },
                                  );
                                },
                              );
                            },
                          ),
                        ),
                      ],
                    );
                  },
                ),
              );
            },
          );
        },
      ),
    );
  }

  Future<void> swapUserAvailabilityDate() async {
    isSwapLoading.value = true;

    final failOrSuccess = await getIt<IProjectRepository>().swapAvailabilityDate(
      projectId: selectProject.value?.id.toString() ?? '',
      swapUserAvailabilityDate: swapValue.value,
    );
    failOrSuccess.fold(
      (l) {
        Utility.toast(message: l.message);
        isSwapLoading.value = false;
        swapValue.value = null;
      },
      (r) {
        Utility.toast(message: r.message);
        isSwapLoading.value = false;
        swapValue.value = null;
        context.pop();
        listOfHolidayCategory.value = [];
        calenderDateList.value = [];
        getProjectPlanningDetails();
      },
    );
  }

  Future<void> exportPdf() async {
    isSwapLoading.value = true;
    log('message');
    final failOrSuccess = await getIt<IProjectRepository>().exportPdf(
      projectId: selectProject.value?.id.toString() ?? '',
      startDate: startDate.value,
      endDate: endDate.value,
    );
    await failOrSuccess.fold(
      (l) {
        Utility.toast(message: l.message);
        isSwapLoading.value = false;
      },
      (r) async {
        log('test file before ${AppStrings.storageBaseUrl}${r.path}');
        try {
          html.window.open('${AppStrings.storageBaseUrl}${r.path}', 'new tab');
        } catch (e) {
          // ignore: only_throw_errors
          throw e.toString();
        }
        // downloadExportFile('${AppStrings.storageBaseUrl}${r.path}');
        log('test file');
        Utility.toast(message: r.message);
        isSwapLoading.value = false;
      },
    );
  }

  Future<void> exportGoogleCalendar() async {
    exportCalendarLoading.value = true;

    final failOrSuccess = await getIt<IProjectRepository>().exportToGoogleCalendar(
      projectId: selectProject.value?.id.toString() ?? '',
    );
    await failOrSuccess.fold(
      (l) {
        Utility.toast(message: l.message);
        exportCalendarLoading.value = false;
      },
      (r) async {
        Utility.toast(message: r.message);
        exportCalendarLoading.value = false;
      },
    );
  }

  Future<void> exportIcalCalendar() async {
    exportIcalLoading.value = true;

    final failOrSuccess = await getIt<IProjectRepository>().exportToIcal(
      projectId: selectProject.value?.id.toString() ?? '',
    );
    await failOrSuccess.fold(
      (l) {
        Utility.toast(message: l.message);
        exportIcalLoading.value = false;
      },
      (r) async {
        final blob = html.Blob([r], 'text/calendar'); // optional MIME type
        final url = html.Url.createObjectUrlFromBlob(blob);
        final anchor = html.AnchorElement(href: url)
          ..setAttribute('download', '${selectProject.value?.name}.ics') // <-- use .ics extension
          ..click();
        html.Url.revokeObjectUrl(url);

        exportIcalLoading.value = false;
        Utility.toast(message: 'Calendar file downloaded successfully');
      },
    );
  }

  Future<void> downloadExportFile(String file) async {
    log('asghagsgha');
    final bytes = Uint8List.fromList(utf8.encode(file));
    final pdf = p.extension(file).replaceFirst('.', '');
    await FileSaver.instance.saveFile(
      name: 'export_pdf',
      bytes: bytes,
      ext: pdf.isNotEmpty ? pdf : 'txt',
    );
  }
}
