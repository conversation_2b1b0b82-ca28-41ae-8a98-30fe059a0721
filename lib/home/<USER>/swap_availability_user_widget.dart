import 'package:excel_app/constants/app_colors.dart';
import 'package:excel_app/constants/app_constants.dart';
import 'package:excel_app/projects/model/project_model.dart';
import 'package:excel_app/users/model/user_model.dart';
import 'package:excel_app/widget/custom_network_image.dart';
import 'package:excel_app/widget/custome_button_gradiant_widget.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';

class SwapAvailabilityUserWidget extends StatelessWidget {
  const SwapAvailabilityUserWidget({
    super.key,
    this.user,
    this.projectmodel,
    this.onTap,
    this.selected = false,
    this.isLoading = false,
  });
  final UserModel? user;
  final ProjectModel? projectmodel;
  final void Function()? onTap;
  final bool selected;
  final bool isLoading;

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      mainAxisSize: MainAxisSize.min,
      children: [
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(100),
              child: CustomNetworkImage(
                imageUrl: user?.imagePath ?? '',
                height: 24,
                width: 24,
                isSmallImage: true,
              ),
            ),
            const Gap(8),
            Flexible(
              child: Text(
                user?.name ?? '',
                maxLines: 1,
                style: Theme.of(context).textTheme.bodyMedium,
              ),
            ),
            if (projectmodel?.leaderType == AppConstants.leaderUser && projectmodel?.leaderUserId == user?.id) ...[
              const Gap(8),
              Container(
                width: 40,
                height: 18,
                alignment: Alignment.center,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(5),
                  color: AppColors.lightMint,
                ),
                child: Text(
                  'L + U',
                  maxLines: 1,
                  style: Theme.of(context).textTheme.labelSmall?.copyWith(fontWeight: FontWeight.w500),
                ),
              ),
            ],
          ],
        ),
        CustomeButtonGradiantWidget(
          buttonText: selected ? 'Swap Request Sent' : 'Send Swap Request',
          isUseContainerBorder: true,
          width: 135,
          height: 32,
          isLoading: isLoading,
          textColor: selected ? AppColors.subText : AppColors.primary,
          fontSize: 12,
          onTap: onTap,
        ),
      ],
    );
  }
}
