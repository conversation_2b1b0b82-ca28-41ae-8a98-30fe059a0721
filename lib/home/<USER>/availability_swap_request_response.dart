import 'package:equatable/equatable.dart';
import 'package:excel_app/home/<USER>/availability_swap_model.dart';

class AvailabilitySwapRequestResponse extends Equatable {
  const AvailabilitySwapRequestResponse({
    this.status,
    this.message,
    this.data,
  });

  factory AvailabilitySwapRequestResponse.fromJson(Map<String, dynamic> json) {
    return AvailabilitySwapRequestResponse(
      status: json['status'] as String?,
      message: json['message'] as String?,
      data: json['data'] == null ? null : AvailabilitySwapModel.fromJson(json['data'] as Map<String, dynamic>),
    );
  }

  final String? status;
  final String? message;
  final AvailabilitySwapModel? data;

  Map<String, dynamic> toJson() => {
        'status': status,
        'message': message,
        'data': data?.toJson(),
      };

  @override
  List<Object?> get props => [
        status,
        message,
        data,
      ];
}
