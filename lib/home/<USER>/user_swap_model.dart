import 'package:excel_app/projects/model/project_model.dart';

class UserSwapModel {
  UserSwapModel({
    this.selectedDate,
    this.userCurrentDate,
    this.currentMonth,
    this.selectedProject,
  });
  DateTime? selectedDate;
  DateTime? userCurrentDate;
  DateTime? currentMonth;
  ProjectModel? selectedProject;

  UserSwapModel copyWith({
    DateTime? selectedDate,
    DateTime? userCurrentDate,
    DateTime? currentMonth,
    ProjectModel? selectedProject,
  }) {
    return UserSwapModel(
      selectedDate: selectedDate ?? this.selectedDate,
      userCurrentDate: userCurrentDate ?? this.userCurrentDate,
      currentMonth: currentMonth ?? this.currentMonth,
      selectedProject: selectedProject ?? this.selectedProject,
    );
  }
}
