// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:excel_app/constants/app_assets.dart';
import 'package:excel_app/constants/app_colors.dart';
import 'package:excel_app/home/<USER>/user_swap_model.dart';
import 'package:excel_app/home/<USER>/swap_calender_day_iew.dart';
import 'package:excel_app/widget/app_asset_image.dart';
import 'package:excel_app/widget/dialogs.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';

class SwapUserCalView extends StatefulWidget {
  const SwapUserCalView({
    this.userSwapModel,
    super.key,
    this.firstDayOfWeek = 0, // 👈 default Monday
  });

  final int firstDayOfWeek;
  final UserSwapModel? userSwapModel;

  static final Map<String, List<String>> _weekDaysCache = {};
  static final Map<String, List<_DayData>> _daysDataCache = {};

  @override
  State<SwapUserCalView> createState() => _SwapUserCalViewState();
}

class _SwapUserCalViewState extends State<SwapUserCalView> {
  List<String> _getWeekDays(int firstDayOfWeek) {
    final cacheKey = firstDayOfWeek.toString();
    return SwapUserCalView._weekDaysCache[cacheKey] ??= _calculateWeekDays(firstDayOfWeek);
  }

  List<String> _calculateWeekDays(int firstDayOfWeek) {
    final baseWeekDays = <String>['Mo', 'Tu', 'We', 'Th', 'Fr', 'Sa', 'Su'];

    // 👇 Rotate based on firstDayOfWeek
    return [
      ...baseWeekDays.sublist(firstDayOfWeek),
      ...baseWeekDays.sublist(0, firstDayOfWeek),
    ];
  }

  List<_DayData> _getDaysData() {
    final cacheKey =
        '${widget.userSwapModel?.currentMonth?.year}-${widget.userSwapModel?.currentMonth?.month}-${widget.firstDayOfWeek}';
    return SwapUserCalView._daysDataCache[cacheKey] ??= _calculateDaysData();
  }

  List<_DayData> _calculateDaysData() {
    final firstDayOfMonth = DateTime(
      widget.userSwapModel?.currentMonth?.year ?? DateTime.now().year,
      widget.userSwapModel?.currentMonth?.month ?? DateTime.now().month,
    );

    final daysInMonth = DateTime(
      widget.userSwapModel?.currentMonth?.year ?? DateTime.now().year,
      (widget.userSwapModel?.currentMonth?.month ?? DateTime.now().month) + 1,
    ).subtract(const Duration(days: 1)).day;

    // 👇 normalize weekday (Mon=1..Sun=7 → Sun=0, Mon=1..Sat=6)
    final weekdayIndex = (firstDayOfMonth.weekday + 6) % 7;
    final firstWeekday = (weekdayIndex - widget.firstDayOfWeek + 7) % 7;

    final daysInPreviousMonth = DateTime(
      widget.userSwapModel?.currentMonth?.year ?? DateTime.now().year,
      widget.userSwapModel?.currentMonth?.month ?? DateTime.now().month,
    ).subtract(const Duration(days: 1)).day;

    const totalDays = 42;

    return List.generate(totalDays, (index) {
      late final DateTime currentDate;
      late final int displayDay;
      var isCurrentMonth = true;

      if (index < firstWeekday) {
        displayDay = daysInPreviousMonth - (firstWeekday - index - 1);
        currentDate = DateTime(
          widget.userSwapModel?.currentMonth?.year ?? DateTime.now().year,
          (widget.userSwapModel?.currentMonth?.month ?? DateTime.now().month) - 1,
          displayDay,
        );
        isCurrentMonth = false;
      } else if (index < firstWeekday + daysInMonth) {
        displayDay = index - firstWeekday + 1;
        currentDate = DateTime(
          widget.userSwapModel?.currentMonth?.year ?? DateTime.now().year,
          widget.userSwapModel?.currentMonth?.month ?? DateTime.now().month,
          displayDay,
        );
      } else {
        displayDay = index - (firstWeekday + daysInMonth) + 1;
        currentDate = DateTime(
          widget.userSwapModel?.currentMonth?.year ?? DateTime.now().year,
          (widget.userSwapModel?.currentMonth?.month ?? DateTime.now().month) + 1,
          displayDay,
        );
        isCurrentMonth = false;
      }

      return _DayData(
        date: currentDate,
        isCurrentMonth: isCurrentMonth,
      );
    });
  }

  void _changeMonth(DateTime newMonth) {
    if (widget.userSwapModel?.selectedProject?.startDate != null &&
        newMonth.isBefore(
          DateTime(
            widget.userSwapModel!.selectedProject!.startDate!.year,
            widget.userSwapModel!.selectedProject!.startDate!.month,
          ),
        )) {
      return; // 👈 Block before start
    }
    if (widget.userSwapModel?.selectedProject?.endDate != null &&
        newMonth.isAfter(
          DateTime(
            widget.userSwapModel!.selectedProject!.endDate!.year,
            widget.userSwapModel!.selectedProject!.endDate!.month,
          ),
        )) {
      return; // 👈 Block after end
    }

    setState(() {
      widget.userSwapModel?.currentMonth = newMonth;

      // 🔥 FIX: Clear cache so _getDaysData recalculates
      SwapUserCalView._daysDataCache.clear();
    });
  }

  @override
  Widget build(BuildContext context) {
    final weekDays = _getWeekDays(widget.firstDayOfWeek);
    final daysData = _getDaysData();

    return RepaintBoundary(
      child: Container(
        decoration: BoxDecoration(
          color: AppColors.white,
          border: Border.all(color: AppColors.gray),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          children: [
            _MonthHeader(
              year: widget.userSwapModel?.currentMonth?.year ?? DateTime.now().year,
              month: widget.userSwapModel?.currentMonth?.month ?? DateTime.now().month,
              currentMonth: widget.userSwapModel?.currentMonth ?? DateTime.now(),
              projectStart: widget.userSwapModel?.selectedProject?.startDate,
              projectEnd: widget.userSwapModel?.selectedProject?.endDate,
              onPrevious: () {
                final prevMonth = DateTime(
                  widget.userSwapModel?.currentMonth?.year ?? DateTime.now().year,
                  (widget.userSwapModel?.currentMonth?.month ?? DateTime.now().month) - 1,
                );
                _changeMonth(prevMonth);
              },
              onNext: () {
                final nextMonth = DateTime(
                  widget.userSwapModel?.currentMonth?.year ?? DateTime.now().year,
                  (widget.userSwapModel?.currentMonth?.month ?? DateTime.now().month) + 1,
                );
                _changeMonth(nextMonth);
              },
            ),
            _WeekDaysHeader(weekDays: weekDays),
            _DaysGrid(
              userSwapModel: widget.userSwapModel,
              daysData: daysData,
            ),
          ],
        ),
      ),
    );
  }
}

class _MonthHeader extends StatelessWidget {
  const _MonthHeader({
    required this.year,
    required this.month,
    required this.onPrevious,
    required this.onNext,
    required this.currentMonth,
    this.projectStart,
    this.projectEnd,
  });

  final int year;
  final int month;
  final VoidCallback onPrevious;
  final VoidCallback onNext;
  final DateTime currentMonth;
  final DateTime? projectStart;
  final DateTime? projectEnd;

  bool get _canGoPrev {
    if (projectStart == null) return true;
    return !(currentMonth.year == projectStart!.year && currentMonth.month <= projectStart!.month);
  }

  bool get _canGoNext {
    if (projectEnd == null) return true;
    return !(currentMonth.year == projectEnd!.year && currentMonth.month >= projectEnd!.month);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
      decoration: const BoxDecoration(
        color: AppColors.gray,
        borderRadius: BorderRadius.vertical(top: Radius.circular(12)),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          InkWell(
            onTap: _canGoPrev ? onPrevious : null, // 🔥 FIX disable tap
            child: AppAssetImage(
              AppAssets.leftArrowIcon,
              color: _canGoPrev ? AppColors.primary : AppColors.subText, // 🔥 FIX color
            ),
          ),
          Center(
            child: Text(
              DateFormat('MMMM yyyy').format(DateTime(year, month)),
              style: Theme.of(context).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
          ),
          InkWell(
            onTap: _canGoNext ? onNext : null, // 🔥 FIX disable tap
            child: AppAssetImage(
              AppAssets.rightArrowIcon,
              color: _canGoNext ? AppColors.primary : AppColors.subText, // 🔥 FIX color
            ),
          ),
        ],
      ),
    );
  }
}

class _WeekDaysHeader extends StatelessWidget {
  const _WeekDaysHeader({required this.weekDays});

  final List<String> weekDays;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: weekDays.map((day) => _WeekDayLabel(day: day)).toList(),
      ),
    );
  }
}

class _WeekDayLabel extends StatelessWidget {
  const _WeekDayLabel({required this.day});

  final String day;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: 24,
      child: Center(
        child: Text(
          day,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
                color: AppColors.dark,
              ),
        ),
      ),
    );
  }
}

class _DaysGrid extends StatefulWidget {
  const _DaysGrid({
    required this.daysData,
    this.userSwapModel,
  });
  final List<_DayData> daysData;
  final UserSwapModel? userSwapModel;

  @override
  State<_DaysGrid> createState() => _DaysGridState();
}

class _DaysGridState extends State<_DaysGrid> {
  final _selectedDate = ValueNotifier<DateTime?>(null);

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder(
      valueListenable: _selectedDate,
      builder: (context, date, _) {
        return GridView.builder(
          physics: const NeverScrollableScrollPhysics(),
          shrinkWrap: true,
          padding: const EdgeInsets.symmetric(horizontal: 16),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 7,
            crossAxisSpacing: 4,
            mainAxisSpacing: 4,
            childAspectRatio: 3.5 / 3,
          ),
          itemCount: widget.daysData.length,
          itemBuilder: (context, index) {
            final dayData = widget.daysData[index];
            final isPastDate = dayData.date.isBefore(
              DateTime(
                DateTime.now().year,
                DateTime.now().month,
                DateTime.now().day,
              ),
            );

            final textColor = date == dayData.date
                ? AppColors.white
                : isPastDate
                    ? Colors.grey.withOpacity(0.5)
                    : dayData.isCurrentMonth
                        ? AppColors.dark
                        : Colors.grey.withOpacity(0.5);

            return SwapCalDayView(
              key: ValueKey('${dayData.date}'),
              displayDay: dayData.date.day,
              cellColor: dayData.isCurrentMonth
                  ? date == dayData.date
                      ? AppColors.primary
                      : Colors.white
                  : Colors.white,
              textColor: textColor,
              onPressed: (dayData.isCurrentMonth && !isPastDate)
                  ? () async {
                      _selectedDate.value = dayData.date;
                      context.pop();

                      if (widget.userSwapModel?.userCurrentDate != null) {
                        await DailogBox.swapAvailablePersonDailog(
                          context,
                          userSwapModel: UserSwapModel(
                            userCurrentDate: widget.userSwapModel?.userCurrentDate,
                            selectedProject: widget.userSwapModel?.selectedProject,
                            currentMonth: widget.userSwapModel?.currentMonth,
                            selectedDate: _selectedDate.value,
                          ),
                        );
                      } else {
                        await DailogBox.selectSwapAvailabilityDailog(
                          context,
                          userSwapModel: UserSwapModel(
                            userCurrentDate: _selectedDate.value,
                            selectedProject: widget.userSwapModel?.selectedProject,
                            currentMonth: widget.userSwapModel?.currentMonth,
                          ),
                        );
                      }
                    }
                  : null,
            );
          },
        );
      },
    );
  }
}

// Helper class for day data
class _DayData {
  final DateTime date;
  final bool isCurrentMonth;

  const _DayData({
    required this.date,
    required this.isCurrentMonth,
  });
}
