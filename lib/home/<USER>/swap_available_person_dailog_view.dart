// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:developer';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:easy_debounce/easy_debounce.dart';
import 'package:excel_app/app/bloc/authentication_bloc.dart';
import 'package:excel_app/chat/repository/i_chat_repository.dart';
import 'package:excel_app/chat/services/firebase_chat_service.dart';
import 'package:excel_app/constants/app_colors.dart';
import 'package:excel_app/constants/app_constants.dart';
import 'package:excel_app/home/<USER>/user_swap_model.dart';
import 'package:excel_app/home/<USER>/swap_availability_user_widget.dart';
import 'package:excel_app/injector/injector.dart';
import 'package:excel_app/users/model/user_model.dart';
import 'package:excel_app/utility/helpers/utility.dart';
import 'package:excel_app/utility/pagination_mixin.dart';
import 'package:excel_app/widget/custome_button_gradiant_widget.dart';
import 'package:excel_app/widget/popup_wrapper.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';

class SwapAvaiblePersonDailog extends StatefulWidget {
  const SwapAvaiblePersonDailog({
    super.key,
    this.userSwapModel,
  });

  final UserSwapModel? userSwapModel;

  @override
  State<SwapAvaiblePersonDailog> createState() => SwapAvaiblePersonDailogState();
}

class SwapAvaiblePersonDailogState extends State<SwapAvaiblePersonDailog> with PaginatisonMixin {
  final searchController = TextEditingController();

  final isSwapRequestLoading = ValueNotifier<bool>(false);

  bool isLoading = false;

  bool isLoadingMore = false;

  bool hasReachedMax = false;

  int page = 1;

  // List<UserModel> users = [];
  final users = ValueNotifier<List<UserModel>>([]);
  int? selectUserId;

  @override
  void initState() {
    super.initState();
    initiatePagination();
    load();
  }

  @override
  void dispose() {
    disposePagination();
    searchController.dispose();
    super.dispose();
  }

  @override
  void onReachedLast() {
    log('updated value$isLoadingMore $isLoading $hasReachedMax');
    if (isLoadingMore || isLoading || hasReachedMax) return;

    EasyDebounce.debounce('Select__User_Pagination', const Duration(milliseconds: 500), loadMore);
  }

  void _notify() {
    if (mounted) {
      setState(() {});
    }
  }

  @override
  Widget build(BuildContext context) {
    return PopUpWrapper(
      constraints: const BoxConstraints(maxWidth: 422, maxHeight: 348),
      physics: const NeverScrollableScrollPhysics(),
      showCancelButton: false,
      children: [
        BlocBuilder<AuthenticationBloc, AuthenticationState>(
          builder: (context, state) {
            if (state is Authenticated) {
              return ValueListenableBuilder(
                valueListenable: users,
                builder: (context, userList, _) {
                  return Column(
                    children: [
                      const Gap(20),
                      Text(
                        'Available Persons',
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                              fontSize: 18,
                              fontWeight: FontWeight.w600,
                            ),
                      ),
                      const Gap(6),
                      Text(
                        'On ${widget.userSwapModel?.selectedDate?.day}/${widget.userSwapModel?.selectedDate?.month}/${widget.userSwapModel?.selectedDate?.year}',
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: AppColors.subText,
                            ),
                      ),
                      const Gap(20),
                      if (isLoading) ...[
                        Utility.progressIndicator(),
                        const Gap(20),
                      ] else if (!isLoading && userList.isEmpty) ...[
                        const Gap(20),
                        Text('No User Found', style: Theme.of(context).textTheme.bodyMedium),
                        const Gap(40),
                      ] else
                        Column(
                          children: [
                            Container(
                              color: AppColors.white,
                              height: 170, // Set a fixed height
                              child: ListView.separated(
                                separatorBuilder: (context, index) {
                                  return const Column(
                                    children: [
                                      Gap(8),
                                      Divider(),
                                      Gap(8),
                                    ],
                                  );
                                },
                                padding: EdgeInsets.zero,
                                controller: scrollPaginationController,
                                shrinkWrap: true,
                                itemBuilder: (context, index) {
                                  if (index == userList.length) {
                                    return Utility.progressIndicator();
                                  }
                                  return ValueListenableBuilder<bool>(
                                    valueListenable: isSwapRequestLoading,
                                    builder: (context, loading, _) {
                                      final users = userList[index];
                                      return SwapAvailabilityUserWidget(
                                        user: users,
                                        projectmodel: widget.userSwapModel?.selectedProject,
                                        selected: users.isRequested,
                                        isLoading: loading && (selectUserId == users.id),
                                        onTap: () {
                                          if (users.isRequested) return;
                                          selectUserId = users.id;
                                          sendSwapRequest(
                                            user: users,
                                            myUserId: state.user.id ?? 0,
                                          );
                                        },
                                      );
                                    },
                                  );
                                },
                                itemCount: userList.length + (isLoadingMore ? 1 : 0),
                              ),
                            ),
                            const Gap(20),
                            CustomeButtonGradiantWidget(
                              buttonText: 'Close',
                              isGradient: true,
                              width: 100,
                              height: 38,
                              onTap: () {
                                context.pop();
                              },
                            ),
                            const Gap(20),
                          ],
                        ),
                    ],
                  );
                },
              );
            }
            return const SizedBox.shrink();
          },
        ),
      ],
    );
  }

  Future<void> load() async {
    isLoading = true;
    _notify();
    hasReachedMax = true;
    final failOrSuccess = await getIt<IChatRepository>().projectSwapAvailableUsers(
      projectId: widget.userSwapModel?.selectedProject?.id.toString() ?? '',
      date: widget.userSwapModel?.selectedDate,
    );
    failOrSuccess.fold(
      (l) {
        isLoading = false;
        _notify();
      },
      (r) {
        isLoading = false;
        hasReachedMax = r.data.length < 20;
        users.value = r.data;
        page = 2;
        _notify();
      },
    );
  }

  Future<void> loadMore() async {
    isLoadingMore = true;
    _notify();

    final failOrSuccess = await getIt<IChatRepository>().projectSwapAvailableUsers(
      projectId: widget.userSwapModel?.selectedProject?.id.toString() ?? '',
      date: widget.userSwapModel?.selectedDate,
      page: page,
    );
    failOrSuccess.fold(
      (l) {
        isLoadingMore = false;
        _notify();
      },
      (r) {
        isLoadingMore = false;
        hasReachedMax = r.data.length < 20;
        page += 1;
        users.value = [...users.value, ...r.data];
        _notify();
      },
    );
  }

  Future<void> sendSwapRequest({required UserModel user, required int myUserId}) async {
    isSwapRequestLoading.value = true;
    final failOrSuccess = await getIt<IChatRepository>().sendSwapRequest(
      currentDate: widget.userSwapModel?.userCurrentDate ?? DateTime.now(),
      swapWithDate: widget.userSwapModel?.selectedDate ?? DateTime.now(),
      userId: user.id.toString(),
      projectId: widget.userSwapModel?.selectedProject?.id.toString() ?? '',
    );
    failOrSuccess.fold(
      (l) {
        isSwapRequestLoading.value = false;
        Utility.toast(message: l.message);
      },
      (r) {
        users.value = users.value.map((e) => e.id == user.id ? e.copyWith(isRequested: true) : e).toList();
        sendMessageRequst(
          myUserId: myUserId,
          otherUserId: user.id ?? 0,
          projectId: widget.userSwapModel?.selectedProject?.id.toString() ?? '',
          message: r.message,
          userRequestId: r.data?.id.toString(),
        );
      },
    );
  }

  Future<void> sendMessageRequst({
    required int myUserId,
    required int otherUserId,
    required String projectId,
    String? message,
    String? userRequestId,
  }) async {
    String useraId;
    String userbId;
    if (myUserId < myUserId) {
      useraId = myUserId.toString();
      userbId = otherUserId.toString();
    } else {
      useraId = otherUserId.toString();
      userbId = myUserId.toString();
    }
    await FireaseChatService.enterDetailUsersChatListCollection(
      chatType: AppConstants.swapRequest,
      useraId: useraId,
      userbId: userbId,
      groupchatid: FireaseChatService.generateUniqueKey(
        userId1: otherUserId,
        userId2: myUserId,
      ),
      currentDate: widget.userSwapModel?.userCurrentDate != null
          ? Timestamp.fromDate(widget.userSwapModel!.userCurrentDate!)
          : null,
      swapDate:
          widget.userSwapModel?.selectedDate != null ? Timestamp.fromDate(widget.userSwapModel!.selectedDate!) : null,
      projectId: projectId,
      userSwapRequestId: userRequestId,
      isSwapRequest: false,
      myid: myUserId.toString(),
    );
    await FireaseChatService.enterChatsData(
      myId: myUserId.toString(),
      otherId: otherUserId.toString(),
      groupChatId: FireaseChatService.generateUniqueKey(
        userId1: otherUserId,
        userId2: myUserId,
      ),
      swapDate:
          widget.userSwapModel?.selectedDate != null ? Timestamp.fromDate(widget.userSwapModel!.selectedDate!) : null,
      currentDate: widget.userSwapModel?.userCurrentDate != null
          ? Timestamp.fromDate(widget.userSwapModel!.userCurrentDate!)
          : null,
      projectId: projectId,
      userSwapRequestId: userRequestId,
      isSwapRequest: false,
      type: AppConstants.swapRequest,
    );
    isSwapRequestLoading.value = false;
    Utility.toast(message: message ?? '');
    if (mounted) context.pop();
    // await sendMessageApi(message: message);
  }

  // Future<void> sendMessageApi({required String message}) async {
  //   final failOrSuccess = await getIt<IChatRepository>().sendMessage(
  //     message: message,
  //     userId: otherUserId.toString() ?? '0',
  //   );
  //   failOrSuccess.fold(
  //     (l) {
  //       Utility.toast(message: l.message);
  //     },
  //     (r) {
  //       messageFocusNode.requestFocus();
  //       setState(() {});
  //     },
  //   );
  // }
}
