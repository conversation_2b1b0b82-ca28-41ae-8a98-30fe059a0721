// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:developer';

import 'package:easy_debounce/easy_debounce.dart';
import 'package:excel_app/constants/app_assets.dart';
import 'package:excel_app/constants/app_colors.dart';
import 'package:excel_app/injector/injector.dart';
import 'package:excel_app/projects/model/project_model.dart';
import 'package:excel_app/projects/repository/i_project_repository.dart';
import 'package:excel_app/utility/helpers/utility.dart';
import 'package:excel_app/utility/pagination_mixin.dart';
import 'package:excel_app/widget/app_text_form_field.dart';
import 'package:excel_app/widget/popup_wrapper.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';

class SelectProjectDialog extends StatefulWidget {
  const SelectProjectDialog({
    super.key,
    this.selectedProject,
  });
  final ProjectModel? selectedProject;

  @override
  State<SelectProjectDialog> createState() => SelectProjectDialogState();
}

class SelectProjectDialogState extends State<SelectProjectDialog> with PaginatisonMixin {
  final searchController = TextEditingController();

  bool isLoading = false;

  bool isLoadingMore = false;

  bool hasReachedMax = false;

  int page = 0;

  List<ProjectModel> projects = [];
  final selectedProject = ValueNotifier<ProjectModel>(const ProjectModel());

  @override
  void initState() {
    super.initState();
    selectedProject.value = widget.selectedProject ?? const ProjectModel();
    initiatePagination();
    load(isFirstLoad: true);
  }

  @override
  void dispose() {
    disposePagination();
    searchController.dispose();

    super.dispose();
  }

  void _refresh() {
    page = 0;
    projects.clear();
    _notify();
    load(isFirstLoad: true);
  }

  @override
  Widget build(BuildContext context) {
    return StatefulBuilder(
      builder: (context, stateUpdate) {
        return PopUpWrapper(
          constraints: const BoxConstraints(maxWidth: 400, maxHeight: 500),
          physics: const NeverScrollableScrollPhysics(),
          children: [
            Text(
              'Select Project',
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                  ),
            ),
            const Gap(20),
            AppTextFormField(
              controller: searchController,
              prefixIcon: AppAssets.searchIcon,
              onChanged: (p0) {
                EasyDebounce.debounce(
                  'search',
                  const Duration(milliseconds: 800),
                  _refresh,
                );
              },
              hintText: 'Search',
            ),
            const Gap(20),
            if (isLoading) ...[
              Utility.progressIndicator(),
              const Gap(20),
            ] else
              Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  SizedBox(
                    height: 290, // Set a fixed height
                    child: ListView.builder(
                      controller: scrollPaginationController,
                      shrinkWrap: true,
                      itemBuilder: (context, index) {
                        if (index == projects.length) {
                          return Utility.progressIndicator();
                        }
                        return ValueListenableBuilder<ProjectModel>(
                          valueListenable: selectedProject,
                          builder: (context, selected, _) {
                            return InkWell(
                              onTap: () {
                                stateUpdate(() {
                                  selectedProject.value = projects[index];
                                });
                                Navigator.pop(context, projects[index]);
                              },
                              child: Container(
                                padding: const EdgeInsets.all(10),
                                margin: const EdgeInsets.symmetric(vertical: 4),
                                decoration: BoxDecoration(
                                  color: selectedProject.value.id == projects[index].id
                                      ? AppColors.primary.withAlpha(20)
                                      : AppColors.transparent,
                                  borderRadius: BorderRadius.circular(8),
                                  border: Border.all(
                                    color: selectedProject.value.id == projects[index].id
                                        ? AppColors.primary
                                        : AppColors.gray,
                                  ),
                                ),
                                child: Text(
                                  projects[index].name ?? '',
                                  style: Theme.of(context).textTheme.bodyMedium,
                                ),
                              ),
                            );
                          },
                        );
                      },
                      itemCount: projects.length + (isLoadingMore ? 1 : 0),
                    ),
                  ),
                ],
              ),
            const Gap(18),
          ],
        );
      },
    );
  }

  void _notify() {
    if (mounted) {
      setState(() {});
    }
  }

  Future<void> load({bool isFirstLoad = false}) async {
    if (isFirstLoad) {
      isLoading = true;
    } else {
      isLoadingMore = true;
    }
    _notify();
    page += 1;
    final failOrSuccess = await getIt<IProjectRepository>().getProject(
      page: page,
      perPage: 10,
      search: searchController.text.trim(),
    );
    failOrSuccess.fold(
      (l) {
        isLoading = false;
        isLoadingMore = false;
        _notify();
      },
      (r) {
        hasReachedMax = r.data.length < 10;
        projects = [...projects, ...r.data];
        isLoading = false;
        isLoadingMore = false;
        _notify();
      },
    );
  }

  @override
  void onReachedLast() {
    log('updated value$isLoadingMore $isLoading $hasReachedMax');
    if (isLoadingMore || isLoading || hasReachedMax) return;

    EasyDebounce.debounce('Select__Project_Pagination', const Duration(milliseconds: 500), load);
  }
}
