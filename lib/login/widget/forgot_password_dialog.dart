import 'package:excel_app/auth/repository/i_auth_repository.dart';
import 'package:excel_app/constants/app_colors.dart';
import 'package:excel_app/injector/injector.dart';
import 'package:excel_app/l10n/l10n_extension.dart';
import 'package:excel_app/utility/helpers/utility.dart';
import 'package:excel_app/widget/app_text_form_field.dart';
import 'package:excel_app/widget/custome_button_gradiant_widget.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';

class ForgotPasswordDialog extends StatefulWidget {
  const ForgotPasswordDialog({super.key});

  @override
  State<ForgotPasswordDialog> createState() => _ForgotPasswordDialogState();
}

class _ForgotPasswordDialogState extends State<ForgotPasswordDialog> {
  TextEditingController emailController = TextEditingController();
  final _formKey = GlobalKey<FormState>();
  final isLoading = ValueNotifier<bool>(false);

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
      child: SizedBox(
        width: 400,
        child: Padding(
          padding: const EdgeInsets.fromLTRB(20, 20, 20, 30),
          child: Form(
            key: _formKey,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'Forgot Password',
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                      ),
                ),
                const Gap(6),
                Text(
                  'Please enter registered email below to\n receive new password.',
                  textAlign: TextAlign.center,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(color: AppColors.subText),
                ),
                const Gap(24),
                AppTextFormField(
                  controller: emailController,
                  title: 'Email',
                  textInputAction: TextInputAction.done,
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return context.l10n.pleaseEnterEmail;
                    } else if (!Utility.isValidEmail(value.trim())) {
                      return context.l10n.pleaseEnterValidEmail;
                    }
                    return null;
                  },
                ),
                const Gap(30),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CustomeButtonGradiantWidget(
                      buttonText: 'Cancel',
                      isUseContainerBorder: true,
                      width: 100,
                      height: 38,
                      onTap: () {
                        Navigator.pop(context);
                      },
                    ),
                    const Gap(15),
                    ValueListenableBuilder(
                      valueListenable: isLoading,
                      builder: (__, loading, _) {
                        return CustomeButtonGradiantWidget(
                          buttonText: 'Submit',
                          isGradient: true,
                          width: 100,
                          height: 38,
                          isLoading: loading,
                          onTap: forgotPassword,
                        );
                      },
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Future<void> forgotPassword() async {
    if (_formKey.currentState!.validate()) {
      isLoading.value = true;
      final response = await getIt<IAuthRepository>().forgotPassword(
        email: emailController.text,
      );
      await response.fold(
        (l) {
          isLoading.value = false;
          Navigator.pop(context);
          Utility.toast(message: l.message);
        },
        (r) async {
          Navigator.pop(context);
          isLoading.value = false;
          Utility.toast(message: r.message);
        },
      );
    }
  }
}
