// ignore_for_file: use_build_context_synchronously

import 'dart:developer';

import 'package:excel_app/app/bloc/authentication_bloc.dart';
import 'package:excel_app/auth/repository/i_auth_repository.dart';
import 'package:excel_app/constants/app_assets.dart';
import 'package:excel_app/constants/app_colors.dart';
import 'package:excel_app/constants/app_constants.dart';
import 'package:excel_app/injector/injector.dart';
import 'package:excel_app/l10n/l10n_extension.dart';
import 'package:excel_app/local_storage/i_local_storage_repository.dart';
import 'package:excel_app/login/widget/forgot_password_dialog.dart';
import 'package:excel_app/utility/extentions/string_extentions.dart';
import 'package:excel_app/utility/helpers/utility.dart';
import 'package:excel_app/widget/app_asset_image.dart';
import 'package:excel_app/widget/app_text_form_field.dart';
import 'package:excel_app/widget/custome_button_gradiant_widget.dart';
import 'package:excel_app/widget/dialogs.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:responsive_builder/responsive_builder.dart';

class LoginPage extends StatefulWidget {
  const LoginPage({super.key});

  @override
  State<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> {
  final emailController = TextEditingController(text: '<EMAIL>'.isDebugging);
  final passwordController = TextEditingController(text: 'password'.isDebugging);
  final passwordVisbility = ValueNotifier<bool>(true);

  final isLoading = ValueNotifier<bool>(false);
  final isGoogleLoading = ValueNotifier<bool>(false);

  final _formKey = GlobalKey<FormState>();

  // String? token;

  @override
  void dispose() {
    isLoading.dispose();
    isGoogleLoading.dispose();
    passwordVisbility.dispose();
    emailController.dispose();
    passwordController.dispose();
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
    // token = FirebaseMessagingService.token;
  }

  @override
  Widget build(BuildContext context) {
    log('${MediaQuery.sizeOf(context).width}width');
    return Scaffold(
      // backgroundColor: AppColors.background,
      body: Stack(
        alignment: getValueForScreenType(
          context: context,
          mobile: Alignment.center,
          tablet: Alignment.center,
          desktop: Alignment.bottomRight,
        ),
        children: [
          Padding(
            padding: EdgeInsets.only(right: (kIsWeb && MediaQuery.sizeOf(context).width > 950) ? 300 : 0),
            child: SizedBox(
              height: MediaQuery.sizeOf(context).height,
              width: MediaQuery.sizeOf(context).width,
              child: Image.asset(
                AppAssets.loginbg,
                fit: BoxFit.cover,
              ),
            ),
          ),
          Container(
            constraints: BoxConstraints(
              maxWidth: getValueForScreenType(context: context, mobile: 450, tablet: 450, desktop: 500),
            ),
            decoration: getValueForScreenType(
              context: context,
              mobile: BoxDecoration(
                color: AppColors.white,
                borderRadius: BorderRadius.circular(12),
              ),
              tablet: BoxDecoration(
                color: AppColors.white,
                borderRadius: BorderRadius.circular(12),
              ),
              desktop: const BoxDecoration(
                color: AppColors.white,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(60),
                  bottomLeft: Radius.circular(60),
                ),
              ),
            ),
            padding: getValueForScreenType(
              context: context,
              mobile: const EdgeInsets.symmetric(vertical: 16, horizontal: 22),
              tablet: const EdgeInsets.symmetric(vertical: 16, horizontal: 22),
              desktop: const EdgeInsets.symmetric(vertical: 16, horizontal: 90),
            ),
            child: Form(
              key: _formKey,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: getValueForScreenType(
                  context: context,
                  mobile: MainAxisSize.min,
                  tablet: MainAxisSize.min,
                  desktop: MainAxisSize.max,
                ),
                children: [
                  Row(
                    children: [
                      const AppAssetImage(
                        AppAssets.logo,
                        height: 42,
                        width: 42,
                      ),
                      const Gap(10),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            AppConstants.projectName,
                            style: Theme.of(context).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.w900),
                          ),
                          Text(
                            'Management',
                            style: Theme.of(context).textTheme.titleMedium,
                          ),
                        ],
                      ),
                    ],
                  ),
                  const Gap(20),
                  Text(
                    context.l10n.login,
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                        ),
                  ),
                  // Text(
                  //   'update token : $token',
                  //   style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  //         fontSize: 18,
                  //         fontWeight: FontWeight.w600,
                  //       ),
                  // ),
                  const Gap(20),
                  AppTextFormField(
                    hintText: context.l10n.email,
                    title: context.l10n.email,
                    textInputAction: TextInputAction.next,
                    controller: emailController,
                    validator: (value) {
                      if (value == null || value.trim().isEmpty || !Utility.isValidEmail(value.trim())) {
                        return context.l10n.pleaseEnterValidEmail;
                      }
                      return null;
                    },
                  ),
                  const Gap(20),
                  ValueListenableBuilder<bool>(
                    valueListenable: passwordVisbility,
                    builder: (__, visible, _) {
                      return AppTextFormField(
                        hintText: context.l10n.password,
                        title: context.l10n.password,
                        controller: passwordController,
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return context.l10n.pleaseEnterPassword;
                          }
                          return null;
                        },
                        onFieldSubmitted: (p0) {
                          login();
                        },
                        obscureText: visible,
                        suffixIcon: IconButton(
                          onPressed: () {
                            passwordVisbility.value = !visible;
                          },
                          icon: AppAssetImage(
                            visible ? AppAssets.eyeCloseIcon : AppAssets.eyeOpenIcon,
                          ),
                        ),
                      );
                    },
                  ),
                  const Gap(20),
                  ValueListenableBuilder<bool>(
                    valueListenable: isLoading,
                    builder: (_, value, __) {
                      return CustomeButtonGradiantWidget(
                        buttonText: context.l10n.login,
                        isLoading: value,
                        isGradient: true,
                        onTap: login,
                      );
                    },
                  ),
                  const Gap(10),
                  InkWell(
                    onTap: () {
                      DailogBox.showBluredBgDailog(context, const ForgotPasswordDialog());
                    },
                    child: Padding(
                      padding: const EdgeInsets.all(10),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            'Forgot Password?',
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(color: AppColors.subText),
                          ),
                        ],
                      ),
                    ),
                  ),
                  const Gap(10),
                  Row(
                    children: [
                      const Expanded(child: Divider()),
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        child: Text(
                          'or continue with',
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(color: AppColors.subText),
                        ),
                      ),
                      const Expanded(child: Divider()),
                    ],
                  ),
                  const Gap(16),
                  ValueListenableBuilder<bool>(
                    valueListenable: isGoogleLoading,
                    builder: (_, value, __) {
                      return InkWell(
                        onTap: value ? null : signInWithGoogle,
                        borderRadius: BorderRadius.circular(10),
                        child: Container(
                          height: 48,
                          width: double.infinity,
                          padding: const EdgeInsets.symmetric(horizontal: 16),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(10),
                            border: Border.all(color: AppColors.strokeColor),
                            boxShadow: [
                              BoxShadow(
                                color: AppColors.black.withOpacity2(0.05),
                                blurRadius: 4,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: value
                              ? Center(child: Utility.progressIndicator(size: 24))
                              : Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Container(
                                      height: 24,
                                      width: 24,
                                      padding: const EdgeInsets.all(2),
                                      child: const AppAssetImage(
                                        AppAssets.googleIcon,
                                      ),
                                    ),
                                    const SizedBox(width: 10),
                                    Text(
                                      'Sign in with Google',
                                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                            fontSize: 16,
                                            fontWeight: FontWeight.w500,
                                          ),
                                    ),
                                  ],
                                ),
                        ),
                      );
                    },
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> login() async {
    if (_formKey.currentState!.validate()) {
      isLoading.value = true;
      final response =
          await getIt<IAuthRepository>().login(email: emailController.text, password: passwordController.text);

      await response.fold(
        (l) {
          isLoading.value = false;
          Utility.toast(message: l.message);
        },
        (r) async {
          if (r.user != null && r.token != null) {
            await getIt<ILocalStorageRepository>().setToken(r.token);
            context.read<AuthenticationBloc>().add(Check(user: r.user));
          }
          Utility.toast(message: r.message);
          isLoading.value = false;
        },
      );
    }
  }

  Future<void> signInWithGoogle() async {
    try {
      isGoogleLoading.value = true;
      final response = await getIt<IAuthRepository>().signInWithGoogle();

      await response.fold(
        (l) {
          // Check if the error is due to user cancellation
          final message = l.message ?? '';
          if (message.toLowerCase().contains('cancel') || message.toLowerCase().contains('cancelled')) {
            log('User cancelled Google Sign-In');
          } else {
            Utility.toast(message: message.isEmpty ? 'Sign-in failed' : message);
          }
        },
        (r) async {
          if (r.user != null && r.token != null) {
            await getIt<ILocalStorageRepository>().setToken(r.token);
            context.read<AuthenticationBloc>().add(Check(user: r.user));
          }
          Utility.toast(message: r.message);
        },
      );
    } on Exception catch (e) {
      log('Error during Google Sign-In: $e');
      Utility.toast(message: 'Sign-in failed. Please try again.');
    } finally {
      // Always reset loading state, regardless of success or failure
      isGoogleLoading.value = false;
    }
  }
}
